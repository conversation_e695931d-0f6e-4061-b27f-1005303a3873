DAG_PREFIX = "data_warehouse"


class ActiveOrdersDAG:
    DAG_ID = f"{DAG_PREFIX}_active_orders"

    class Task:
        ACTIVE_ORDERS_MASKED = "active_orders_masked"


class CalendarMaskedDAG:
    DAG_ID = f"{DAG_PREFIX}_calendar_masked"

    class Task:
        CALENDAR_MASKED = "calendar_masked"
        CALENDAR_RECOVERY_MASKED = "calendar_recovery_masked"
        CALENDAR_SHIPPER_MASKED = "calendar_shipper_masked"
        CALENDAR_SORT_MASKED = "calendar_sort_masked"
        CALENDAR_SPEED_MASKED = "calendar_speed_masked"


class CostCardDAG:
    DAG_ID = f"{DAG_PREFIX}_cost_card"

    class Task:
        COST_CARD_INTERMEDIATE_MASKED = "cost_card_intermediate_masked"
        COST_CARD_EVENTS_ID_MASKED = "cost_card_events_id_masked"
        COST_CARD_EVENTS_PH_MASKED = "cost_card_events_ph_masked"
        COST_CARD_EVENTS_MY_MASKED = "cost_card_events_my_masked"
        COST_CARD_EVENTS_SG_MASKED = "cost_card_events_sg_masked"
        COST_CARD_EVENTS_VN_MASKED = "cost_card_events_vn_masked"
        ORDER_PROFITS_ID_MASKED = "order_profits_id_masked"
        ORDER_PROFITS_MY_MASKED = "order_profits_my_masked"
        ORDER_PROFITS_PH_MASKED = "order_profits_ph_masked"
        ORDER_PROFITS_SG_MASKED = "order_profits_sg_masked"
        ORDER_PROFITS_VN_MASKED = "order_profits_vn_masked"
        PRICED_ORDERS_INTERMEDIATE_MASKED = "priced_orders_intermediate_masked"

class CrossBorderDAG:
    DAG_ID = f"{DAG_PREFIX}_cross_border"

    class Task:
        WMS_PARCELS_ENRICHED_MASKED = "wms_parcels_enriched_masked"
        XB_CORE_COMPONENTS_MASKED = "xb_core_components_masked"
        XB_EVENTS_COMBINED_MASKED = "xb_events_combined_masked"
        XB_EVENTS_ENRICHED_MASKED = "xb_events_enriched_masked"
        XB_EXPORT_JOB_ITEMS_ENRICHED_MASKED = "xb_export_job_items_enriched_masked"
        XB_INVOICE_ALLOCATED_COSTS_MASKED = "xb_invoice_allocated_costs_masked"
        XB_INVOICE_BAGS_MASKED = "xb_invoice_bags_masked"
        XB_INVOICE_PARCELS_MASKED = "xb_invoice_parcels_masked"
        XB_INVOICE_PERIODS_MASKED = "xb_invoice_periods_masked"
        XB_INVOICE_SHIPMENTS_MASKED = "xb_invoice_shipments_masked"
        XB_INVOICES_MASKED = "xb_invoices_masked"
        XB_PARCEL_ORIGIN_DESTINATION_MASKED = "xb_parcel_origin_destination_masked"
        XB_PARCELS_ENRICHED_MASKED = "xb_parcels_enriched_masked"
        XB_PRODUCTS_ENRICHED_MASKED = "xb_products_enriched_masked"
        XB_PARCEL_RECEIVING_TASKS_ENRICHED_MASKED = "xb_parcel_receiving_tasks_enriched_masked"
        XB_SERVICES_ENRICHED_MASKED = "xb_services_enriched_masked"
        XB_SHIPMENT_PARCELS_ENRICHED_MASKED = "xb_shipment_parcels_enriched_masked"
        XB_SHIPPER_RATES_PERIODS_MASKED = "xb_shipper_rates_periods_masked"
        XB_SHIPPER_RATES_RATE_MASKED = "xb_shipper_rates_rate_masked"
        XB_SHIPPER_RATES_RATE_REGIONS_MASKED = "xb_shipper_rates_rate_regions_masked"
        XB_SHIPPER_RATES_REGIONS_MASKED = "xb_shipper_rates_regions_masked"
        XB_SHIPPER_RATES_SHIPPER_PROFILE_MASKED = "xb_shipper_rates_shipper_profile_masked"
        XB_SHIPPER_RATES_WEIGHT_RANGES_MASKED = "xb_shipper_rates_weight_ranges_masked"
        XB_SHIPPERS_ENRICHED_MASKED = "xb_shippers_enriched_masked"
        XB_TP_RATES_TP_RATES_INFO_MASKED = "xb_tp_rates_tp_rates_info_masked"
        XB_TP_RATES_TP_RATES_STATUS_MASKED = "xb_tp_rates_tp_rates_status_masked"
        XB_TP_RATES_TP_RATES_WEIGHT_RANGE_MASKED = "xb_tp_rates_tp_rates_weight_range_masked"
        XB_VENDOR_RATE_CARDS_ENRICHED_MASKED = "xb_vendor_rate_cards_enriched_masked"
        XB_VENDOR_RATE_CARDS_EXPANDED_FEES_MASKED = "xb_vendor_rate_cards_expanded_fees_masked"
        XB_VENDOR_RATE_CARDS_RANGES_FEES_MASKED = "xb_vendor_rate_cards_ranges_fees_masked"


class CNTicketingToolDAG:
    DAG_ID = f"{DAG_PREFIX}_tiktok"

    class Task:
        TIKTOK_LSP_DATA_REPORT_MASKED = "tiktok_lsp_data_report_masked"
        TIKTOK_INTERNAL_TICKET_DATA_MASKED = "tiktok_internal_ticket_data_masked"
        TIKTOK_LSP_DATA_REPORT_DUPLICATE_MASKED = "tiktok_lsp_data_report_duplicate_masked"
        TIKTOK_INTERNAL_TICKET_DATA_DUPLICATE_MASKED = "tiktok_internal_ticket_data_duplicate_masked"
        TIKTOK_TRACKER_DATA_REPORT_DUPLICATE_MASKED = "tiktok_tracker_data_report_duplicate_masked"
        TIKTOK_TRACKER_DATA_REPORT_MASKED = "tiktok_tracker_data_report_masked"


class C2CDAG:
    DAG_ID = f"{DAG_PREFIX}_c2c"

    class Task:
        C2C_CSAT_MASKED = "c2c_csat_masked"


class DataWarehouseExportDAG:
    DAG_ID = "misc_dwh_to_gcs_export"

    class Task:
        PARTNERSHIP_WEBHOOK_EXPORT_MASKED = "partnership_webhook_export_masked"
        PARTNERSHIP_WEBHOOK_SUMMARY_EXPORT_MASKED = "partnership_webhook_summary_export_masked"


class DPDAG:
    DAG_ID = f"{DAG_PREFIX}_dp"

    class Task:
        DP_OPERATING_HOURS_ENRICHED_MASKED = "dp_operating_hours_enriched_masked"
        DP_PARTNERS_ENRICHED_MASKED = "dp_partners_enriched_masked"
        DP_RESERVATION_EVENTS_ENRICHED_MASKED = "dp_reservation_events_enriched_masked"
        DP_RESERVATIONS_ENRICHED_MASKED = "dp_reservations_enriched_masked"
        DP_SHIPPER_VOL_DAILY_MASKED = "dp_shipper_vol_daily_masked"
        DPS_ENRICHED_MASKED = "dps_enriched_masked"


class EberDAG:
    DAG_ID = f"{DAG_PREFIX}_eber"

    class Task:
        NINJA_REWARDS_MONTHLY_MASKED = "ninja_rewards_monthly_masked"
        NINJA_REWARDS_USERS_MASKED = "ninja_rewards_users_masked"
        NINJA_REWARDS_USERS_BASE_MASKED = "ninja_rewards_users_base_masked"


class EndtoEndDAG:
    DAG_ID = f"{DAG_PREFIX}_end_to_end"

    class Task:
        DIGITAL_LEAD_ENRICHED_MASKED = "digital_lead_enriched_masked"


class FakePhysicalParcelDAG:
    DAG_ID = f"{DAG_PREFIX}_fake_physical_parcel"

    class Task:
        CONSIGNEE_PHONE_NUMBER_ORDER_FEATURES_MASKED = "consignee_phone_number_order_features_masked"
        CONSIGNEE_EMAIL_ORDER_FEATURES_MASKED = "consignee_email_order_features_masked"
        ID_CLAIMS_ENRICHED_MASKED = "id_claims_enriched_masked"
        PARCEL_SCAN_FEATURES_MASKED = "parcel_scan_features_masked"
        LAZADA_FRAUD_PERFORMANCE_MASKED = "lazada_fraud_performance_masked"
        LAZADA_HV_ORDER_FEATURES_MASKED = "lazada_hv_order_features_masked"
        LAZADA_HV_ORDER_FRAUD_PREDICTION_MASKED = "lazada_hv_order_fraud_prediction_masked"
        LAZADA_OPTIMISED_WORKING_REPORT_MASKED = "lazada_optimised_working_report_masked"
        LAZADA_ORDERS_BASE_MASKED = "lazada_orders_base_masked"
        SELLER_ORDER_FEATURES_MASKED = "seller_order_features_masked"


class FleetDAG:
    DAG_ID = f"{DAG_PREFIX}_fleet"

    class Task:
        CISP_COMPLETION_REPORT_MASKED = "cisp_completion_report_masked"
        CISP_COMPLETION_REPORT_DAILY_MASKED = "cisp_completion_report_daily_masked"
        CISP_PRIOR_REPORT_MASKED = "cisp_prior_report_masked"
        CISP_PRIOR_REPORT_DAILY_MASKED = "cisp_prior_report_daily_masked"
        CISP_REPORT_BASE_MASKED = "cisp_report_base_masked"
        CISP_TERMINAL_STATUS_REPORT_MASKED = "cisp_terminal_status_report_masked"
        CISP_TERMINAL_STATUS_REPORT_DAILY_MASKED = "cisp_terminal_status_report_daily_masked"
        COURIER_CLASSIFICATION_PH_MASKED = "courier_classification_ph_masked"
        COURIER_DISCIPLINE_REPORT_MASKED = "courier_discipline_report_masked"
        COURIER_DISCIPLINE_REPORT_TARGETS_MASKED = "courier_discipline_report_targets_masked"
        DRIVER_CIF_MIGRATION_REPORT_MASKED = "driver_cif_migration_report_masked"
        DRIVER_RANKING_ENRICHED_MASKED = "driver_ranking_enriched_masked"
        DRIVER_TYPES_ENRICHED_MASKED = "driver_types_enriched_masked"
        DRIVERS_ENRICHED_MASKED = "drivers_enriched_masked"
        FIRST_MILE_NOTIFICATIONS_ENRICHED_MASKED = 'first_mile_notifications_enriched_masked'
        FIRST_MILE_ROUTING_METRICS_ENRICHED_MASKED = 'first_mile_routing_metrics_enriched_masked'
        FIRST_MILE_VOLUME_ORDERS_MASKED = 'first_mile_volume_orders_masked'
        FM_SHIPPER_EXCLUSIONS_MASKED = "fm_shipper_exclusions_masked"
        FLEET_PERFORMANCE_BASE_DATA_MASKED = "fleet_performance_base_data_masked"
        ID_FIRST_MILE_AUTO_ROUTING_ADDRESSES_MASKED = "id_first_mile_auto_routing_addresses_masked"
        LAST_MILE_DAILY_PUSH_OFF_ADHERENCE_MASKED = "last_mile_daily_push_off_adherence_masked"
        LAST_MILE_PROCESSING_TIME_MASKED = "last_mile_processing_time_masked"
        LAST_MILE_PUSH_OFF_CUTOFFS_MASKED = "last_mile_push_off_cutoffs_masked"
        LAST_MILE_PUSH_OFF_REPORT_MASKED = "last_mile_push_off_report_masked"
        LAST_MILE_TIMESLOT_ADHERENCE_COURIER_REPORT_MASKED = "last_mile_timeslot_adherence_courier_report_masked"
        LAST_MILE_TIMESLOT_ADHERENCE_REPORT_MASKED = "last_mile_timeslot_adherence_report_masked"
        LAST_MILE_TIMESLOT_ADHERENCE_SHIPPER_REPORT_MASKED = "last_mile_timeslot_adherence_shipper_report_masked"
        MILKRUN_ENRICHED_MASKED = "milkrun_enriched_masked"
        MM_DRIVERS_ENRICHED_MASKED = "mm_drivers_enriched_masked"
        N0_PICKUP_SLA_KPI_BASE_MASKED = 'n0_pickup_sla_kpi_base_masked'
        N0_PICKUP_SLA_KPI_MASKED = 'n0_pickup_sla_kpi_masked'
        RESERVATIONS_ENRICHED_MASKED = "reservations_enriched_masked"
        RESERVATION_PICKED_UP_ORDERS_MASKED = 'reservation_picked_up_orders_masked'
        ROBOCALL_TABLE_ENRICHED_MASKED = "robocall_table_enriched_masked"
        ROBOCHAT_MESSAGES_ENRICHED_MASKED = "robochat_messages_enriched_masked"
        ROUTE_CLASSIFICATION_MASKED = "route_classification_masked"
        ROUTE_CLASSIFICATION_PH_MASKED = "route_classification_ph_masked"
        ROUTE_LOGS_ENRICHED_MASKED = "route_logs_enriched_masked"
        RTS_RATES_KPI_MASKED = "rts_rates_kpi_masked"
        POD_VALIDATION_TASKS_ENRICHED_MASKED = "pod_validation_tasks_enriched_masked"
        POH_ORDER_METRICS_MASKED = "poh_order_metrics_masked"
        POH_METRICS_MASKED = "poh_metrics_masked"
        PROOFS_ENRICHED_MASKED = 'proofs_enriched_masked'
        SG_ROUTE_ZONE_SUCCESS_RATE_MASKED = 'sg_route_zone_success_rate_masked'
        WAREHOUSE_SPEED_REPORT_MASKED = "warehouse_speed_report_masked"
        WAYPOINTS_ENRICHED_MASKED = "waypoints_enriched_masked"
        WAYPOINT_PHOTOS_ENRICHED_MASKED = "waypoint_photos_enriched_masked"

class FieldSalesDAG:
    DAG_ID = f"{DAG_PREFIX}_field_sales"

    class Task:
        FIELD_SALES_SHORT_TERM_RETENTION_MASKED = "field_sales_short_term_retention_masked"
        FIELD_SALES_SHORT_TERM_RETENTION_BASE_MASKED = "field_sales_short_term_retention_base_masked"
        MATCHED_CONSIGNEE_CONTACT_EMAIL_MASKED = "matched_consignee_contact_email_masked"
        MATCHED_SHIPPER_CONTACT_EMAIL_MASKED = "matched_shipper_contact_email_masked"


class GamificationDAG:
    DAG_ID = f"{DAG_PREFIX}_gamification"

    class Task:
        GAMIFICATION_ID_PICKUP_EVENTS_MASKED = "gamification_id_pickup_events_masked"
        GAMIFICATION_ID_PLANNED_PARCELS_EVENTS_MASKED = "gamification_id_planned_parcels_events_masked"
        GAMIFICATION_ID_DAILY_REPORT_MASKED = "gamification_id_daily_report_masked"
        GAMIFICATION_ID_MONTHLY_REPORT_BASE_MASKED = "gamification_id_monthly_report_base_masked"
        GAMIFICATION_ID_MONTHLY_REPORT_MASKED = "gamification_id_monthly_report_masked"
        GAMIFICATION_MY_BASE_DATA_MASKED = "gamification_my_base_data_masked"
        GAMIFICATION_MY_DAILY_REPORT_MASKED = "gamification_my_daily_report_masked"
        GAMIFICATION_MY_DAILY_REPORT_WITH_FREE_PARCELS_MASKED = "gamification_my_daily_report_with_free_parcels_masked"
        GAMIFICATION_MY_DAILY_REPORT_WITHOUT_FREE_PARCELS_MASKED = "gamification_my_daily_report_without_free_parcels_masked"
        GAMIFICATION_MY_MONTHLY_REPORT_MASKED = "gamification_my_monthly_report_masked"
        GAMIFICATION_MY_ORDER_EVENTS_PICKUP_SUCCESS_MASKED = "gamification_my_order_events_pickup_success_masked"
        GAMIFICATION_MY_PLANNED_PARCELS_EVENTS_MASKED = "gamification_my_planned_parcels_events_masked"
        GAMIFICATION_MY_RESERVATION_PICKED_UP_ORDERS_MASKED = "gamification_my_reservation_picked_up_orders_masked"
        GAMIFICATION_MY_RESERVATIONS_ENRICHED_MASKED = "gamification_my_reservations_enriched_masked"


class GSheetsDAG:
    DAG_ID = f"{DAG_PREFIX}_gsheets"

    class Task:
        FINANCE_BILLING_FILES_MY_MASKED = "finance_billing_files_my_masked"
        SALES_TARGETS_BU_MASKED = "sales_targets_bu_masked"
        NINJA_MART_PL_ACTUALS_MASKED = "ninja_mart_pl_actuals_masked"
        NINJA_MART_PL_TARGETS_MASKED = "ninja_mart_pl_targets_masked"
        NINJA_MART_SALES_TARGETS_MASKED = "ninja_mart_sales_targets_masked"
        NINJA_MART_SKU_MARGINS_MASKED = "ninja_mart_sku_margins_masked"
        MART_MY_LOVS_MASKED = "mart_my_lovs_masked"
        MART_VN_LOVS_MASKED = "mart_vn_lovs_masked"
        TIKTOK_LLP_ID_CX_MASKED = "tiktok_llp_id_cx_masked"


class HubsDAG:
    DAG_ID = f"{DAG_PREFIX}_hubs"

    class Task:
        DIM_WEIGHT_SCANS_MASKED = "dim_weight_scans_masked"
        DIM_WEIGHT_SCANS_BASE_MASKED = "dim_weight_scans_base_masked"
        HUB_RELATION_SCHEDULES_ENRICHED_MASKED = "hub_relation_schedules_enriched_masked"
        HUB_SWEEP_REPORT_MASKED = "hub_sweep_report_masked"
        HUBS_ENRICHED_MASKED = "hubs_enriched_masked"
        MISSORT_DAILY_REPORT_MASKED = "missort_daily_report_masked"
        MISSORT_KPI_MASKED = "missort_kpi_masked"
        PARCEL_MEASUREMENT_SCAN_ENRICHED_MASKED = "parcel_measurement_scan_enriched_masked"
        SCAN_RESULT_ENRICHED_MASKED = "scan_result_enriched_masked"
        SORT_ATS_COMPLIANCE_BASE_MASKED = "sort_ats_compliance_base_masked"
        SORT_ATS_COMPLIANCE_MASKED = "sort_ats_compliance_masked"


class JiraDAG:
    DAG_ID = f"{DAG_PREFIX}_jira"

    class Task:
        JIRA_ISSUE_EVENTS_MASKED = "jira_issue_events_masked"
        JIRA_ISSUES_ENRICHED_MASKED = "jira_issues_enriched_masked"
        JIRA_SPRINT_ISSUES_MASKED = "jira_sprint_issues_masked"
        JIRA_SPRINTS_MASKED = "jira_sprints_masked"

class JotformDAG:
    DAG_ID = f"{DAG_PREFIX}_jotform"

    class Task:
        ENRICHED_NPS_DATA_MASKED = "enriched_nps_data_masked"

class MiddleMileDAG:
    DAG_ID = f"{DAG_PREFIX}_middle_mile"

    class Task:
        COMBINED_SHIPMENT_COMPLIANCE_KPI_FY24_MASKED = "combined_shipment_compliance_kpi_fy24_masked"
        COMBINED_SHIPMENT_COMPLIANCE_KPI_MASKED = "combined_shipment_compliance_kpi_masked"
        MIDDLE_MILE_TRIP_RELATIONSHIPS_MASKED = "middle_mile_trip_relationships_masked"
        MIDDLE_MILE_DRIVER_APP_ADOPTION_KPI_MASKED = "middle_mile_driver_app_adoption_kpi_masked"
        MOVEMENT_TRIPS_ENRICHED_MASKED = "movement_trips_enriched_masked"
        ON_TIME_SHIPMENT_TRIPS_FY24_MASKED = "on_time_shipment_trips_fy24_masked"
        ON_TIME_SHIPMENT_TRIPS_KPI_MASKED = "on_time_shipment_trips_kpi_masked"
        SHIPMENT_ADHERENCE_FY24_MASKED = "shipment_adherence_fy24_masked"
        SHIPMENT_MOVEMENT_ADHERENCE_KPI_MASKED = "shipment_movement_adherence_kpi_masked"
        SHIPMENT_HUB_MILESTONES_MASKED = "shipment_hub_milestones_masked"
        SHIPMENT_ORDERS_ENRICHED_MASKED = "shipment_orders_enriched_masked"
        SHIPMENT_ORDERS_ENRICHED_BASE_MASKED = "shipment_orders_enriched_base_masked"
        SHIPMENT_PATHS_MASKED = "shipment_paths_masked"
        SHIPMENTS_ENRICHED_MASKED = "shipments_enriched_masked"


class MonitoringDAG:
    DAG_ID = f"{DAG_PREFIX}_monitoring"

    class Task:
        HUBS_COORDINATES_CHANGE_EVENTS_MASKED = "hubs_coordinates_change_events_masked"


class NinjaMartDAG:
    DAG_ID = f"{DAG_PREFIX}_ninja_mart"

    class Task:
        NINJA_MART_MY_LINE_ITEMS_ENRICHED_MASKED = "ninja_mart_my_line_items_enriched_masked"
        NINJA_MART_VN_LINE_ITEMS_ENRICHED_MASKED = "ninja_mart_vn_line_items_enriched_masked"
        NINJA_MART_VN_PRESALES_MASKED = "ninja_mart_vn_presales_masked"
        NINJA_MART_VN_RETENTION_MASKED = "ninja_mart_vn_retention_masked"


class OrderAggregatesDAG:
    DAG_ID = f"{DAG_PREFIX}_order_aggregates"

    class Task:
        COUNTRY_COMPLETION_VOL_DAILY_MASKED = "country_completion_vol_daily_masked"
        HUB_COMPLETION_VOL_DAILY_MASKED = "hub_completion_vol_daily_masked"
        HUB_INBOUND_VOL_DAILY_MASKED = "hub_inbound_vol_daily_masked"
        SELLER_OPERATIONAL_METRICS_MASKED = "seller_operational_metrics_masked"
        SHIPPER_COMPLETION_VOL_DAILY_MASKED = "shipper_completion_vol_daily_masked"
        SHIPPER_COMPLETION_VOL_DAILY_FULL_MASKED = "shipper_completion_vol_daily_full_masked"
        SHIPPER_COMPLETION_VOL_MONTHLY_MASKED = "shipper_completion_vol_monthly_masked"
        SHIPPER_CREATION_VOL_DAILY_MASKED = "shipper_creation_vol_daily_masked"
        SHIPPER_INBOUND_VOL_DAILY_MASKED = "shipper_inbound_vol_daily_masked"
        SHIPPER_INBOUND_VOL_MONTHLY_MASKED = "shipper_inbound_vol_monthly_masked"
        WORKDAY_SORT_PRODUCTIVITY_MASKED = "workday_sort_productivity_masked"
        MATCHED_CONSIGNEE_CONTACT_EMAIL_CM_MASKED = "matched_consignee_contact_email_cm_masked"


class OrderEventsDAG:
    DAG_ID = f"{DAG_PREFIX}_order_events"

    class Task:
        ADD_TO_ROUTE_EVENTS_MASKED = "add_to_route_events_masked"
        ADD_TO_SHIPMENT_EVENTS_MASKED = "add_to_shipment_events_masked"
        ADDED_TO_IMPLANT_MANIFEST_EVENTS_MASKED = "added_to_implant_manifest_events_masked"
        CANCELLED_EVENTS_MASKED = "cancelled_events_masked"
        DELIVERY_FAILURE_EVENTS_MASKED = "delivery_failure_events_masked"
        DELIVERY_TRANSACTION_EVENTS_MASKED = "delivery_transaction_events_masked"
        DP_ORDER_EVENTS_MASKED = "dp_order_events_masked"
        DRIVER_PICKUP_SCAN_EVENTS_MASKED = "driver_pickup_scan_events_masked"
        DRIVER_SCAN_EVENTS_MASKED = "driver_scan_events_masked"
        DRIVER_START_ROUTE_EVENTS_MASKED = "driver_start_route_events_masked"
        FIRST_TERMINAL_STATUS_EVENTS_MASKED = "first_terminal_status_events_masked"
        FORCE_SUCCESS_EVENTS_MASKED = "force_success_events_masked"
        FROM_DRIVER_TO_HUB_EVENTS_MASKED = "from_driver_to_hub_events_masked"
        HUB_INBOUND_EVENTS_MASKED = "hub_inbound_events_masked"
        HUB_JOURNEYS_MASKED = "hub_journeys_masked"
        IMPLANTED_MANIFEST_SCAN_EVENTS_MASKED = "implanted_manifest_scan_events_masked"
        IMPLANTED_MANIFEST_SCAN_EVENTS_ENRICHED_MASKED = "implanted_manifest_scan_events_enriched_masked"
        ON_HOLD_EVENTS_MASKED = "on_hold_events_masked"
        ORDER_DEPARTMENT_MOVEMENTS_MASKED = "order_department_movements_masked"
        ORDER_EVENTS_PICKUP_SUCCESS_MASKED = "order_events_pickup_success_masked"
        ORDER_EVENTS_UPDATE_CONTACT_INFORMATION_MASKED = "order_events_update_contact_information_masked"
        ORDER_HUB_MOVEMENTS_MASKED = "order_hub_movements_masked"
        ORDER_MOVEMENTS_MASKED = "order_movements_masked"
        ORDER_MOVEMENTS_BASE_MASKED = "order_movements_base_masked"
        ORIGINAL_CONSIGNEE_INFORMATION_MASKED = "original_consignee_information_masked"
        PARCEL_SWEEPER_EVENTS_MASKED = "parcel_sweeper_events_masked"
        PICKUP_SCAN_EVENTS_MASKED = "pickup_scan_events_masked"
        PICKUP_TRANSACTION_EVENTS_MASKED = "pickup_transaction_events_masked"
        PULL_OUT_OF_ROUTE_EVENTS_MASKED = "pull_out_of_route_events_masked"
        RESERVATION_CHANGE_OF_ROUTE_EVENTS_MASKED = "reservation_change_of_route_events_masked"
        RESERVATION_REMOVE_FROM_ROUTE_EVENTS_MASKED = "reservation_remove_from_route_events_masked"
        RESERVATION_ROUTED_EVENTS_MASKED = "reservation_routed_events_masked"
        ROUTE_INBOUND_EVENTS_MASKED = "route_inbound_events_masked"
        RTS_TRIGGER_EVENTS_MASKED = "rts_trigger_events_masked"
        THIRD_PARTY_TRANSFER_EVENTS_MASKED = "third_party_transfer_events_masked"
        TICKET_RESOLVED_EVENTS_MASKED = "ticket_resolved_events_masked"
        UPDATE_ADDRESS_EVENTS_MASKED = "update_address_events_masked"
        UPDATE_ADDRESS_VERIFICATION_EVENTS_MASKED = "update_address_verification_events_masked"
        UPDATE_CASH_EVENTS_MASKED = "update_cash_events_masked"
        UPDATE_STATUS_EVENTS_MASKED = "update_status_events_masked"
        UPDATE_TAGS_EVENTS_MASKED = "update_tags_events_masked"
        VALID_SCAN_EVENTS_MASKED = "valid_scan_events_masked"
        WAREHOUSE_SCAN_EVENTS_MASKED = "warehouse_scan_events_masked"
        WAREHOUSE_SPEED_REPORT_EVENTS_MASKED = "warehouse_speed_report_events_masked"

class OpsMonthlyDAG:
    DAG_ID = f"{DAG_PREFIX}_ops_monthly"

    class Task:
        RTS_RATES_EXCLUDED_SHIPPERS_MASKED = "rts_rates_excluded_shippers_masked"

class OrderSLADAG:
    DAG_ID = f"{DAG_PREFIX}_order_sla"

    class Task:
        ID_NETWORK_SLA_REPORT_BASE_MASKED = "id_network_sla_report_base_masked"
        ID_NETWORK_SLA_REPORT_MASKED = "id_network_sla_report_masked"
        ID_TIKTOK_LONGTAIL_REPORTS_MASKED = "id_tiktok_longtail_reports_masked"
        LAZ_SLA_BREACH_REPORT_MASKED = "laz_sla_breach_report_masked"
        LONGTAIL_SLA_HOURS_MASKED = "longtail_sla_hours_masked"
        ORDER_HUB_TIMESTAMPS_FLAT_MASKED = "order_hub_timestamps_flat_masked"
        SERVICE_BREACH_REPORT_MASKED = "service_breach_report_masked"
        SG_SLA_ENRICHED_MASKED = "sg_sla_enriched_masked"
        SHIPPER_CLAIMS_REPORT_MASKED = "shipper_claims_report_masked"
        SHIPPER_SLA_DAYS_MASKED = "shipper_sla_days_masked"
        SLA_EXTENSIONS_MASKED = "sla_extensions_masked"
        SLA_REPORTS_BASE_MASKED = "sla_reports_base_masked"
        TRANSIT_TIME_REPORT_MASKED = "transit_time_report_masked"
        TRANSIT_TIME_SPEED_REPORT_MASKED = "transit_time_speed_report_masked"


class OrderTagsDAG:
    DAG_ID = f"{DAG_PREFIX}_order_tags"

    class Task:
        ORDER_TAGS_ENRICHED_MASKED = "order_tags_enriched_masked"


class OpsWeeklyDAG:
    DAG_ID = f"{DAG_PREFIX}_ops_weekly"

    class Task:
        ACTION_AFTER_TICKET_CLOSURE_BASE_MASKED = "action_after_ticket_closure_base_masked"
        ACTION_AFTER_TICKET_CLOSURE_DAILY_MASKED = "action_after_ticket_closure_daily_masked"
        ACTION_AFTER_TICKET_CLOSURE_REPORT_MASKED = "action_after_ticket_closure_report_masked"
        ID_FIRST_MILE_AUTO_ROUTING_ADDRESSES_WEEKLY_MASKED = "id_first_mile_auto_routing_addresses_weekly_masked"
        ORDER_HUB_HISTORY_MASKED = "order_hub_history_masked"
        ORDER_HUB_HISTORY_LATEST_TRIPS_MASKED = "order_hub_history_latest_trips_masked"
        ORDER_HUB_HISTORY_BASE_MASKED = "order_hub_history_base_masked"
        ORDER_HUB_HISTORY_INTERMEDIATE_MASKED = "order_hub_history_intermediate_masked"


class OrdersDAG:
    DAG_ID = f"{DAG_PREFIX}_orders"

    class Task:
        ADDRESS_VERIFICATION_ACCURACY_REPORT_MASKED = "address_verification_accuracy_report_masked"
        AUTO_ADDRESS_VERIFICATION_ACCURACY_REPORT_MASKED = "auto_address_verification_accuracy_report_masked"
        B2B_BUNDLE_ORDERS_MASKED = "b2b_bundle_orders_masked"
        B2B_RDO_PERFORMANCE_REPORT_MASKED = "b2b_rdo_performance_report_masked"
        EXTERNAL_XDOCK_ORDER_MAPPINGS_ENRICHED_MASKED = "external_xdock_order_mappings_enriched_masked"
        HUB_DRIVING_DISTANCES_MASKED = "hub_driving_distances_masked"
        ID_FM_PAYROLL_DAILY_MASKED = "id_fm_payroll_daily_masked"
        ID_FM_PAYROLL_MONTHLY_MASKED = "id_fm_payroll_monthly_masked"
        ID_LM_PAYROLL_DAILY_ADJUSTED_MASKED = "id_lm_payroll_daily_adjusted_masked"
        ID_LM_PAYROLL_MONTHLY_ADJUSTED_MASKED = "id_lm_payroll_monthly_adjusted_masked"
        ID_LM_PAYROLL_DAILY_TRIAL_MASKED = "id_lm_payroll_daily_trial_masked"
        ID_LM_PAYROLL_MONTHLY_TRIAL_MASKED = "id_lm_payroll_monthly_trial_masked"
        ID_MANUAL_AV_LEADTIME_MASKED = "id_manual_av_leadtime_masked"
        LAZADA_INTERCEPTED_ORDERS_MASKED = 'lazada_intercepted_orders_masked'
        LNK_ORDERS_SHIPPERS_MASKED = "lnk_orders_shippers_masked"
        ORDER_ADDED_TO_IMPLANT_MANIFESTS_MASKED = "order_added_to_implant_manifests_masked"
        ORDER_CANCELLATIONS_MASKED = "order_cancellations_masked"
        ORDER_DELIVERIES_MASKED = "order_deliveries_masked"
        ORDER_DESTINATIONS_MASKED = "order_destinations_masked"
        ORDER_DIMENSIONS_MASKED = "order_dimensions_masked"
        ORDER_DP_MILESTONES_MASKED = "order_dp_milestones_masked"
        ORDER_FORCE_SUCCESSES_MASKED = "order_force_successes_masked"
        ORDER_FOURTH_PARTY_HANDOVERS_MASKED = "order_fourth_party_handovers_masked"
        ORDER_INBOUNDS_MASKED = "order_inbounds_masked"
        ORDERS_JOBS_ASSIGNMENTS_ENRICHED_MASKED = "orders_jobs_assignments_enriched_masked"
        ORDER_MILESTONES_MASKED = "order_milestones_masked"
        ORDER_PICKUPS_MASKED = "order_pickups_masked"
        ORDER_RTS_TRIGGER_LOCATIONS_MASKED = "order_rts_trigger_locations_masked"
        ORDER_RTS_TRIGGERS_MASKED = "order_rts_triggers_masked"
        ORDER_THIRD_PARTY_TRANSFERS_MASKED = "order_third_party_transfers_masked"
        ORDERS_ENRICHED_MASKED = "orders_enriched_masked"
        PRICING_ORDERS_HISTORY_ENRICHED_MASKED = "pricing_orders_history_enriched_masked"
        PRICING_PRICING_DETAILS_MASKED = "pricing_pricing_details_masked"
        PUDO_COLLECT_ENRICHED_MASKED = "pudo_collect_enriched_masked"
        RESERVE_TRACKING_IDS_ENRICHED_MASKED = "reserve_tracking_ids_enriched_masked"
        SHIPPER_REF_ADDRESSES_MASKED = "shipper_ref_addresses_masked"
        SHIPPER_REF_DATA_MASKED = "shipper_ref_data_masked"
        SHIPPER_REF_PLATFORM_INFO_MASKED = "shipper_ref_platform_info_masked"
        SHIPPER_REF_ITEMS_MASKED = "shipper_ref_items_masked"
        SHIPPER_REF_SENDER_RECIPIENT_MASKED = "shipper_ref_sender_recipient_masked"
        SHOPIFY_ORDERS_MASKED = "shopify_orders_masked"
        SORT_COMPLIANCE_MASKED = "sort_compliance_masked"
        UPDATE_CASH_EVENTS_ENRICHED_MASKED = "update_cash_events_enriched_masked"
        XB_OUTBOUND_ORDERS_MASKED = "xb_outbound_orders_masked"



class RecoveryDAG:
    DAG_ID = f"{DAG_PREFIX}_recovery"

    class Task:
        EXCLUSION_REQUESTS_MASKED = "exclusion_requests_masked"
        EXCLUSION_REQUEST_DETAILS_MASKED = "exclusion_request_details_masked"
        LIQUIDATION_PARCELS_MASKED = "liquidation_parcels_masked"
        PETS_TICKETS_ENRICHED_MASKED = "pets_tickets_enriched_masked"
        PETS_TICKETS_ENRICHED_BASE_MASKED = "pets_tickets_enriched_base_masked"
        PETS_TICKETS_RESOLVED_DAILY_MASKED = "pets_tickets_resolved_daily_masked"
        RTS_SHIPPER_CONFIRMATION_ENRICHED_MASKED = "rts_shipper_confirmation_enriched_masked"
        TICKET_CLOSURE_PERFORMANCE_MASKED = "ticket_closure_performance_masked"
        TICKET_CREATION_LAST_SCANS_MASKED = "ticket_creation_last_scans_masked"
        TICKET_CREATION_PERFORMANCE_MASKED = "ticket_creation_performance_masked"


class SalesDAG:
    DAG_ID = f"{DAG_PREFIX}_sales"

    class Task:
        SALESPERSONS_ENRICHED_MASKED = "salespersons_enriched_masked"


class SalesforceDAG:
    DAG_ID = f"{DAG_PREFIX}_salesforce"

    class Task:
        INVOICE_DISPUTE_CASE_ENRICHED_MASKED = "invoice_dispute_case_enriched_masked"
        INVOICE_DISPUTE_TID_ENRICHED_MASKED = "invoice_dispute_tid_enriched_masked"
        NINJA_BUDDIES_BUDDY_PAYOUT_MASKED = "ninja_buddies_buddy_payout_masked"
        NINJA_BUDDIES_FRIEND_PAYOUT_MASKED = "ninja_buddies_friend_payout_masked"
        NINJA_BUDDIES_LEADS_MASKED = "ninja_buddies_leads_masked"
        SALESFORCE_ACCOUNT_ENRICHED_MASKED = "salesforce_account_enriched_masked"
        SALESFORCE_ACTIVITY_ENRICHED_MASKED = "salesforce_activity_enriched_masked"
        SALESFORCE_CLAIM_ENRICHED_MASKED = "salesforce_claim_enriched_masked"
        SALESFORCE_CLAIM_ENRICHED_BASE_MASKED = "salesforce_claim_enriched_base_masked"
        SALESFORCE_CONTACT_ENRICHED_MASKED = "salesforce_contact_enriched_masked"
        SALESFORCE_CONTENT_VERSION_ENRICHED_MASKED = "salesforce_content_version_enriched_masked"
        SALESFORCE_CS_CASE_ENRICHED_MASKED = "salesforce_cs_case_enriched_masked"
        SALESFORCE_FLEET_TASK_ENRICHED_MASKED = "salesforce_fleet_task_enriched_masked"
        SALESFORCE_LEAD_ENRICHED_MASKED = "salesforce_lead_enriched_masked"
        SALESFORCE_OPPORTUNITY_ENRICHED_MASKED = "salesforce_opportunity_enriched_masked"
        SALESFORCE_RECORD_TYPE_ENRICHED_MASKED = "salesforce_record_type_enriched_masked"
        SALESFORCE_SS_CASE_ENRICHED_MASKED = "salesforce_ss_case_enriched_masked"
        SALESFORCE_USER_ENRICHED_MASKED = "salesforce_user_enriched_masked"


class SalesforceExportDAG:
    DAG_ID = f"{DAG_PREFIX}_salesforce_export"

    class Task:
        FS_SEGMENTATION_DAILY_MASKED = "fs_segmentation_daily_masked"
        SALESFORCE_SHIPPER_EXPORT_MASKED = "salesforce_shipper_export_masked"
        SFMC_SHIPPERS_EXPORT_MASKED = "sfmc_shippers_export_masked"
        SHIPPER_PICKUP_ASSIGNEES_MASKED = "shipper_pickup_assignees_masked"
        SHIPPER_SEGMENTATION_DAILY_MASKED = "shipper_segmentation_daily_masked"
        SS_SEGMENTATION_DAILY_MASKED = "ss_segmentation_daily_masked"


class ShipperLifetimeValueDAG:
    DAG_ID = f"{DAG_PREFIX}_shipper_lifetime_value"

    class Task:
        PARENT_SHIPPER_LIFETIME_VALUES_MASKED = "parent_shipper_lifetime_values_masked"
        SHIPPER_LIFETIME_VALUES_MASKED = "shipper_lifetime_values_masked"


class ShippersDAG:
    DAG_ID = f"{DAG_PREFIX}_shippers"

    class Task:
        FIELD_AND_CORP_SALES_SHIPPER_EXPORT_MASKED = "field_and_corp_sales_shipper_export_masked"
        PARENT_SHIPPER_LIFETIME_VALUES_BASE_MASKED = "parent_shipper_lifetime_values_base_masked"
        SHIPPER_ATTRIBUTES_MASKED = "shipper_attributes_masked"
        SHIPPER_LIFETIME_VALUES_BASE_MASKED = "shipper_lifetime_values_base_masked"
        SHIPPER_MILESTONES_MASKED = "shipper_milestones_masked"
        SHIPPERS_ENRICHED_MASKED = "shippers_enriched_masked"


class SLABreachDAG:
    DAG_ID = f"{DAG_PREFIX}_sla_breach"

    class Task:
        CONSIGNEE_EMAIL_DOMAIN_REPORT_MASKED = "consignee_email_domain_report_masked"
        DWS_SCANS_EVENTS_MASKED = "dws_scans_events_masked"
        DWS_VARIANCE_REPORT_MASKED = "dws_variance_report_masked"
        HIGH_VALUE_LAZMALL_ORDERS_REPORT_MASKED = "high_value_lazmall_orders_report_masked"
        KNOWN_FRAUD_SHIPPER_REPORT_MASKED = "known_fraud_shipper_report_masked"
        LATEST_SHIPMENT_REPORT_MASKED = "latest_shipment_report_masked"
        LAZADA_FRAUD_FLAGS_MASKED = "lazada_fraud_flags_masked"
        LAZADA_ORDERS_MASKED = "lazada_orders_masked"
        LAZADA_ORDERS_ENRICHED_MASKED = "lazada_orders_enriched_masked"
        NAMES_PER_CONSIGNEE_EMAIL_REPORT_MASKED = "names_per_consignee_email_report_masked"
        MITRA_ORDERS_MASKED = "mitra_orders_masked"
        MITRA_TREND_REPORT_MASKED = "mitra_trend_report_masked"
        PRIORITISED_LAZADA_ORDERS_MASKED = "prioritised_lazada_orders_masked"
        SUSPICIOUS_LAZADA_ORDERS_REPORT_MASKED = "suspicious_lazada_orders_report_masked"
        UPDATE_CONSIGNEE_EMAIL_EVENTS_MASKED = "update_consignee_email_events_masked"


class SLABreachPriceMatchingDAG:
    DAG_ID = f"{DAG_PREFIX}_sla_breach_price_matching"

    class Task:
        COD_ORDERS_TO_EXAMINE_MASKED = "cod_orders_to_examine_masked"
        HIGH_COD_PRICE_SCRAPPING_REPORT_MASKED = "high_cod_price_scrapping_report_masked"


class SNSChatDAG:
    DAG_ID = f"{DAG_PREFIX}_sns_chat"

    class Task:
        SNS_SUBSCRIPTION_ENRICHED_MASKED = "sns_subscription_enriched_masked"


class MonthlySnapshotDAG:
    DAG_ID = f"{DAG_PREFIX}_monthly_snapshot"

    class Task:
        COST_CARD_EVENTS_MY_MONTHLY_SNAPSHOT_MASKED = "cost_card_events_my_monthly_snapshot_masked"
        COST_CARD_EVENTS_PH_MONTHLY_SNAPSHOT_MASKED = "cost_card_events_ph_monthly_snapshot_masked"
        COST_CARD_EVENTS_VN_MONTHLY_SNAPSHOT_MASKED = "cost_card_events_vn_monthly_snapshot_masked"


class SupportDAG:
    DAG_ID = f"{DAG_PREFIX}_support"

    class Task:
        DIMENSION_DATES_MASKED = "dimension_dates_masked"
        LM_CLAW_MASTER_MASKED = "lm_claw_master_masked"


class TasksLoggingDAG:
    DAG_ID = f"{DAG_PREFIX}_tasks_logging"

    class Task:
        ETL_TASKS_LOG_FULL_MASKED = "etl_tasks_log_full_masked"


class TasksMappingDAG:
    DAG_ID = f"{DAG_PREFIX}_tasks_mapping"

    class Task:
        ETL_TASKS_MAPPING_MASKED = "etl_tasks_mapping_masked"


class WebhookDAG:
    DAG_ID = f"{DAG_PREFIX}_webhook"

    class Task:
        PARTNERSHIP_PREFIX_MASKED = "partnership_prefix_masked"


class WebhookSnapshotDAG:
    DAG_ID = f"{DAG_PREFIX}_webhook_snapshot"

    class Task:
        MMCC_WEBHOOK_FAILED_TRANSMISSION_MASKED = "mmcc_webhook_failed_transmission_masked"
        MMCC_WEBHOOK_LATENCY_BREACH_MASKED = "mmcc_webhook_latency_breach_masked"
        MMCC_WEBHOOK_MASKED = "mmcc_webhook_masked"
        MMCC_WEBHOOK_ORDERS_MASKED = "mmcc_webhook_orders_masked"
        MMCC_WEBHOOK_ORDERS_ENRICHED_MASKED = "mmcc_webhook_orders_enriched_masked"
        PARTNERSHIP_WEBHOOK_ORDERS_MASKED = "partnership_webhook_orders_masked"
        PARTNERSHIP_WEBHOOK_ORDERS_ENRICHED_MASKED = "partnership_webhook_orders_enriched_masked"
        PARTNERSHIP_WEBHOOK_FAILED_TRANSMISSION_MASKED = "partnership_webhook_failed_transmission_masked"
        PARTNERSHIP_WEBHOOK_LATENCY_BREACH_MASKED = "partnership_webhook_latency_breach_masked"
        PARTNERSHIP_WEBHOOK_STATS_ENRICHED_MASKED = "partnership_webhook_stats_enriched_masked"
        PARTNERSHIP_WEBHOOK_SUMMARY_STATS_MASKED = "partnership_webhook_summary_stats_masked"
        PARTNERSHIP_WEBHOOK_INTER_ORDERS_ENRICHED_MASKED = "partnership_webhook_inter_orders_enriched_masked"
        PARTNERSHIP_WEBHOOK_INTER_FAILED_TRANSMISSION_MASKED = "partnership_webhook_inter_failed_transmission_masked"
        PARTNERSHIP_WEBHOOK_INTER_LATENCY_BREACH_MASKED = "partnership_webhook_inter_latency_breach_masked"


class WebhookFreqSnapshotDAG:
    DAG_ID = f"{DAG_PREFIX}_webhook_freq_snapshot"

    class Task:
        PARTNERSHIP_WEBHOOK_MASKED = "partnership_webhook_masked"
        PARTNERSHIP_WEBHOOK_LATENCY_REPORT_MASKED = "partnership_webhook_latency_report_masked"
        PARTNERSHIP_WEBHOOK_LATENCY_DETAILS_MASKED = "partnership_webhook_latency_details_masked"


class WebhookExportDAG:
    DAG_ID = f"partnership_webhook_export"

    class Task:
        PARTNERSHIP_WEBHOOK_LATENCY_EXPORT_MASKED = "partnership_webhook_latency_export_masked"


class ZendeskDAG:
    DAG_ID = f"{DAG_PREFIX}_zendesk"

    class Task:
        ZENDESK_TICKETS_ENRICHED_MASKED = "zendesk_tickets_enriched_masked"