EBER_CONFIG = {
    "member_transactions": {
        "system_ids": ("id", "my", "ph", "sg", "vn", "th"),
        "path": "v3/public/business/{business_id}/member_transaction/list",
        "schema": {
            "auth_user_id": "Int64",
            "base_type": "object",
            "channel": "object",
            "created_at": "datetime64",
            "friendly_created_at": "datetime64",
            "friendly_from_expiry_date": "datetime64",
            "friendly_to_expiry_date": "datetime64",
            "from_expiry_date": "datetime64",
            "from_member_tier": "object",
            "from_member_tier_id": "Int64",
            "id": "Int64",
            "member_tier_auto_upgrade": "object",
            "member_tier_auto_upgrade_id": "Int64",
            "member_type": "object",
            "note": "object",
            "order": "object",
            "order_id": "Int64",
            "payment_id": "Int64",
            "source_id": "Int64",
            "source_type": "object",
            "staff": "object",
            "store": "object",
            "store_id": "Int64",
            "store_staff": "object",
            "store_staff_id": "Int64",
            "to_expiry_date": "datetime64",
            "to_member_tier": "object",
            "to_member_tier_id": "Int64",
            "transaction_no": "object",
            "type": "object",
            "updated_at": "datetime64",
            "user": "object",
            "user_agent": "object",
            "user_id": "Int64",
            "business_id": "Int64",
        },
        "nested_columns": {
            "from_member_tier",
            "member_tier_auto_upgrade",
            "staff",
            "store",
            "to_member_tier",
            "user",
        },
        "updated_at_field": "created_at",
        "fetch_type": "incremental",
        "incremental_range_fields": ("from_date", "to_date"),
    },
    "point_transactions": {
        "system_ids": ("id", "my", "ph", "sg", "vn", "th"),
        "path": "v3/public/business/{business_id}/point/transaction",
        "schema": {
            "actual_amount": "float64",
            "actual_currency": "object",
            "auth_user_id": "Int64",
            "amount": "float64",
            "business": "object",
            "business_id": "Int64",
            "channel": "object",
            "conversion_rate": "object",
            "coupon_issued": "object",
            "coupon_issued_id": "Int64",
            "created_at": "datetime64",
            "currency": "object",
            "custom_staff_id": "Int64",
            "earning_activity_usage_id": "Int64",
            "ecom_integration_id": "Int64",
            "ecom_transaction_id": "Int64",
            "enable_void": "bool",
            "friendly_created_at": "datetime64",
            "friendly_original_created_at": "datetime64",
            "friendly_transaction_created_at": "datetime64",
            "friendly_updated_at": "datetime64",
            "from_user_id": "Int64",
            "id": "Int64",
            "import_batch_id": "Int64",
            "imported": "bool",
            "issued_object": "object",
            "issued_object_id": "Int64",
            "issued_object_type": "object",
            "member_tier_id": "Int64",
            "member_tier_id_at_transaction": "Int64",
            "note": "object",
            "object_id": "Int64",
            "object_type": "object",
            "original_created_at": "datetime64",
            "parent": "object",
            "parent_point_transaction_id": "Int64",
            "point_rule_id": "Int64",
            "point_type": "object",
            "point_type_id": "Int64",
            "points": "Int64",
            "public_note": "object",
            "public_params": "object",
            "purchase": "object",
            "qr_code_id": "Int64",
            "qualify_actual_amount": "float64",
            "qualify_actual_currency": "object",
            "qualify_amount": "float64",
            "qualify_currency": "object",
            "reason": "object",
            "remark": "object",
            "remark_code": "object",
            "staff": "object",
            "staff_id": "Int64",
            "store": "object",
            "store_connector_id": "Int64",
            "store_id": "Int64",
            "store_staff": "object",
            "store_staff_id": "Int64",
            "sub_type": "object",
            "tags": "object",
            "transaction_email_id": "Int64",
            "transaction_no": "object",
            "type": "object",
            "type_display_name": "object",
            "unique_type": "object",
            "updated_at": "datetime64",
            "user": "object",
            "user_agent": "object",
            "user_id": "Int64",
            "void": "bool",
            "void_amount": "float64",
            "void_note": "object",
            "void_user": "object",
            "void_user_id": "Int64",
            "voided_at": "datetime64",
        },
        "nested_columns": {
            "coupon_issued",
            "issued_object",
            "parent",
            "purchase",
            "staff",
            "store",
            "user",
            "void_user",
        },
        "updated_at_field": "updated_at",
        "fetch_type": "incremental",
        "incremental_range_fields": ("from_updated_at", "to_updated_at"),
    },
    "users": {
        "system_ids": ("id", "my", "ph", "sg", "vn", "th"),
        "path": "v3/public/business/{business_id}/user/list",
        "schema": {
            "access_level": "Int64",
            "address": "object",
            "anniversary_day": "Int64",
            "anniversary_month": "Int64",
            "anniversary_year": "Int64",
            "auth_user_id": "Int64",
            "base_type": "object",
            "birth_day": "Int64",
            "birth_month": "Int64",
            "birth_year": "Int64",
            "block": "object",
            "business_id": "Int64",
            "category_update_job_id": "Int64",
            "channel": "object",
            "channel_name": "object",
            "city": "object",
            "company": "object",
            "country": "object",
            "created_at": "datetime64",
            "currency": "object",
            "deleted_at": "datetime64",
            "dhl_sync": "bool",
            "display_name": "object",
            "email_verified_at": "datetime64[ns]",
            "phone_verified_at": "datetime64[ns]",
            "dnc": "bool",
            "email": "object",
            "email_2": "object",
            "email_2_status": "bool",
            "email_status": "bool",
            "email_verify": "bool",
            "email_verify_2": "bool",
            "enrolled": "bool",
            "enrolled_at": "datetime64",
            "external_member_id": "object",
            "facebook_member_card_attachment_id": "Int64",
            "facebook_qr_code_attachment_id": "Int64",
            "facebook_sender_id": "Int64",
            "first_name": "object",
            "floor": "Int64",
            "force_update_password": "bool",
            "friendly_created_at": "datetime64",
            "friendly_enrolled_at": "datetime64",
            "friendly_last_login": "datetime64",
            "friendly_tnc_agreed_at": "datetime64",
            "friendly_updated_at": "datetime64",
            "full_permissions": "bool",
            "gender": "object",
            "google_cid": "Int64",
            "id": "Int64",
            "import": "bool",
            "industry": "object",
            "language": "object",
            "last_average_days_per_visit": "float64",
            "last_average_spending": "float64",
            "last_category_update_at": "datetime64",
            "last_name": "object",
            "last_spending_prediction": "float64",
            "member": "object",
            "member_tier_id_on_creating": "Int64",
            "member_tier_points_earn": "object",
            "member_tiers": "object",
            "nationality": "object",
            "order_id": "Int64",
            "original_created_at": "datetime64",
            "phone": "object",
            "phone_2": "object",
            "phone_code": "object",
            "phone_code_2": "object",
            "phone_format": "object",
            "phone_format_2": "object",
            "phone_full": "object",
            "phone_full_2": "object",
            "phone_verify": "bool",
            "phone_verify_2": "bool",
            "points": "object",
            "postal_code": "object",
            "refer_url": "object",
            "referral_code": "object",
            "referral_user_id": "Int64",
            "salutation": "object",
            "source_id": "Int64",
            "source_type": "object",
            "state": "object",
            "status": "bool",
            "store_id": "Int64",
            "store_staff_id": "Int64",
            "sub_members": "object",
            "tile_id": "Int64",
            "timezone": "object",
            "tnc2_agreed_at": "datetime64",
            "tnc_agreed_at": "datetime64",
            "transaction_no": "object",
            "type": "object",
            "unit": "object",
            "updated_at": "datetime64",
            "updated_at_extended": "datetime64",
            "updated_by_master": "bool",
            "user_agent": "object",
            "user_category_id": "Int64",
            "user_qr_codes": "object",
            "username": "Int64",
            "utm_campaign": "object",
            "utm_content": "object",
            "utm_medium": "object",
            "utm_source": "object",
            "utm_term": "object",
            "uuid": "object",
            "website": "object",
        },
        "nested_columns": {
            "member",
            "member_tier_points_earn",
            "member_tiers",
            "points",
            "sub_members",
            "user_qr_codes",
        },
        "updated_at_field": "updated_at",
        "fetch_type": "all",
    },
}
