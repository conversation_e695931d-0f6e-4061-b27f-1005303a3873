TABLES = {
    "dev": {
        "3pl_dev_gl": ["bags"],
        "aaa_dev_gl": ["user_info"],
        "consignee_dev_gl": ["consignees"],
    },
    "qa": {"3pl_qa_gl": ["events", "shippers"]},
    "prod": {
        "3pl_oms_prod_gl": ["third_party_requests", "third_party_orders", "third_party_vendors"],
        "3pl_prod_gl": [
            "audit_logs",
            "bags",
            "events",
            "export_job_items",
            "order_requests",
            "parcel_items",
            "parcels",
            "partner_settings",
            "partner_shippers",
            "partners",
            "product_sla_targets",
            "product_vendors",
            "products",
            "queue_messages",
            "service_sla_targets",
            "services",
            "shipment_events",
            "shipment_parcels",
            "shipments",
            "shippers",
            "vendor_orders",
            "vendors",
        ],
        "aaa_prod_gl": ["user_info", "users", "users_groups", "groups", "user_identities", "groups_scopes", "scopes"],
        "address_appraiser_prod_gl": ["predicted_order_coordinates","order_coordinates"],
        "addressing_prod_gl": ["address_events", "zones"],
        "argus_prod_gl": ["error_budgets", "hourly_apdex", "service_uptime"],
        "automation": [
            "cucumber_coverage_reports",
            "cucumber_execution_reports",
            "cucumber_error_rate_reports",
            "management_coverage_reports",
            "management_error_rate_reports",
        ],
        "b2b_prod_gl": ["b2b_addresses", "b2b_carton_events", "b2b_cartons", "b2b_document_events", "b2b_documents", "b2b_on_hold_orders", "b2b_order_events", "b2b_orders"],
        "billing_prod_gl": ["invoiced_orders", "priced_orders"],
        "consignee_prod_gl": ["consignees", "preferences"],
        "control_prod_gl": [
            "pickup_appointment_jobs",
            "pickup_appointment_jobs_orders",
            "pickup_appointment_jobs_pickup_tags",
            "pickup_appointment_jobs_search",
            "pickup_appointment_jobs_search_pickup_tags",
            "pickup_tags",
            "proofs",
            "proof_jobs",
            "proof_transactions",
            "proof_reservations",
            "proof_photos",
            "pudo_pickup_appointment_jobs",
        ],
        "core_prod_id": [
            "blobs",
            "blocked_dates",
            "cod_collections",
            "cod_inbounds",
            "cods",
            "delivery_types",
            "hubs",
            "inbound_scans",
            "industries",
            "job_photos",
            "order_batch_items",
            "order_batches",
            "order_delivery_verifications",
            "order_details",
            "order_jaro_scores_v2",
            "order_pickups",
            "order_sla",
            "order_tag_names",
            "order_tags",
            "orders",
            "outbound_scans",
            "reservation_blob",
            "reservation_failure_reason",
            "reservation_tracking_ids",
            "reservations",
            "route_logs",
            "route_waypoint",
            "salespersons",
            "third_party_shippers",
            "timewindows",
            "transaction_blob",
            "transaction_failure_reason",
            "transactions",
            "warehouse_sweeps",
            "waypoint_photos",
            "waypoints",
        ],
        "core_prod_my": [
            "blobs",
            "blocked_dates",
            "cod_collections",
            "cod_inbounds",
            "cods",
            "delivery_types",
            "hubs",
            "inbound_scans",
            "industries",
            "job_photos",
            "order_delivery_verifications",
            "order_details",
            "order_jaro_scores_v2",
            "order_pickups",
            "order_sla",
            "order_tag_names",
            "order_tags",
            "orders",
            "outbound_scans",
            "reservation_blob",
            "reservation_failure_reason",
            "reservation_tracking_ids",
            "reservations",
            "route_logs",
            "route_waypoint",
            "salespersons",
            "third_party_shippers",
            "timewindows",
            "transaction_blob",
            "transaction_failure_reason",
            "transactions",
            "warehouse_sweeps",
            "waypoint_photos",
            "waypoints",
        ],
        "core_prod_ph": [
            "blobs",
            "blocked_dates",
            "cod_collections",
            "cod_inbounds",
            "cods",
            "delivery_types",
            "hubs",
            "inbound_scans",
            "industries",
            "job_photos",
            "order_batch_items",
            "order_delivery_verifications",
            "order_details",
            "order_jaro_scores_v2",
            "order_pickups",
            "order_sla",
            "order_tag_names",
            "order_tags",
            "orders",
            "outbound_scans",
            "reservation_blob",
            "reservation_failure_reason",
            "reservation_tracking_ids",
            "reservations",
            "route_logs",
            "route_waypoint",
            "salespersons",
            "third_party_shippers",
            "timewindows",
            "transaction_blob",
            "transaction_failure_reason",
            "transactions",
            "warehouse_sweeps",
            "waypoint_photos",
            "waypoints",
        ],
        "core_prod_sg": [
            "blobs",
            "blocked_dates",
            "cod_collections",
            "cod_inbounds",
            "cods",
            "delivery_types",
            "hubs",
            "inbound_scans",
            "industries",
            "order_details",
            "order_jaro_scores_v2",
            "order_pickups",
            "order_sla",
            "order_tag_names",
            "order_tags",
            "orders",
            "outbound_scans",
            "reservation_blob",
            "reservation_failure_reason",
            "reservation_tracking_ids",
            "reservations",
            "route_logs",
            "route_waypoint",
            "salespersons",
            "third_party_shippers",
            "timewindows",
            "transaction_blob",
            "transaction_failure_reason",
            "transactions",
            "warehouse_sweeps",
            "waypoint_photos",
            "waypoints",
        ],
        "core_prod_th": [
            "blobs",
            "blocked_dates",
            "cod_collections",
            "cod_inbounds",
            "cods",
            "delivery_types",
            "hubs",
            "inbound_scans",
            "industries",
            "order_delivery_verifications",
            "order_details",
            "order_jaro_scores_v2",
            "order_pickups",
            "order_sla",
            "order_tag_names",
            "order_tags",
            "orders",
            "outbound_scans",
            "reservation_blob",
            "reservation_failure_reason",
            "reservation_tracking_ids",
            "reservations",
            "route_logs",
            "route_waypoint",
            "salespersons",
            "third_party_shippers",
            "timewindows",
            "transaction_blob",
            "transaction_failure_reason",
            "transactions",
            "warehouse_sweeps",
            "waypoint_photos",
            "waypoints",
        ],
        "core_prod_vn": [
            "blobs",
            "blocked_dates",
            "cod_collections",
            "cod_inbounds",
            "cods",
            "delivery_types",
            "hubs",
            "inbound_scans",
            "industries",
            "job_photos",
            "order_delivery_verifications",
            "order_details",
            "order_jaro_scores_v2",
            "order_pickups",
            "order_sla",
            "order_tag_names",
            "order_tags",
            "orders",
            "outbound_scans",
            "reservation_blob",
            "reservation_failure_reason",
            "reservation_tracking_ids",
            "reservations",
            "route_logs",
            "route_waypoint",
            "salespersons",
            "third_party_shippers",
            "timewindows",
            "transaction_blob",
            "transaction_failure_reason",
            "transactions",
            "warehouse_sweeps",
            "waypoint_photos",
            "waypoints",
        ],
        "core_prod_mm": [
            "blobs",
            "cods",
            "delivery_types",
            "hubs",
            "inbound_scans",
            "industries",
            "order_pickups",
            "order_sla",
            "orders",
            "reservation_blob",
            "reservations",
            "route_logs",
            "route_waypoint",
            "salespersons",
            "transaction_failure_reason",
            "transactions",
            "warehouse_sweeps",
            "waypoints",
        ],
        "direct_prod_gl": [
            "ar_invoices",
            "ar_payments",
            "bank_information",
            "box_items",
            "box_status_histories",
            "boxes",
            "cod_rates",
            "crossborder_providers",
            "crossborder_provider_rates",
            "customers",
            "event_histories",
            "fx_rates",
            "gmv_thresholds",
            "invoice_histories",
            "levy_rates",
            "lfi_histories",
            "lfi_history_details",
            "lm_tracking_histories",
            "origin_request_order_items",
            "origin_request_orders",
            "parcel_statuses",
            "payment_methods",
            "purchase_orders",
            "request_order_items",
            "request_orders",
            "reservations",
            "service_fees",
            "shippers",
            "staffs",
            "supplier_addresses",
            "suppliers",
            "tax_rates",
            "vas_fees",
            "warehouses",
            "waypoints",
            "xb_fee_histories",
        ],
        "dp_prod_gl": [
            "dp_job_orders",
            "dp_jobs",
            "dp_operating_hours",
            "dp_reservation_events",
            "dp_reservations",
            "dps",
            "lodge_in_order",
            "receipts",
        ],
        "driver_prod_gl": [
            "driver_contacts",
            "driver_devices",
            "driver_vehicles",
            "driver_zone_preferences",
            "drivers",
            "failure_reasons",
            "payment_requests",
            "vehicles",
            "waypoint_sequences",
            "implant_manifest_scans",
            "implant_manifests",
            "hub_handovers",
        ],
        "driver_vantage_prod_gl": [
            "penalty_v2",
            "suspension_v2"
        ],
        "epi_prod_gl": ["external_linked_accounts", "external_linked_orders", "external_orders"],
        "fdm_prod_gl": ["shipper_buckets"],
        "first_mile_prod_gl": ["shipper_first_mile_addresses"],
        "events_prod_gl": ["order_events", "route_events", "pickup_events"],
        "goals_prod_gl": [
            "compound_goal_kpi_part_region_group",
            "compound_goals",
            "goal_requests",
            "goal_transactions",
            "kpi_categories",
            "kpi_part_region_group_data",
            "kpi_part_region_groups",
            "kpi_parts",
            "kpi_regions",
            "kpi_requests",
            "kpis",
            "library_transactions",
            "planning_dates",
            "proposed_goals",
            "subscriptions",
            "targets",
            "user_compound_goal",
            "user_goal_stage",
            "user_non_compound_goal",
            "users",
        ],
        "hub_prod_gl": [
            "airports",
            "awb_events",
            "awb_offload_detail",
            "awb_shipment_scans",
            "hub_crossdock_details",
            "hub_relations",
            "flight_info",
            "flight_mawb",
            "landhaul_vendors",
            "mm_driver_details",
            "movement_events",
            "movement_schedule_drivers",
            "movement_trip_drivers",
            "movement_trip_events",
            "movement_trips",
            "safety_check_items",
            "scans",
            "sea_vendors",
            "seaway_bills",
            "shipment_dimensions",
            "shipment_events",
            "shipment_ext_awbs",
            "shipment_orders",
            "shipment_paths",
            "shipment_seaway_bills",
            "shipment_trips",
            "shipments",
            "trip_safety_check_details",
            "trip_safety_checks",
            "trip_cancellation_reasons",
            "trip_shipment_scans",
            "trip_unscanned_shipments",
            "truck_types",
            "truck_utilizations",
            "users",
            "vendors",
        ],
        "loyalty_prod_gl": ["earnings", "mart_instant_redemptions", "merchants", "orders"],
        "hub_usrs_prod_gl": ["hub_users", "hub_user_assignments_v2", "sort_app_user_additional_hubs", "sort_app_users"],
        "mart_promotion_prod_gl": ["deal_versions", "transactions", "transaction_deals", "deals"],
        "mart_sp_mgmt_prod_gl": [
            "accounts",
            "brand",
            "category",
            "commissions",
            "customers",
            "inventories",
            "inventory_events",
            "inventory_forecasts",
            "manufactures",
            "mapping_uom_systems",
            "object_audits",
            "order_inventories",
            "order_reconciles",
            "orders",
            "payment_methods",
            "purchase_order_inventories",
            "purchase_orders",
            "salepersons",
            "shipping_orders",
            "shipping_requests",
            "sku",
            "sku_configuration",
            "sku_point_amast_sync_history",
            "sku_point_files",
            "sku_point_formula",
            "sku_point_histories",
            "sku_points",
            "sku_selling_price",
            "sku_tiers",
            "suppliers",
            "warehouses",
            "wms_purchase_orders"
        ],
        "mart_ecommerce_prod_gl": [
            "coin_transactions",
            "coin_earning_activities",
            "configurations",
            "delivery_order_items",
            "delivery_orders",
            "discount_configs",
            "loyalty_earning_redemptions",
            "loyalty_earnings",
            "loyalty_earnings_v2",
            "loyalty_program_versions",
            "loyalty_programs",
            "merchants",
            "mission_program_images",
            "orders",
            "popup_configs",
            "product_configs",
            "product_inventories",
            "products",
            "sku_pricings",
            "skus",
            "stw_configs",
            "stw_prizes",
            "stw_spin_history",
            "stw_ticket_logs",
            "vouchers"
        ],
        "movement_trip_prod_gl": [
            "vehicles",
            "flight_info",
            "hub_crossdock_details",
            "hub_relation_schedules",
            "hub_relations",
            "mm_driver_details",
            "movement_path_events",
            "movement_paths",
            "movement_schedule_drivers",
            "movement_trip_drivers",
            "movement_trip_events",
            "movement_trips",
            "path_finding_operations",
            "path_finding_results",
            "path_finding_spanning_nodes",
            "safety_check_items",
            "trip_cancellation_reasons",
            "trip_photos",
            "trip_safety_check_details",
            "trip_safety_checks",
            "truck_types",
            "truck_utilizations",
            "vehicle_registrations",
            "vehicle_registration_photos",
        ],
        "notifications_prod_gl": ["notification_whitelists", "sms_outbound_messages", "template_variants", "templates"],
        "notifications_email_prod_gl": ["email_audits"],
        "notifications_sms_prod_gl": ["sms_audits", "sms_outbound_messages"],
        "notifications_v2_prod_gl": [
            "audits",
            "callout_policies",
            "consignee_subscriptions",
            "notification_events",
            "notifications_routing_logs",
            "scheduled_callouts",
            "shipper_subscriptions",
            "template_variants",
            "templates",
        ],
        "notifications_voice_prod_gl": [
            "callout_jobs",
            "callout_requests",
            "vonage_event_webhook_logs",
        ],
        "ocreate_prod_gl": [
            "generated_tracking_ids",
            "kafka_publish_errors",
            "ninja_pack_pre_generated_sequences",
            "ninja_pack_tracking_ids",
            "order_create_dlq_errors",
            "reserve_tracking_ids",
        ],
        "overwatch_prod_gl": ["live_trackers"],
        "pod_validation_prod_gl": ["assignments", "invalid_pod_reasons", "parcels", "photos", "tasks"],
        "pricing_prod_gl": ["pricing_evt_dlq", "pricing_orders", "pricing_orders_history"],
        "reports_prod_gl": ["chunk_audit_logs", "report_audit_logs", "report_subscriptions"],
        "route_prod_gl": [
            "route_logs",
            "route_tags",
            "sr_area_variations_v2",
            "sr_coverages",
            "sr_keywords",
            "tags",
            "waypoints",
            "waypoint_photos",
        ],
        "sda_prod_gl": ["hatica"],
        "script_engine_prod_gl": [
            "pricer_rules",
            "pricer_rate_card_entries",
            "pricing_levers",
            "pricing_profiles",
            "shipper_discounts",
        ],
        "shipper_prod_gl": [
            "marketplace_sellers",
            "shipper_addresses",
            "shipper_metadata",
            "shipper_prefixes",
            "shipper_settings",
            "shippers",
        ],
        "sns_prod_gl": [
            "chat_tickets",
            "chatbot_interactions",
            "chatbot_ratings",
            "chatbot_session_tags",
            "chatbot_sessions",
            "consignee_preferences",
            "cs_feedback_dsat_reasons",
            "cs_feedback_ratings",
            "epr_events",
            "external_platform_references",
            "fallback_queries",
            "identities",
            "invite_codes",
            "lead_gen_contacts",
            "lead_gen_responses",
            "lead_gen_sessions",
            "live_chat_events",
            "live_chat_operating_hours",
            "mattermost_push_receipts",
            "message_logs",
            "messages",
            "pending_chatbot_messages",
            "platform_errors",
            "public_holidays",
            "reservation_requests",
            "shipper_kam_mapping",
            "sns_audits",
            "special_instructions_logs",
            "unsubscribe_reasons",
            "webhook_logs",
        ],
        "sns_platforms_prod_gl": ["message_logs"],
        "sort_prod_gl": [
            "parcel_images",
            "scan_result",
            "sort_mistakes",
            "sorting_maps",
            "sorting_nodes",
            "sorting_paths",
        ],
        "sort_mistake_prod_gl": [
            "prev_scan_ref",
            "scan_results",
            "sort_maps",
            "sort_nodes",
            "sort_nodes_compat_map",
            "sort_paths",
        ],
        "sort_vendor_prod_gl": [
            "feature_switch",
            "inbound_parcel_images",
            "parcel_images",
            "parcel_measurement",
            "parcel_measurement_scan",
            "parcel_images_v2",
            "sort_belt_arm_logics",
            "sort_belt_devices",
            "sort_belt_presets",
            "vendors",
            "vendors_device_access",
            "vendors_system_access",
        ],
        "ticketing_prod_gl": ["ticket_comments", "ticket_logs", "ticket_photos"],
        "url_shortener_gl": ["link_opens", "urls", "urls_v2"],
        "webhook_receiver_prod_gl": ["webhook_requests"],
        "wms_prod_gl": ["bags", "bins", "events", "parcels", "sessions", "requests_parcels", "requests"],
        "xb_operations_prod_gl": [
            "bags",
            "b2b_shipment_fpl_mapping_tab",
            "b2b_shipment_milestones_tab",
            "b2b_shipments",
            "container_milestones_tab",
            "containers_tab",
            "exchange_rates",
            "export_job_items",
            "export_jobs",
            "inbound_job_bags",
            "inbound_job_parcel_items",
            "inbound_job_parcels",
            "inbound_jobs",
            "milestones_tab",
            "parcels",
            "products",
            "receiving_job_manifests",
            "receiving_jobs",
            "receiving_task_items",
            "receiving_tasks",
            "transits_bags_tab",
            "transits_tab",
            "warehouses_tab",
        ],
        "ninjamart_order": [
            "user_order",
            "user_order_item",
            "user_order_item_report",
        ],
        "ninjamart_report": [
            "inventory_activity_log",
            "user_order_status_latest",
            "user_order_status_log",
        ],
        "ninjamart_warehouse_service": [
            'brand',
            'category',
            'country',
            'district',
            'inventory',
            'kafka_event',
            'province',
            'purchase_order',
            'sku',
            'sku_configuration',
            'sku_selling_price',
            'source',
            'ward',
            'warehouse',
        ],
        "developer_support_automation_prod_gl": [
            "audits",
            "audit_info",
            "audit_scenarios",
            "audit_answers",
            "audit_dlq_errors",
            "integrations",
            "integration_details",
            "integration_prefixes",
            "integration_shipper_settings",
            "scenarios",
            "questions",
            "shipper_info",
            "case_info",
            "api_info",
        ],
    },
}
