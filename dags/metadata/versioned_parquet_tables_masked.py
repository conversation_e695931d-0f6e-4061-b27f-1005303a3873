from metadata.constants import BI_SENSITIVE_REPORTS_BASE_URI, DATAWAREHOUSE_BASE_URI, MASKED_DATA_WAREHOUSE_BASE_URI

class DataWarehouse:
    def __init__(self, env="prod"):
        self._BI_SENSITIVE_BASE = BI_SENSITIVE_REPORTS_BASE_URI.format(env)
        self._URI_BASE = DATAWAREHOUSE_BASE_URI.format(env)
        self._MASKED_URI_BASE = MASKED_DATA_WAREHOUSE_BASE_URI.format(env)
        self.ACTION_AFTER_TICKET_CLOSURE_BASE = f"{self._MASKED_URI_BASE}/action_after_ticket_closure_base"
        self.ACTION_AFTER_TICKET_CLOSURE_DAILY = f"{self._MASKED_URI_BASE}/action_after_ticket_closure_daily"
        self.ACTION_AFTER_TICKET_CLOSURE_REPORT = f"{self._MASKED_URI_BASE}/action_after_ticket_closure_report"
        self.ACTIVE_ORDERS = f"{self._MASKED_URI_BASE}/active_orders"
        self.ADD_TO_ROUTE_EVENTS = f"{self._MASKED_URI_BASE}/add_to_route_events"
        self.ADD_TO_SHIPMENT_EVENTS = f"{self._MASKED_URI_BASE}/add_to_shipment_events"
        self.ADDED_TO_IMPLANT_MANIFEST_EVENTS = f"{self._MASKED_URI_BASE}/added_to_implant_manifest_events"
        self.ADDRESS_VERIFICATION_ACCURACY_REPORT = f"{self._MASKED_URI_BASE}/address_verification_accuracy_report"
        self.AUTO_ADDRESS_VERIFICATION_ACCURACY_REPORT = f"{self._MASKED_URI_BASE}/auto_address_verification_accuracy_report"
        self.B2B_BUNDLE_ORDERS = f"{self._MASKED_URI_BASE}/b2b_bundle_orders"
        self.B2B_RDO_PERFORMANCE_REPORT = f"{self._MASKED_URI_BASE}/b2b_rdo_performance_report"
        self.CANCELLED_EVENTS = f"{self._MASKED_URI_BASE}/cancelled_events"
        self.CISP_COMPLETION_REPORT = f"{self._MASKED_URI_BASE}/cisp_completion_report"
        self.CISP_COMPLETION_REPORT_DAILY = f"{self._MASKED_URI_BASE}/cisp_completion_report_daily"
        self.CISP_PRIOR_REPORT = f"{self._MASKED_URI_BASE}/cisp_prior_report"
        self.CISP_PRIOR_REPORT_DAILY = f"{self._MASKED_URI_BASE}/cisp_prior_report_daily"
        self.CISP_REPORT_BASE = f"{self._MASKED_URI_BASE}/cisp_report_base"
        self.CISP_TERMINAL_STATUS_REPORT = f"{self._MASKED_URI_BASE}/cisp_terminal_status_report"
        self.CISP_TERMINAL_STATUS_REPORT_DAILY = f"{self._MASKED_URI_BASE}/cisp_terminal_status_report_daily"
        self.COD_ORDERS_TO_EXAMINE = f"{self._MASKED_URI_BASE}/cod_orders_to_examine"
        self.COMBINED_SHIPMENT_COMPLIANCE_KPI_FY24 = f"{self._MASKED_URI_BASE}/combined_shipment_compliance_kpi_fy24"
        self.COMBINED_SHIPMENT_COMPLIANCE_KPI = f"{self._MASKED_URI_BASE}/combined_shipment_compliance_kpi"
        self.CONSIGNEE_EMAIL_DOMAIN_REPORT = f"{self._BI_SENSITIVE_BASE}/consignee_email_domain_report"
        self.CONSIGNEE_EMAIL_ORDER_FEATURES = f"{self._BI_SENSITIVE_BASE}/consignee_email_order_features"
        self.CONSIGNEE_PHONE_NUMBER_ORDER_FEATURES = f"{self._BI_SENSITIVE_BASE}/consignee_phone_number_order_features"
        self.COST_CARD_EVENTS_ID = f"{self._BI_SENSITIVE_BASE}/cost_card_events_id"
        self.COST_CARD_EVENTS_MY = f"{self._BI_SENSITIVE_BASE}/cost_card_events_my"
        self.COST_CARD_EVENTS_MY_MONTHLY_SNAPSHOT = f"{self._BI_SENSITIVE_BASE}/cost_card_events_my_monthly_snapshot"
        self.COST_CARD_EVENTS_PH = f"{self._BI_SENSITIVE_BASE}/cost_card_events_ph"
        self.COST_CARD_EVENTS_PH_MONTHLY_SNAPSHOT = f"{self._BI_SENSITIVE_BASE}/cost_card_events_ph_monthly_snapshot"
        self.COST_CARD_EVENTS_SG = f"{self._BI_SENSITIVE_BASE}/cost_card_events_sg"
        self.COST_CARD_EVENTS_SG_MONTHLY_SNAPSHOT = f"{self._BI_SENSITIVE_BASE}/cost_card_events_sg_monthly_snapshot"
        self.COST_CARD_EVENTS_VN = f"{self._BI_SENSITIVE_BASE}/cost_card_events_vn"
        self.COST_CARD_EVENTS_VN_MONTHLY_SNAPSHOT = f"{self._BI_SENSITIVE_BASE}/cost_card_events_vn_monthly_snapshot"
        self.COST_CARD_INTERMEDIATE = f"{self._BI_SENSITIVE_BASE}/cost_card_intermediate"
        self.COUNTRY_COMPLETION_VOL_DAILY = f"{self._MASKED_URI_BASE}/country_completion_vol_daily"
        self.COURIER_CLASSIFICATION_PH = f"{self._MASKED_URI_BASE}/courier_classification_ph"
        self.COURIER_DISCIPLINE_REPORT = f"{self._MASKED_URI_BASE}/courier_discipline_report"
        self.COURIER_DISCIPLINE_REPORT_TARGETS = f"{self._MASKED_URI_BASE}/courier_discipline_report_targets"
        self.C2C_CSAT = f"{self._MASKED_URI_BASE}/c2c_csat"
        self.DELIVERY_FAILURE_EVENTS = f"{self._MASKED_URI_BASE}/delivery_failure_events"
        self.DELIVERY_TRANSACTION_EVENTS = f"{self._MASKED_URI_BASE}/delivery_transaction_events"
        self.DIGITAL_LEAD_ENRICHED = f"{self._MASKED_URI_BASE}/digital_lead_enriched"
        self.DIM_WEIGHT_SCANS = f"{self._MASKED_URI_BASE}/dim_weight_scans"
        self.DIM_WEIGHT_SCANS_BASE = f"{self._MASKED_URI_BASE}/dim_weight_scans_base"
        self.DIMENSION_DATES = f"{self._MASKED_URI_BASE}/dimension_dates"
        self.DP_OPERATING_HOURS_ENRICHED = f"{self._MASKED_URI_BASE}/dp_operating_hours_enriched"
        self.DP_ORDER_EVENTS = f"{self._MASKED_URI_BASE}/dp_order_events"
        self.DP_PARTNERS_ENRICHED = f"{self._MASKED_URI_BASE}/dp_partners_enriched"
        self.DP_RESERVATION_EVENTS_ENRICHED = f"{self._MASKED_URI_BASE}/dp_reservation_events_enriched"
        self.DP_RESERVATIONS_ENRICHED = f"{self._MASKED_URI_BASE}/dp_reservations_enriched"
        self.DP_SHIPPER_VOL_DAILY = f"{self._MASKED_URI_BASE}/dp_shipper_vol_daily"
        self.DPS_ENRICHED = f"{self._MASKED_URI_BASE}/dps_enriched"
        self.DRIVER_RANKING_ENRICHED = f"{self._MASKED_URI_BASE}/driver_ranking_enriched"
        self.DRIVER_PICKUP_SCAN_EVENTS = f"{self._MASKED_URI_BASE}/driver_pickup_scan_events"
        self.DRIVER_SCAN_EVENTS = f"{self._MASKED_URI_BASE}/driver_scan_events"
        self.DRIVER_START_ROUTE_EVENTS = f"{self._MASKED_URI_BASE}/driver_start_route_events"
        self.DRIVER_TYPES_ENRICHED = f"{self._MASKED_URI_BASE}/driver_types_enriched"
        self.DRIVERS_ENRICHED = f"{self._MASKED_URI_BASE}/drivers_enriched"
        self.DWS_SCANS_EVENTS = f"{self._BI_SENSITIVE_BASE}/dws_scans_events"
        self.DWS_VARIANCE_REPORT = f"{self._BI_SENSITIVE_BASE}/dws_variance_report"
        self.ENRICHED_NPS_DATA = f"{self._MASKED_URI_BASE}/enriched_nps_data"
        self.EXCLUSION_REQUESTS = f"{self._MASKED_URI_BASE}/exclusion_requests"
        self.EXCLUSION_REQUEST_DETAILS = f"{self._MASKED_URI_BASE}/exclusion_request_details"
        self.EXTERNAL_XDOCK_ORDER_MAPPINGS_ENRICHED = f"{self._MASKED_URI_BASE}/external_xdock_order_mappings_enriched"
        self.FM_SHIPPER_EXCLUSIONS = f"{self._MASKED_URI_BASE}/fm_shipper_exclusions"
        self.FLEET_PERFORMANCE_BASE_DATA = f"{self._MASKED_URI_BASE}/fleet_performance_base_data"
        self.FIELD_AND_CORP_SALES_SHIPPER_EXPORT = f"{self._MASKED_URI_BASE}/field_and_corp_sales_shipper_export"
        self.FIELD_SALES_SHORT_TERM_RETENTION = f"{self._MASKED_URI_BASE}/field_sales_short_term_retention"
        self.FIELD_SALES_SHORT_TERM_RETENTION_BASE = f"{self._MASKED_URI_BASE}/field_sales_short_term_retention_base"
        self.FINANCE_BILLING_FILES_MY = f"{self._BI_SENSITIVE_BASE}/finance_billing_files_my"
        self.FIRST_MILE_NOTIFICATIONS_ENRICHED = f"{self._MASKED_URI_BASE}/first_mile_notifications_enriched"
        self.FIRST_MILE_ROUTING_METRICS_ENRICHED = f"{self._MASKED_URI_BASE}/first_mile_routing_metrics_enriched"
        self.FIRST_MILE_VOLUME_ORDERS = f"{self._MASKED_URI_BASE}/first_mile_volume_orders"
        self.FIRST_TERMINAL_STATUS_EVENTS = f"{self._MASKED_URI_BASE}/first_terminal_status_events"
        self.FORCE_SUCCESS_EVENTS = f"{self._MASKED_URI_BASE}/force_success_events"
        self.FROM_DRIVER_TO_HUB_EVENTS = f"{self._MASKED_URI_BASE}/from_driver_to_hub_events"
        self.FS_SEGMENTATION_DAILY = f"{self._MASKED_URI_BASE}/fs_segmentation_daily"
        self.GAMIFICATION_ID_PICKUP_EVENTS = f"{self._BI_SENSITIVE_BASE}/gamification_id_pickup_events"
        self.GAMIFICATION_ID_PLANNED_PARCELS_EVENTS = f"{self._BI_SENSITIVE_BASE}/gamification_id_planned_parcels_events"
        self.GAMIFICATION_ID_DAILY_REPORT = f"{self._BI_SENSITIVE_BASE}/gamification_id_daily_report"
        self.GAMIFICATION_ID_MONTHLY_REPORT_BASE = f"{self._BI_SENSITIVE_BASE}/gamification_id_monthly_report_base"
        self.GAMIFICATION_ID_MONTHLY_REPORT = f"{self._BI_SENSITIVE_BASE}/gamification_id_monthly_report"
        self.HIGH_COD_PRICE_SCRAPPING_REPORT = f"{self._MASKED_URI_BASE}/high_cod_price_scrapping_report"
        self.HUB_COMPLETION_VOL_DAILY = f"{self._MASKED_URI_BASE}/hub_completion_vol_daily"
        self.HIGH_VALUE_LAZMALL_ORDERS_REPORT = f"{self._BI_SENSITIVE_BASE}/high_value_lazmall_orders_report"
        self.HUB_INBOUND_EVENTS = f"{self._MASKED_URI_BASE}/hub_inbound_events"
        self.HUB_INBOUND_VOL_DAILY = f"{self._MASKED_URI_BASE}/hub_inbound_vol_daily"
        self.HUB_JOURNEYS = f"{self._MASKED_URI_BASE}/hub_journeys"
        self.HUB_RELATION_SCHEDULES_ENRICHED = f"{self._MASKED_URI_BASE}/hub_relation_schedules_enriched"
        self.HUB_SWEEP_REPORT = f"{self._MASKED_URI_BASE}/hub_sweep_report"
        self.HUBS_COORDINATES_CHANGE_EVENTS = f"{self._MASKED_URI_BASE}/hubs_coordinates_change_events"
        self.HUBS_ENRICHED = f"{self._MASKED_URI_BASE}/hubs_enriched"
        self.ID_CLAIMS_ENRICHED = f"{self._BI_SENSITIVE_BASE}/id_claims_enriched"
        self.ID_FM_PAYROLL_DAILY = f"{self._BI_SENSITIVE_BASE}/id_fm_payroll_daily"
        self.ID_FM_PAYROLL_MONTHLY = f"{self._BI_SENSITIVE_BASE}/id_fm_payroll_monthly"
        self.ID_LM_PAYROLL_DAILY_ADJUSTED = f"{self._BI_SENSITIVE_BASE}/id_lm_payroll_daily_adjusted"
        self.ID_LM_PAYROLL_MONTHLY_ADJUSTED = f"{self._BI_SENSITIVE_BASE}/id_lm_payroll_monthly_adjusted"
        self.ID_LM_PAYROLL_DAILY_TRIAL = f"{self._BI_SENSITIVE_BASE}/id_lm_payroll_daily_trial"
        self.ID_LM_PAYROLL_MONTHLY_TRIAL = f"{self._BI_SENSITIVE_BASE}/id_lm_payroll_monthly_trial"
        self.ID_TIKTOK_LONGTAIL_REPORTS = f"{self._MASKED_URI_BASE}/id_tiktok_longtail_reports"
        self.GAMIFICATION_MY_BASE_DATA = f"{self._BI_SENSITIVE_BASE}/gamification_my_base_data"
        self.GAMIFICATION_MY_DAILY_REPORT = f"{self._BI_SENSITIVE_BASE}/gamification_my_daily_report"
        self.GAMIFICATION_MY_DAILY_REPORT_WITH_FREE_PARCELS = f"{self._BI_SENSITIVE_BASE}/gamification_my_daily_report_with_free_parcels"
        self.GAMIFICATION_MY_DAILY_REPORT_WITHOUT_FREE_PARCELS = f"{self._BI_SENSITIVE_BASE}/gamification_my_daily_report_without_free_parcels"
        self.GAMIFICATION_MY_MONTHLY_REPORT = f"{self._BI_SENSITIVE_BASE}/gamification_my_monthly_report"
        self.GAMIFICATION_MY_ORDER_EVENTS_PICKUP_SUCCESS = f"{self._BI_SENSITIVE_BASE}/gamification_my_order_events_pickup_success"
        self.GAMIFICATION_MY_PLANNED_PARCELS_EVENTS = f"{self._BI_SENSITIVE_BASE}/gamification_my_planned_parcels_events"
        self.GAMIFICATION_MY_RESERVATION_PICKED_UP_ORDERS = f"{self._BI_SENSITIVE_BASE}/gamification_my_reservation_picked_up_orders"
        self.GAMIFICATION_MY_RESERVATIONS_ENRICHED = f"{self._BI_SENSITIVE_BASE}/gamification_my_reservations_enriched"
        self.ID_FIRST_MILE_AUTO_ROUTING_ADDRESSES = f"{self._MASKED_URI_BASE}/id_first_mile_auto_routing_addresses"
        self.ID_FIRST_MILE_AUTO_ROUTING_ADDRESSES_WEEKLY = f"{self._MASKED_URI_BASE}/id_first_mile_auto_routing_addresses_weekly"
        self.ID_MANUAL_AV_LEADTIME = f"{self._MASKED_URI_BASE}/id_manual_av_leadtime"
        self.ID_NETWORK_SLA_REPORT = f"{self._MASKED_URI_BASE}/id_network_sla_report"
        self.ID_NETWORK_SLA_REPORT_BASE = f"{self._MASKED_URI_BASE}/id_network_sla_report_base"
        self.INVOICE_DISPUTE_CASE_ENRICHED = f"{self._BI_SENSITIVE_BASE}/invoice_dispute_case_enriched"
        self.INVOICE_DISPUTE_TID_ENRICHED = f"{self._BI_SENSITIVE_BASE}/invoice_dispute_tid_enriched"
        self.IMPLANTED_MANIFEST_SCAN_EVENTS = f"{self._MASKED_URI_BASE}/implanted_manifest_scan_events"
        self.IMPLANTED_MANIFEST_SCAN_EVENTS_ENRICHED = f"{self._MASKED_URI_BASE}/implanted_manifest_scan_events_enriched"
        self.JIRA_ISSUE_EVENTS = f"{self._MASKED_URI_BASE}/jira_issue_events"
        self.JIRA_ISSUES_ENRICHED = f"{self._MASKED_URI_BASE}/jira_issues_enriched"
        self.JIRA_SPRINT_ISSUES = f"{self._MASKED_URI_BASE}/jira_sprint_issues"
        self.JIRA_SPRINTS = f"{self._MASKED_URI_BASE}/jira_sprints"
        self.KNOWN_FRAUD_SHIPPER_REPORT = f"{self._BI_SENSITIVE_BASE}/known_fraud_shipper_report"
        self.LAST_MILE_DAILY_PUSH_OFF_ADHERENCE = f"{self._MASKED_URI_BASE}/last_mile_daily_push_off_adherence"
        self.LAST_MILE_PUSH_OFF_CUTOFFS = f"{self._MASKED_URI_BASE}/last_mile_push_off_cutoffs"
        self.LAST_MILE_PUSH_OFF_REPORT = f"{self._MASKED_URI_BASE}/last_mile_push_off_report"
        self.LAST_MILE_TIMESLOT_ADHERENCE_COURIER_REPORT = (
            f"{self._MASKED_URI_BASE}/last_mile_timeslot_adherence_courier_report"
        )
        self.LAST_MILE_TIMESLOT_ADHERENCE_REPORT = f"{self._MASKED_URI_BASE}/last_mile_timeslot_adherence_report"
        self.LAST_MILE_TIMESLOT_ADHERENCE_SHIPPER_REPORT = (
            f"{self._MASKED_URI_BASE}/last_mile_timeslot_adherence_shipper_report"
        )
        self.LAST_MILE_PROCESSING_TIME = f"{self._MASKED_URI_BASE}/last_mile_processing_time"
        self.LATEST_SHIPMENT_REPORT = f"{self._MASKED_URI_BASE}/latest_shipment_report"
        self.LAZADA_FRAUD_FLAGS = f"{self._BI_SENSITIVE_BASE}/lazada_fraud_flags"
        self.LAZADA_FRAUD_PERFORMANCE = f"{self._BI_SENSITIVE_BASE}/lazada_fraud_performance"
        self.LAZADA_HV_ORDER_FRAUD_PREDICTION = f"{self._BI_SENSITIVE_BASE}/lazada_hv_order_fraud_prediction"
        self.LAZADA_INTERCEPTED_ORDERS = f"{self._BI_SENSITIVE_BASE}/lazada_intercepted_orders"
        self.LAZADA_HV_ORDER_FEATURES = f"{self._BI_SENSITIVE_BASE}/lazada_hv_order_features"
        self.LAZADA_OPTIMISED_WORKING_REPORT = f"{self._BI_SENSITIVE_BASE}/lazada_optimised_working_report"
        self.LAZADA_ORDERS = f"{self._BI_SENSITIVE_BASE}/lazada_orders"
        self.LAZADA_ORDERS_BASE = f"{self._BI_SENSITIVE_BASE}/lazada_orders_base"
        self.LAZADA_ORDERS_ENRICHED = f"{self._BI_SENSITIVE_BASE}/lazada_orders_enriched"
        self.LIQUIDATION_PARCELS = f"{self._MASKED_URI_BASE}/liquidation_parcels"
        self.LNK_ORDERS_SHIPPERS = f"{self._MASKED_URI_BASE}/lnk_orders_shippers"
        self.LONGTAIL_SLA_HOURS = f"{self._MASKED_URI_BASE}/longtail_sla_hours"
        self.MART_MY_LOVS = f"{self._MASKED_URI_BASE}/mart_my_lovs"
        self.MART_VN_LOVS = f"{self._MASKED_URI_BASE}/mart_vn_lovs"
        self.MATCHED_CONSIGNEE_CONTACT_EMAIL = f"{self._MASKED_URI_BASE}/matched_consignee_contact_email"
        self.MATCHED_CONSIGNEE_CONTACT_EMAIL_CM = f"{self._MASKED_URI_BASE}/matched_consignee_contact_email_cm"
        self.MATCHED_SHIPPER_CONTACT_EMAIL = f"{self._MASKED_URI_BASE}/matched_shipper_contact_email"
        self.MIDDLE_MILE_TRIP_RELATIONSHIPS = f"{self._MASKED_URI_BASE}/middle_mile_trip_relationships"
        self.MILKRUN_ENRICHED = f"{self._MASKED_URI_BASE}/milkrun_enriched"
        self.MISSORT_DAILY_REPORT = f"{self._MASKED_URI_BASE}/missort_daily_report"
        self.MISSORT_KPI = f"{self._MASKED_URI_BASE}/missort_kpi"
        self.MITRA_ORDERS = f"{self._BI_SENSITIVE_BASE}/mitra_orders"
        self.MITRA_TREND_REPORT = f"{self._BI_SENSITIVE_BASE}/mitra_trend_report"
        self.MMCC_WEBHOOK = f"{self._MASKED_URI_BASE}/mmcc_webhook"
        self.MMCC_WEBHOOK_FAILED_TRANSMISSION = f"{self._MASKED_URI_BASE}/mmcc_webhook_failed_transmission"
        self.MMCC_WEBHOOK_LATENCY_BREACH = f"{self._MASKED_URI_BASE}/mmcc_webhook_latency_breach"
        self.MMCC_WEBHOOK_ORDERS = f"{self._MASKED_URI_BASE}/mmcc_webhook_orders"
        self.MMCC_WEBHOOK_ORDERS_ENRICHED = f"{self._MASKED_URI_BASE}/mmcc_webhook_orders_enriched"
        self.MIDDLE_MILE_DRIVER_APP_ADOPTION_KPI = f"{self._MASKED_URI_BASE}/middle_mile_driver_app_adoption_kpi"
        self.LAZ_SLA_BREACH_REPORT = f"{self._BI_SENSITIVE_BASE}/laz_sla_breach_report"
        self.LM_CLAW_MASTER = f"{self._MASKED_URI_BASE}/lm_claw_master"
        self.MM_DRIVERS_ENRICHED = f"{self._MASKED_URI_BASE}/mm_drivers_enriched"
        self.MOVEMENT_TRIPS_ENRICHED = f"{self._MASKED_URI_BASE}/movement_trips_enriched"
        self.N0_PICKUP_SLA_KPI_BASE = f"{self._MASKED_URI_BASE}/n0_pickup_sla_kpi_base"
        self.N0_PICKUP_SLA_KPI = f"{self._MASKED_URI_BASE}/n0_pickup_sla_kpi"
        self.NAMES_PER_CONSIGNEE_EMAIL_REPORT = f"{self._BI_SENSITIVE_BASE}/names_per_consignee_email_report"
        self.NINJA_BUDDIES_BUDDY_PAYOUT = f"{self._MASKED_URI_BASE}/ninja_buddies_buddy_payout"
        self.NINJA_BUDDIES_FRIEND_PAYOUT = f"{self._MASKED_URI_BASE}/ninja_buddies_friend_payout"
        self.NINJA_BUDDIES_LEADS = f"{self._MASKED_URI_BASE}/ninja_buddies_leads"
        self.NINJA_MART_MY_LINE_ITEMS_ENRICHED = f"{self._MASKED_URI_BASE}/ninja_mart_my_line_items_enriched"
        self.NINJA_MART_PL_ACTUALS = f"{self._BI_SENSITIVE_BASE}/ninja_mart_pl/actuals"
        self.NINJA_MART_PL_TARGETS = f"{self._BI_SENSITIVE_BASE}/ninja_mart_pl/targets"
        self.NINJA_MART_SALES_TARGETS = f"{self._MASKED_URI_BASE}/ninja_mart_sales_targets"
        self.NINJA_MART_SKU_MARGINS = f"{self._MASKED_URI_BASE}/ninja_mart_sku_margins"
        self.NINJA_MART_VN_LINE_ITEMS_ENRICHED = f"{self._MASKED_URI_BASE}/ninja_mart_vn_line_items_enriched"
        self.NINJA_MART_VN_PRESALES = f"{self._MASKED_URI_BASE}/ninja_mart_vn_presales"
        self.NINJA_MART_VN_RETENTION = f"{self._MASKED_URI_BASE}/ninja_mart_vn_retention"
        self.NINJA_REWARDS_MONTHLY = f"{self._MASKED_URI_BASE}/ninja_rewards_monthly"
        self.NINJA_REWARDS_USERS = f"{self._MASKED_URI_BASE}/ninja_rewards_users"
        self.NINJA_REWARDS_USERS_BASE = f"{self._MASKED_URI_BASE}/ninja_rewards_users_base"
        self.ON_HOLD_EVENTS = f"{self._MASKED_URI_BASE}/on_hold_events"
        self.ON_TIME_SHIPMENT_TRIPS_FY24 = f"{self._MASKED_URI_BASE}/on_time_shipment_trips_fy24"
        self.ON_TIME_SHIPMENT_TRIPS_KPI = f"{self._MASKED_URI_BASE}/on_time_shipment_trips_kpi"
        self.ORDER_ADDED_TO_IMPLANT_MANIFESTS = f"{self._MASKED_URI_BASE}/order_added_to_implant_manifests"
        self.ORDER_CANCELLATIONS = f"{self._MASKED_URI_BASE}/order_cancellations"
        self.ORDER_DELIVERIES = f"{self._MASKED_URI_BASE}/order_deliveries"
        self.ORDER_DEPARTMENT_MOVEMENTS = f"{self._MASKED_URI_BASE}/order_department_movements"
        self.ORDER_DESTINATIONS = f"{self._MASKED_URI_BASE}/order_destinations"
        self.ORDER_DIMENSIONS = f"{self._MASKED_URI_BASE}/order_dimensions"
        self.ORDER_DP_MILESTONES = f"{self._MASKED_URI_BASE}/order_dp_milestones"
        self.ORDER_EVENTS_PICKUP_SUCCESS = f"{self._MASKED_URI_BASE}/order_events_pickup_success"
        self.ORDER_EVENTS_UPDATE_CONTACT_INFORMATION = (
            f"{self._MASKED_URI_BASE}/order_events_update_contact_information"
        )
        self.ORDER_FORCE_SUCCESSES = f"{self._MASKED_URI_BASE}/order_force_successes"
        self.ORDER_FOURTH_PARTY_HANDOVERS = f"{self._MASKED_URI_BASE}/order_fourth_party_handovers"
        self.ORDER_HUB_HISTORY = f"{self._MASKED_URI_BASE}/order_hub_history"
        self.ORDER_HUB_HISTORY_BASE = f"{self._MASKED_URI_BASE}/order_hub_history_base"
        self.ORDER_HUB_HISTORY_INTERMEDIATE = f"{self._MASKED_URI_BASE}/order_hub_history_intermediate"
        self.ORDER_HUB_HISTORY_LATEST_TRIPS = f"{self._MASKED_URI_BASE}/order_hub_history_latest_trips"
        self.ORDER_HUB_MOVEMENTS = f"{self._MASKED_URI_BASE}/order_hub_movements"
        self.ORDER_HUB_TIMESTAMPS_FLAT = f"{self._MASKED_URI_BASE}/order_hub_timestamps_flat"
        self.ORDER_INBOUNDS = f"{self._MASKED_URI_BASE}/order_inbounds"
        self.ORDERS_JOBS_ASSIGNMENTS_ENRICHED = f"{self._MASKED_URI_BASE}/orders_jobs_assignments_enriched"
        self.ORDER_MILESTONES = f"{self._MASKED_URI_BASE}/order_milestones"
        self.ORDER_MOVEMENTS = f"{self._MASKED_URI_BASE}/order_movements"
        self.ORDER_MOVEMENTS_BASE = f"{self._MASKED_URI_BASE}/order_movements_base"
        self.ORDER_PICKUPS = f"{self._MASKED_URI_BASE}/order_pickups"
        self.ORDER_PROFITS_ID = f"{self._BI_SENSITIVE_BASE}/order_profits_id"
        self.ORDER_PROFITS_MY = f"{self._BI_SENSITIVE_BASE}/order_profits_my"
        self.ORDER_PROFITS_PH = f"{self._BI_SENSITIVE_BASE}/order_profits_ph"
        self.ORDER_PROFITS_SG = f"{self._BI_SENSITIVE_BASE}/order_profits_sg"
        self.ORDER_PROFITS_VN = f"{self._BI_SENSITIVE_BASE}/order_profits_vn"
        self.ORDER_RTS_TRIGGER_LOCATIONS = f"{self._MASKED_URI_BASE}/order_rts_trigger_locations"
        self.ORDER_RTS_TRIGGERS = f"{self._MASKED_URI_BASE}/order_rts_triggers"
        self.ORDER_TAGS_ENRICHED = f"{self._MASKED_URI_BASE}/order_tags_enriched"
        self.ORDER_THIRD_PARTY_TRANSFERS = f"{self._MASKED_URI_BASE}/order_third_party_transfers"
        self.ORDERS_ENRICHED = f"{self._MASKED_URI_BASE}/orders_enriched"
        self.ORIGINAL_CONSIGNEE_INFORMATION = f"{self._MASKED_URI_BASE}/original_consignee_information"
        self.PARCEL_MEASUREMENT_SCAN_ENRICHED = f"{self._MASKED_URI_BASE}/parcel_measurement_scan_enriched"
        self.PARCEL_SCAN_FEATURES = f"{self._BI_SENSITIVE_BASE}/parcel_scan_features"
        self.PARCEL_SWEEPER_EVENTS = f"{self._MASKED_URI_BASE}/parcel_sweeper_events"
        self.PARENT_SHIPPER_LIFETIME_VALUES = f"{self._MASKED_URI_BASE}/parent_shipper_lifetime_values"
        self.PARENT_SHIPPER_LIFETIME_VALUES_BASE = f"{self._MASKED_URI_BASE}/parent_shipper_lifetime_values_base"
        self.PARTNERSHIP_PREFIX = f"{self._MASKED_URI_BASE}/partnership_prefix"
        self.PARTNERSHIP_WEBHOOK = f"{self._MASKED_URI_BASE}/partnership_webhook"
        self.PARTNERSHIP_WEBHOOK_ORDERS = f"{self._MASKED_URI_BASE}/partnership_webhook_orders"
        self.PARTNERSHIP_WEBHOOK_ORDERS_ENRICHED = f"{self._MASKED_URI_BASE}/partnership_webhook_orders_enriched"
        self.PARTNERSHIP_WEBHOOK_INTER_ORDERS_ENRICHED = f"{self._MASKED_URI_BASE}/partnership_webhook_inter_orders_enriched"
        self.PARTNERSHIP_WEBHOOK_INTER_FAILED_TRANSMISSION = f"{self._MASKED_URI_BASE}/partnership_webhook_inter_failed_transmission"
        self.PARTNERSHIP_WEBHOOK_INTER_LATENCY_BREACH = f"{self._MASKED_URI_BASE}/partnership_webhook_inter_latency_breach"
        self.PARTNERSHIP_WEBHOOK_FAILED_TRANSMISSION = f"{self._MASKED_URI_BASE}/partnership_webhook_failed_transmission"
        self.PARTNERSHIP_WEBHOOK_LATENCY_BREACH = f"{self._MASKED_URI_BASE}/partnership_webhook_latency_breach"
        self.PARTNERSHIP_WEBHOOK_LATENCY_REPORT = f"{self._MASKED_URI_BASE}/partnership_webhook_latency_report"
        self.PARTNERSHIP_WEBHOOK_LATENCY_DETAILS = f"{self._MASKED_URI_BASE}/partnership_webhook_latency_details"
        self.PARTNERSHIP_WEBHOOK_STATS_ENRICHED = f"{self._MASKED_URI_BASE}/partnership_webhook_stats_enriched"
        self.PARTNERSHIP_WEBHOOK_SUMMARY_STATS = f"{self._MASKED_URI_BASE}/partnership_webhook_summary_stats"
        self.PETS_TICKETS_ENRICHED = f"{self._MASKED_URI_BASE}/pets_tickets_enriched"
        self.PETS_TICKETS_ENRICHED_BASE = f"{self._MASKED_URI_BASE}/pets_tickets_enriched_base"
        self.PETS_TICKETS_RESOLVED_DAILY = f"{self._MASKED_URI_BASE}/pets_tickets_resolved_daily"
        self.PICKUP_SCAN_EVENTS = f"{self._MASKED_URI_BASE}/pickup_scan_events"
        self.PICKUP_TRANSACTION_EVENTS = f"{self._MASKED_URI_BASE}/pickup_transaction_events"
        self.POD_VALIDATION_TASKS_ENRICHED = f"{self._MASKED_URI_BASE}/pod_validation_tasks_enriched"
        self.POH_ORDER_METRICS = f"{self._MASKED_URI_BASE}/proof_of_handover_order_metrics"
        self.POH_METRICS = f"{self._MASKED_URI_BASE}/proof_of_handover_metrics"
        self.PRICED_ORDERS_INTERMEDIATE = f"{self._BI_SENSITIVE_BASE}/priced_orders_intermediate"
        self.PRICING_ORDERS_HISTORY_ENRICHED = f"{self._MASKED_URI_BASE}/pricing_orders_history_enriched"
        self.PRICING_PRICING_DETAILS = f"{self._BI_SENSITIVE_BASE}/pricing_pricing_details"
        self.PRIORITISED_LAZADA_ORDERS = f"{self._BI_SENSITIVE_BASE}/prioritised_lazada_orders"
        self.PROOFS_ENRICHED = f"{self._MASKED_URI_BASE}/proofs_enriched"
        self.PUDO_COLLECT_ENRICHED = f"{self._MASKED_URI_BASE}/pudo_collect_enriched"
        self.PULL_OUT_OF_ROUTE_EVENTS = f"{self._MASKED_URI_BASE}/pull_out_of_route_events"
        self.RESERVATIONS_ENRICHED = f"{self._MASKED_URI_BASE}/reservations_enriched"
        self.RESERVATION_PICKED_UP_ORDERS = f"{self._MASKED_URI_BASE}/reservation_picked_up_orders"
        self.RESERVATION_CHANGE_OF_ROUTE_EVENTS = f"{self._MASKED_URI_BASE}/reservation_change_of_route_events"
        self.RESERVATION_ROUTED_EVENTS = f"{self._MASKED_URI_BASE}/reservation_routed_events"
        self.RESERVATION_REMOVE_FROM_ROUTE_EVENTS = f"{self._MASKED_URI_BASE}/reservation_remove_from_route_events"
        self.RESERVE_TRACKING_IDS_ENRICHED = f"{self._MASKED_URI_BASE}/reserve_tracking_ids_enriched"
        self.ROBOCALL_TABLE_ENRICHED = f"{self._BI_SENSITIVE_BASE}/robocall_table_enriched"
        self.ROBOCHAT_MESSAGES_ENRICHED = f"{self._MASKED_URI_BASE}/robochat_messages_enriched"
        self.ROUTE_CLASSIFICATION = f"{self._MASKED_URI_BASE}/route_classification"
        self.ROUTE_CLASSIFICATION_PH = f"{self._MASKED_URI_BASE}/route_classification_ph"
        self.ROUTE_INBOUND_EVENTS = f"{self._MASKED_URI_BASE}/route_inbound_events"
        self.ROUTE_LOGS_ENRICHED = f"{self._MASKED_URI_BASE}/route_logs_enriched"
        self.RTS_TRIGGER_EVENTS = f"{self._MASKED_URI_BASE}/rts_trigger_events"
        self.RTS_RATES_EXCLUDED_SHIPPERS = f"{self._MASKED_URI_BASE}/rts_rates_excluded_shippers"
        self.RTS_RATES_KPI = f"{self._MASKED_URI_BASE}/rts_rates_kpi"
        self.RTS_SHIPPER_CONFIRMATION_ENRICHED = f"{self._MASKED_URI_BASE}/rts_shipper_confirmation_enriched"
        self.SALES_TARGETS_BU = f"{self._MASKED_URI_BASE}/sales_targets_bu"
        self.SALESFORCE_ACCOUNT_ENRICHED = f"{self._MASKED_URI_BASE}/salesforce_account_enriched"
        self.SALESFORCE_ACTIVITY_ENRICHED = f"{self._MASKED_URI_BASE}/salesforce_activity_enriched"
        self.SALESFORCE_CASE_ENRICHED = f"{self._MASKED_URI_BASE}/salesforce_case_enriched"
        self.SALESFORCE_CLAIM_ENRICHED = f"{self._MASKED_URI_BASE}/salesforce_claim_enriched"
        self.SALESFORCE_CLAIM_ENRICHED_BASE = f"{self._MASKED_URI_BASE}/salesforce_claim_enriched_base"
        self.SALESFORCE_CONTACT_ENRICHED = f"{self._MASKED_URI_BASE}/salesforce_contact_enriched"
        self.SALESFORCE_CS_CASE_ENRICHED = f"{self._MASKED_URI_BASE}/salesforce_cs_case_enriched"
        self.SALESFORCE_FLEET_TASK_ENRICHED = f"{self._MASKED_URI_BASE}/salesforce_fleet_task_enriched"
        self.SALESFORCE_LEAD_ENRICHED = f"{self._MASKED_URI_BASE}/salesforce_lead_enriched"
        self.SALESFORCE_OPPORTUNITY_ENRICHED = f"{self._MASKED_URI_BASE}/salesforce_opportunity_enriched"
        self.SALESFORCE_SHIPPER_EXPORT = f"{self._MASKED_URI_BASE}/salesforce_shipper_export"
        self.SALESFORCE_RECORD_TYPE_ENRICHED = f"{self._MASKED_URI_BASE}/salesforce_record_type_enriched"
        self.SALESFORCE_SS_CASE_ENRICHED = f"{self._MASKED_URI_BASE}/salesforce_ss_case_enriched"
        self.SALESFORCE_USER_ENRICHED = f"{self._MASKED_URI_BASE}/salesforce_user_enriched"
        self.SALESPERSONS_ENRICHED = f"{self._MASKED_URI_BASE}/salespersons_enriched"
        self.SCAN_RESULT_ENRICHED = f"{self._MASKED_URI_BASE}/scan_result_enriched"
        self.SELLER_ORDER_FEATURES = f"{self._BI_SENSITIVE_BASE}/seller_order_features"
        self.SERVICE_BREACH_REPORT = f"{self._MASKED_URI_BASE}/service_breach_report"
        self.SFMC_SHIPPERS_EXPORT = f"{self._MASKED_URI_BASE}/sfmc_shippers_export"
        self.SG_ROUTE_ZONE_SUCCESS_RATE = f"{self._MASKED_URI_BASE}/sg_route_zone_success_rate"
        self.SG_SLA_ENRICHED = f"{self._MASKED_URI_BASE}/sg_sla_enriched"
        self.SHIPMENT_HUB_MILESTONES = f"{self._MASKED_URI_BASE}/shipment_hub_milestones"
        self.SHIPMENT_ORDERS_ENRICHED = f"{self._MASKED_URI_BASE}/shipment_orders_enriched"
        self.SHIPMENT_ORDERS_ENRICHED_BASE = f"{self._MASKED_URI_BASE}/shipment_orders_enriched_base"
        self.SHIPMENT_ADHERENCE_FY24 = f"{self._MASKED_URI_BASE}/shipment_adherence_fy24"
        self.SHIPMENT_MOVEMENT_ADHERENCE_KPI = f"{self._MASKED_URI_BASE}/shipment_movement_adherence_kpi"
        self.SHIPMENT_PATHS = f"{self._MASKED_URI_BASE}/shipment_paths"
        self.SHIPMENTS_ENRICHED = f"{self._MASKED_URI_BASE}/shipments_enriched"
        self.SHIPPER_ATTRIBUTES = f"{self._MASKED_URI_BASE}/shipper_attributes"
        self.SHIPPER_CLAIMS_REPORT = f"{self._MASKED_URI_BASE}/shipper_claims_report"
        self.SHIPPER_COMPLETION_VOL_DAILY = f"{self._MASKED_URI_BASE}/shipper_completion_vol_daily"
        self.SHIPPER_COMPLETION_VOL_DAILY_FULL = f"{self._MASKED_URI_BASE}/shipper_completion_vol_daily_full"
        self.SHIPPER_COMPLETION_VOL_MONTHLY = f"{self._MASKED_URI_BASE}/shipper_completion_vol_monthly"
        self.SHIPPER_CREATION_VOL_DAILY = f"{self._MASKED_URI_BASE}/shipper_creation_vol_daily"
        self.SHIPPER_INBOUND_VOL_DAILY = f"{self._MASKED_URI_BASE}/shipper_inbound_vol_daily"
        self.SHIPPER_INBOUND_VOL_MONTHLY = f"{self._MASKED_URI_BASE}/shipper_inbound_vol_monthly"
        self.SHIPPER_LIFETIME_VALUES = f"{self._MASKED_URI_BASE}/shipper_lifetime_values"
        self.SHIPPER_LIFETIME_VALUES_BASE = f"{self._MASKED_URI_BASE}/shipper_lifetime_values_base"
        self.SHIPPER_MILESTONES = f"{self._MASKED_URI_BASE}/shipper_milestones"
        self.SHIPPER_PICKUP_ASSIGNEES = f"{self._MASKED_URI_BASE}/shipper_pickup_assignees"
        self.SHIPPER_SEGMENTATION_DAILY = f"{self._MASKED_URI_BASE}/shipper_segmentation_daily"
        self.SHIPPER_SLA_DAYS = f"{self._MASKED_URI_BASE}/shipper_sla_days"
        self.SHIPPER_REF_ADDRESSES = f"{self._MASKED_URI_BASE}/shipper_ref_addresses"
        self.SHIPPER_REF_DATA = f"{self._MASKED_URI_BASE}/shipper_ref_data"
        self.SHIPPER_REF_PLATFORM_INFO = f"{self._MASKED_URI_BASE}/shipper_ref_platform_info"
        self.SHIPPER_REF_ITEMS = f"{self._MASKED_URI_BASE}/shipper_ref_items"
        self.SHIPPER_REF_SENDER_RECIPIENT = f"{self._MASKED_URI_BASE}/shipper_ref_sender_recipient"
        self.SHIPPERS_ENRICHED = f"{self._MASKED_URI_BASE}/shippers_enriched"
        self.SHOPIFY_ORDERS = f"{self._MASKED_URI_BASE}/shopify_orders"
        self.SLA_EXTENSIONS = f"{self._MASKED_URI_BASE}/sla_extensions"
        self.SLA_REPORTS_BASE = f"{self._MASKED_URI_BASE}/sla_reports_base"
        self.SNS_SUBSCRIPTION_ENRICHED = f"{self._MASKED_URI_BASE}/sns_subscription_enriched"
        self.SORT_COMPLIANCE = f"{self._MASKED_URI_BASE}/sort_compliance"
        self.SORT_ATS_COMPLIANCE_BASE = f"{self._MASKED_URI_BASE}/sort_ats_compliance_base"
        self.SORT_ATS_COMPLIANCE = f"{self._MASKED_URI_BASE}/sort_ats_compliance"
        self.SS_SEGMENTATION_DAILY = f"{self._MASKED_URI_BASE}/ss_segmentation_daily"
        self.SUSPICIOUS_LAZADA_ORDERS_REPORT = f"{self._BI_SENSITIVE_BASE}/suspicious_lazada_orders_report"
        self.THIRD_PARTY_TRANSFER_EVENTS = f"{self._MASKED_URI_BASE}/third_party_transfer_events"
        self.TICKET_CLOSURE_PERFORMANCE = f"{self._MASKED_URI_BASE}/ticket_closure_performance"
        self.TICKET_CREATION_LAST_SCANS = f"{self._MASKED_URI_BASE}/ticket_creation_last_scans"
        self.TICKET_CREATION_PERFORMANCE = f"{self._MASKED_URI_BASE}/ticket_creation_performance"
        self.TICKET_CREATION_PERFORMANCE_DAILY = f"{self._MASKED_URI_BASE}/ticket_creation_performance_daily"
        self.TICKET_CREATION_PERFORMANCE_REPORT = f"{self._MASKED_URI_BASE}/ticket_creation_performance_report"
        self.TICKET_RESOLVED_EVENTS = f"{self._MASKED_URI_BASE}/ticket_resolved_events"
        self.TIKTOK_LSP_DATA_REPORT = f"{self._MASKED_URI_BASE}/tiktok_lsp_data_report"
        self.TIKTOK_INTERNAL_TICKET_DATA = f"{self._MASKED_URI_BASE}/tiktok_internal_ticket_data"
        self.TIKTOK_LSP_DATA_REPORT_DUPLICATE = f"{self._MASKED_URI_BASE}/tiktok_lsp_data_report_duplicate"
        self.TIKTOK_INTERNAL_TICKET_DATA_DUPLICATE = f"{self._MASKED_URI_BASE}/tiktok_internal_ticket_data_duplicate"
        self.TIKTOK_TRACKER_DATA_REPORT_DUPLICATE = f"{self._MASKED_URI_BASE}/tiktok_tracker_data_report_duplicate"
        self.TIKTOK_TRACKER_DATA_REPORT = f"{self._MASKED_URI_BASE}/tiktok_tracker_data_report"
        self.TIKTOK_LLP_ID_CX = f"{self._MASKED_URI_BASE}/tiktok_llp_id_cx"
        self.TRANSIT_TIME_REPORT = f"{self._MASKED_URI_BASE}/transit_time_report"
        self.TRANSIT_TIME_SPEED_REPORT = f"{self._MASKED_URI_BASE}/transit_time_speed_report"
        self.UPDATE_ADDRESS_EVENTS = f"{self._MASKED_URI_BASE}/update_address_events"
        self.UPDATE_ADDRESS_VERIFICATION_EVENTS = f"{self._MASKED_URI_BASE}/update_address_verification_events"
        self.UPDATE_CASH_EVENTS = f"{self._MASKED_URI_BASE}/update_cash_events"
        self.UPDATE_CASH_EVENTS_ENRICHED = f"{self._MASKED_URI_BASE}/update_cash_events_enriched"
        self.UPDATE_CONSIGNEE_EMAIL_EVENTS = f"{self._BI_SENSITIVE_BASE}/update_consignee_email_events"
        self.UPDATE_STATUS_EVENTS = f"{self._MASKED_URI_BASE}/update_status_events"
        self.UPDATE_TAGS_EVENTS = f"{self._MASKED_URI_BASE}/update_tags_events"
        self.VALID_SCAN_EVENTS = f"{self._MASKED_URI_BASE}/valid_scan_events"
        self.WAREHOUSE_SCAN_EVENTS = f"{self._MASKED_URI_BASE}/warehouse_scan_events"
        self.WAREHOUSE_SPEED_REPORT = f"{self._MASKED_URI_BASE}/warehouse_speed_report"
        self.WAREHOUSE_SPEED_REPORT_EVENTS = f"{self._MASKED_URI_BASE}/warehouse_speed_report_events"
        self.WAYPOINTS_ENRICHED = f"{self._MASKED_URI_BASE}/waypoints_enriched"
        self.WAYPOINT_PHOTOS_ENRICHED = f"{self._MASKED_URI_BASE}/waypoint_photos_enriched"
        self.WORKDAY_SORT_PRODUCTIVITY = f"{self._MASKED_URI_BASE}/workday_sort_productivity"
        self.WMS_PARCELS_ENRICHED = f"{self._MASKED_URI_BASE}/wms_parcels_enriched"
        self.XB_CORE_COMPONENTS = f"{self._MASKED_URI_BASE}/xb_core_components"
        self.XB_EVENTS_COMBINED = f"{self._MASKED_URI_BASE}/xb_events_combined"
        self.XB_EVENTS_ENRICHED = f"{self._MASKED_URI_BASE}/xb_events_enriched"
        self.XB_EXPORT_JOB_ITEMS_ENRICHED = f"{self._MASKED_URI_BASE}/xb_export_job_items_enriched"
        self.XB_INVOICE_ALLOCATED_COSTS = f"{self._MASKED_URI_BASE}/xb_invoice_allocated_costs"
        self.XB_INVOICE_BAGS = f"{self._MASKED_URI_BASE}/xb_invoice_bags"
        self.XB_INVOICE_PARCELS = f"{self._MASKED_URI_BASE}/xb_invoice_parcels"
        self.XB_INVOICE_PERIODS = f"{self._MASKED_URI_BASE}/xb_invoice_periods"
        self.XB_INVOICE_SHIPMENTS = f"{self._MASKED_URI_BASE}/xb_invoice_shipments"
        self.XB_INVOICES = f"{self._MASKED_URI_BASE}/xb_invoices"
        self.XB_OUTBOUND_ORDERS = f"{self._MASKED_URI_BASE}/xb_outbound_orders"
        self.XB_PARCEL_ORIGIN_DESTINATION = f"{self._MASKED_URI_BASE}/xb_parcel_origin_destination"
        self.XB_PARCELS_ENRICHED = f"{self._MASKED_URI_BASE}/xb_parcels_enriched"
        self.XB_PRODUCTS_ENRICHED = f"{self._MASKED_URI_BASE}/xb_products_enriched"
        self.XB_PARCEL_RECEIVING_TASKS_ENRICHED = f"{self._MASKED_URI_BASE}/xb_parcel_receiving_tasks_enriched"
        self.XB_SERVICES_ENRICHED = f"{self._MASKED_URI_BASE}/xb_services_enriched"
        self.XB_SHIPMENT_PARCELS_ENRICHED = f"{self._MASKED_URI_BASE}/xb_shipment_parcels_enriched"
        self.XB_SHIPPER_RATES_PERIODS = f"{self._MASKED_URI_BASE}/xb_shipper_rates_periods"
        self.XB_SHIPPER_RATES_RATE = f"{self._MASKED_URI_BASE}/xb_shipper_rates_rate"
        self.XB_SHIPPER_RATES_RATE_REGIONS = f"{self._MASKED_URI_BASE}/xb_shipper_rates_rate_regions"
        self.XB_SHIPPER_RATES_REGIONS = f"{self._MASKED_URI_BASE}/xb_shipper_rates_regions"
        self.XB_SHIPPER_RATES_SHIPPER_PROFILE = f"{self._MASKED_URI_BASE}/xb_shipper_rates_shipper_profile"
        self.XB_SHIPPER_RATES_WEIGHT_RANGES = f"{self._MASKED_URI_BASE}/xb_shipper_rates_weight_ranges"
        self.XB_SHIPPERS_ENRICHED = f"{self._MASKED_URI_BASE}/xb_shippers_enriched"
        self.XB_TP_RATES_TP_RATES_INFO = f"{self._MASKED_URI_BASE}/xb_tp_rates_tp_rates_info"
        self.XB_TP_RATES_TP_RATES_STATUS = f"{self._MASKED_URI_BASE}/xb_tp_rates_tp_rates_status"
        self.XB_TP_RATES_TP_RATES_WEIGHT_RANGE = f"{self._MASKED_URI_BASE}/xb_tp_rates_tp_rates_weight_range"
        self.XB_VENDOR_RATE_CARDS_ENRICHED = f"{self._MASKED_URI_BASE}/xb_vendor_rate_cards_enriched"
        self.XB_VENDOR_RATE_CARDS_EXPANDED_FEES = f"{self._MASKED_URI_BASE}/xb_vendor_rate_cards_expanded_fees"
        self.XB_VENDOR_RATE_CARDS_RANGES_FEES = f"{self._MASKED_URI_BASE}/xb_vendor_rate_cards_ranges_fees"
        self.ZENDESK_TICKETS_ENRICHED = f"{self._MASKED_URI_BASE}/zendesk_tickets_enriched"