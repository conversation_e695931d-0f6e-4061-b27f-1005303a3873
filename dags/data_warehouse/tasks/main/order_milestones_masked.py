import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED + ".py",
    task_name=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MM,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    depends_on=(
        data_warehouse.OrdersDAG.Task.ORDER_CANCELLATIONS_MASKED,
        data_warehouse.OrdersDAG.Task.ORDER_DELIVERIES_MASKED,
        data_warehouse.OrdersDAG.Task.ORDER_DESTINATIONS_MASKED,
        data_warehouse.OrdersDAG.Task.ORDER_DIMENSIONS_MASKED,
        data_warehouse.OrdersDAG.Task.ORDER_DP_MILESTONES_MASKED,
        data_warehouse.OrdersDAG.Task.ORDER_FORCE_SUCCESSES_MASKED,
        data_warehouse.OrdersDAG.Task.ORDER_INBOUNDS_MASKED,
        data_warehouse.OrdersDAG.Task.ORDER_PICKUPS_MASKED,
        data_warehouse.OrdersDAG.Task.ORDER_RTS_TRIGGERS_MASKED,
        data_warehouse.OrdersDAG.Task.ORDER_THIRD_PARTY_TRANSFERS_MASKED,
        data_warehouse.OrdersDAG.Task.ORDERS_ENRICHED_MASKED,
        data_warehouse.OrdersDAG.Task.RESERVE_TRACKING_IDS_ENRICHED_MASKED,
        data_warehouse.OrdersDAG.Task.XB_OUTBOUND_ORDERS_MASKED,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="mm_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
    spark_conf_overrides={
        "spark.cz.accelerate.mode.enabled": "false",
        "spark.cz.accelerate.mode.convert.fallback.enabled": "true",
        "spark.cz.accelerate.mode.execute.fallback.enabled": "true",
    },
    post_execution_check=True,
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_PICKUPS,
                view_name="pickup",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_DP_MILESTONES,
                view_name="dp_milestones",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_INBOUNDS,
                view_name="inbound",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_DESTINATIONS,
                view_name="destination",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_CANCELLATIONS,
                view_name="cancellation",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_FORCE_SUCCESSES,
                view_name="force_success",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_DELIVERIES,
                view_name="delivery",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_RTS_TRIGGERS,
                view_name="rts",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_THIRD_PARTY_TRANSFERS,
                view_name="transfers",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).XB_OUTBOUND_ORDERS,
                view_name="xb_outbound_orders",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDERS_ENRICHED,
                view_name="orders_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).RESERVE_TRACKING_IDS_ENRICHED,
                view_name="reserve_tracking_ids_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).UPDATE_CASH_EVENTS_ENRICHED,
                view_name="update_cash_events_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).LNK_ORDERS_SHIPPERS,
                view_name="lnk_orders_shippers",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_ADDED_TO_IMPLANT_MANIFESTS,
                view_name="added_to_implant_manifests",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).ORDER_DIMENSIONS,
                view_name="dimensions",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                SELECT orders.order_id
                       , orders.system_id as country
                       , orders.tracking_id
                       , orders.third_party_tracking_id
                       , xb_outbound_orders.outbound_flag as xb_outbound_flag
                       , orders.shipper_reference_number
                       , orders.status
                       , orders.granular_status
                       , orders.status_enriched
                       , orders_shippers.shipper_id
                       , orders.seller_id
                       , orders.seller_name
                       , orders.lazmall_flag
                       , orders.lex_flag
                       , orders.source_id
                       , orders.plugin_name
                       , orders.stamp_id
                       , orders.cod_id
                       , orders.cost
                       , orders.cost_sgd
                       , orders.insurance_value
                       , orders.insurance_value_sgd
                       , orders.gst_fee
                       , orders.gst_fee_sgd
                       , orders.delivery_fee
                       , orders.delivery_fee_sgd
                       , orders.cod_value
                       , orders.cod_value_sgd
                       , orders.ninjapack_flag
                       , orders.rts_flag
                       , orders.parcel_size
                       , orders.nv_width
                       , orders.nv_height
                       , orders.nv_length
                       , orders.nv_weight
                       , orders.weight
                       , orders.original_weight
                       , dimensions.estimated_weight
                       , dimensions.estimated_volume
                       , orders.items
                       , orders.delivery_instructions
                       , orders.from_billing_zone
                       , orders.to_billing_zone
                       {%- for level in ('l1', 'l2', 'l3') %}
                       , orders.from_{{ level }}_id
                       , orders.from_{{ level }}_name
                       , orders.to_{{ level }}_id
                       , orders.to_{{ level }}_name
                       {%- endfor %}
                       , orders.service_type
                       , orders.delivery_type
                       , orders.order_type
                       , orders.delivery_verification_mode
                       , orders.from_postcode
                       , orders.to_postcode
                       , if(delivery.event_flag = 1
                           , delivery.previous_to_postcode_before_update_address_events
                           , orders.to_postcode
                       ) AS dest_postcode
                       , orders.to_name
                       , orders.from_name
                       , orders.from_address1
                       , orders.from_address2
                       , orders.from_city
                       , orders.from_country
                       , orders.to_address1
                       , orders.to_address2
                       , orders.to_city
                       , orders.to_country
                       , orders.original_to_address1
                       , orders.original_to_address2
                       , orders.original_to_city
                       , orders.original_to_country
                       , orders.original_to_latitude
                       , orders.original_to_longitude
                       , orders.creation_datetime
                       , orders.platform_creation_datetime
                       , destination.delivery_dest_hub_id as dest_hub_id
                       , destination.delivery_dest_zone as dest_zone
                       , dp_milestones.shipper_to_dp_datetime as dp_dropoff_datetime
                       -- TODO: rename this to dp_dropoff_dpms_id
                       , dp_milestones.shipper_to_dp_dpms_id as dp_dropoff_dp_id
                       , pickup.first_attempt_failure_reason_id as first_pickup_attempt_failure_reason_id
                       , pickup.first_attempt_reservation_id as first_pickup_attempt_reservation_id
                       , pickup.first_attempt_route_hub_id as first_pickup_attempt_hub_id
                       , pickup.first_attempt_datetime as first_pickup_attempt_datetime
                       , pickup.success_datetime as pickup_datetime
                       , pickup.nv_success_datetime as nv_pickup_datetime
                       , pickup.success_route_hub_id as pickup_hub_id
                       , pickup.success_route_driver_id as pickup_success_driver_id
                       , pickup.total_attempts as pickup_attempts
                       , cast(if(pickup.success_datetime is not null, 1, 0) as bigint) as pickup_flag
                       , inbound.event_datetime as inbound_datetime
                       , inbound.type as inbound_type
                       , inbound.scan_hub_id as inbound_hub_id
                       , cast(if(inbound.event_datetime is not null, 1, 0) as bigint) as inbound_flag
                       , dp_milestones.driver_to_dp_datetime as driver_to_dp_datetime
                       , dp_milestones.driver_to_dp_dpms_id as driver_to_dp_dpms_id
                       , delivery.first_valid_delivery_attempt_datetime
                       , delivery.first_valid_delivery_attempt_status
                       , delivery.first_valid_delivery_attempt_failure_reason_id
                         as first_valid_delivery_failure_reason_id
                       , delivery.first_valid_delivery_attempt_timeslot_start
                       , delivery.first_valid_delivery_attempt_timeslot_end
                       , delivery.first_valid_delivery_attempt_hub_id
                       , cast(if(delivery.first_valid_delivery_attempt_datetime is not null, 1, 0) as bigint)
                         as first_valid_delivery_attempt_flag
                       , delivery.second_valid_delivery_attempt_datetime
                       , delivery.second_valid_delivery_attempt_status
                       , delivery.second_valid_delivery_attempt_failure_reason_id
                         as second_valid_delivery_failure_reason_id
                       , delivery.second_valid_delivery_attempt_timeslot_start
                       , delivery.second_valid_delivery_attempt_timeslot_end
                       , delivery.second_valid_delivery_attempt_hub_id
                       , cast(if(delivery.second_valid_delivery_attempt_datetime is not null, 1, 0) as bigint)
                         as second_valid_delivery_attempt_flag
                       , delivery.third_valid_delivery_attempt_datetime
                       , delivery.third_valid_delivery_attempt_status
                       , delivery.third_valid_delivery_attempt_failure_reason_id
                         as third_valid_delivery_failure_reason_id
                       , delivery.third_valid_delivery_attempt_timeslot_start
                       , delivery.third_valid_delivery_attempt_timeslot_end
                       , delivery.third_valid_delivery_attempt_hub_id
                       , cast(if(delivery.third_valid_delivery_attempt_datetime is not null, 1, 0) as bigint)
                         as third_valid_delivery_attempt_flag
                       , delivery.last_valid_delivery_attempt_datetime
                       , delivery.last_valid_delivery_attempt_status
                       , delivery.last_valid_delivery_attempt_failure_reason_id as last_valid_delivery_failure_reason_id
                       , delivery.last_valid_delivery_attempt_timeslot_start
                       , delivery.last_valid_delivery_attempt_timeslot_end
                       , delivery.last_valid_delivery_attempt_hub_id
                       , coalesce(delivery.delivery_attempts, 0) as delivery_attempts
                       , destination.rts_dest_hub_id
                       , destination.rts_dest_zone as rts_dest_zone
                       , rts.event_datetime as rts_trigger_datetime
                       , rts.rts_reason
                       , delivery.first_valid_rts_attempt_datetime
                       , delivery.first_valid_rts_attempt_status
                       , delivery.first_valid_rts_attempt_failure_reason_id as first_valid_rts_failure_reason_id
                       , delivery.first_valid_rts_attempt_hub_id
                       , cast(if(delivery.first_valid_rts_attempt_datetime is not null, 1, 0) as bigint)
                         as first_valid_rts_attempt_flag
                       , delivery.last_valid_rts_attempt_datetime
                       , delivery.last_valid_rts_attempt_status
                       , delivery.last_valid_rts_attempt_failure_reason_id as last_valid_rts_failure_reason_id
                       , delivery.last_valid_rts_attempt_hub_id
                       , coalesce(delivery.rts_attempts, 0) as rts_attempts
                       , cast(if((delivery.delivery_attempts = 0
                                  or delivery.delivery_attempts IS NULL)
                                 and orders.rts_flag = 1, 1, 0) as bigint)
                         as rts_before_first_attempt_flag
                       , delivery.delivery_success_datetime
                       , delivery.delivery_success_hub_id
                       , delivery.delivery_success_driver_id
                       , cast(if(delivery.delivery_success_datetime is not null, 1, 0) as bigint)
                         as delivery_success_flag
                       , force_success.event_datetime as force_success_datetime
                       , cast(if(force_success.event_datetime is not null, 1, 0) as bigint) as force_success_flag
                       , transfers.event_datetime as third_party_transfer_datetime
                       , cast(if(transfers.event_datetime is not null, 1, 0) as bigint) as third_party_transfer_flag
                       , reserve_tracking_ids_enriched.is_pickup_required
                       , added_to_implant_manifests.event_datetime as implant_manifest_datetime
                       , cancellation.event_datetime as cancellation_datetime
                       , update_cash_events_enriched.min_old_cod_value
                       , update_cash_events_enriched.max_new_cod_value
                       , update_cash_events_enriched.cod_update_frequency
                       , orders.comments
                       , orders.billing_weight
                       , orders.third_party_shipper_name
                       , orders.package_id
                       , orders.shipper_group_my
                       , orders.created_month
                from orders_enriched as orders
                left join rts on
                    orders.order_id = rts.order_id
                left join pickup on
                    orders.order_id = pickup.order_id
                left join dp_milestones on
                    orders.order_id = dp_milestones.order_id
                left join inbound on
                    orders.order_id = inbound.order_id
                left join delivery on
                    orders.order_id = delivery.order_id
                left join cancellation on
                    orders.order_id = cancellation.order_id
                left join force_success on
                    orders.order_id = force_success.order_id
                left join destination on
                    orders.order_id = destination.order_id
                left join transfers on
                    orders.order_id = transfers.order_id
                left join xb_outbound_orders on
                    orders.order_id = xb_outbound_orders.order_id
                left join reserve_tracking_ids_enriched on
                    orders.tracking_id = reserve_tracking_ids_enriched.tracking_id
                left join update_cash_events_enriched on
                    orders.order_id = update_cash_events_enriched.order_id
                left join lnk_orders_shippers orders_shippers on 
                    orders_shippers.order_id = orders.order_id
                    and orders_shippers.system_id = orders.system_id
                left join added_to_implant_manifests on
                    orders.order_id = added_to_implant_manifests.order_id
                left join dimensions on
                    orders.order_id = dimensions.order_id
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ORDER_MILESTONES,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")

    run(spark, task_config)
    spark.stop()
