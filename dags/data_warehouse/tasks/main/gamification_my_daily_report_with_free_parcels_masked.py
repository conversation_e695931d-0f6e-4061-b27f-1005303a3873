import sys

from pyspark.sql import SparkSession

from common import date
from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.GamificationDAG.Task.GAMIFICATION_MY_DAILY_REPORT_WITH_FREE_PARCELS_MASKED + ".py",
    task_name=data_warehouse.GamificationDAG.Task.GAMIFICATION_MY_DAILY_REPORT_WITH_FREE_PARCELS_MASKED,
    system_ids=(
        constants.SystemID.MY,
    ),
    depends_on=(
        data_warehouse.GamificationDAG.Task.GAMIFICATION_MY_BASE_DATA_MASKED,
        data_warehouse.GamificationDAG.Task.GAMIFICATION_MY_RESERVATIONS_ENRICHED_MASKED,
        data_warehouse.GamificationDAG.Task.GAMIFICATION_MY_PLANNED_PARCELS_EVENTS_MASKED,
    ),
)

special_hybrid_driver_id = (200046, 200487, 200501, 1013700, 1653789)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 0, 6)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).GAMIFICATION_MY_BASE_DATA,
                view_name="orders_base_data",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).GAMIFICATION_MY_RESERVATIONS_ENRICHED,
                view_name="reservations_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).GAMIFICATION_MY_PLANNED_PARCELS_EVENTS,
                view_name="planned_parcels_events",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).MY_LM_PAYROLL_DATA_DELIVERY_RATES,
                view_name="delivery_rates",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).MY_LM_PAYROLL_DATA_RESERVATION_RATES,
                view_name="reservation_rates",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).MY_LM_PAYROLL_DATA_FREE_PARCELS,
                view_name="free_parcels",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).MY_LM_PAYROLL_DATA_BALLOON_TARGETS,
                view_name="balloon_targets",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).MY_LM_PAYROLL_DATA_LOGIC,
                view_name="logic",
            ),
        ),
        delta_tables=(
            base.InputTable(path=delta_tables.DriverProdGL(input_env, is_masked).DRIVERS, view_name="drivers"),
            base.InputTable(path=delta_tables.SortProdGL(input_env, is_masked).HUBS, view_name="hubs"),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="delivery_rates_cte",
                jinja_template=""" 
                with base as (

                    select
                        courier_type
                        , start_date
                        ,  date(lag(start_date)
                            over(partition by courier_type order by start_date desc) - interval '1' day)
                            as end_date_lag
                    from delivery_rates
                    where courier_type not like '%INDEPENDENT%'
                    group by 1,2

                ),

                final as (

                    select
                        delivery_rates.courier_type
                        , delivery_rates.weight_start
                        , coalesce(delivery_rates.weight_end, 999999) as weight_end
                        , delivery_rates.category
                        , delivery_rates.ppr1 as ppr
                        , if(delivery_rates.weight_end is null, concat('>',cast(delivery_rates.weight_start as int),'kg')
                            , concat(cast(delivery_rates.weight_start as int),'-',cast(delivery_rates.weight_end as int),'kg')) as weight_tier
                        , delivery_rates.start_date
                        , date(coalesce(base.end_date_lag, '2099-01-01')) as end_date
                    from delivery_rates
                    left join base
                        on delivery_rates.courier_type = base.courier_type
                        and delivery_rates.start_date = base.start_date
                    where delivery_rates.courier_type not like '%INDEPENDENT%'

                )

                select * from final
                """,
            ),

            base.TransformView(
                view_name="reservation_rates_cte",
                jinja_template=""" 
                with base as (

                    select 
                        *
                        ,  date(lag(start_date)
                            over(partition by courier_type order by start_date desc) - interval '1' day)
                            as end_date_lag
                    from reservation_rates
                    where courier_type not like '%INDEPENDENT%'

                )

                select
                    courier_type
                    , rsvn_ppr
                    , picked_up_parcel_ppr
                    , start_date
                    , date(coalesce(end_date_lag, '2099-01-01')) as end_date
                from base

                """,
            ),

            base.TransformView(
                view_name="free_parcels_cte",
                jinja_template=""" 
                with base as (

                    select 
                        *
                        ,  date(lag(start_date)
                            over(partition by courier_type, free_parcel_threshold order by start_date desc)
                                - interval '1' day)
                        as end_date_lag
                    from free_parcels

                )

                select
                    courier_type
                    , free_parcel_threshold
                    , start_date
                    , date(coalesce(end_date_lag, '2099-01-01')) as end_date
                from base
                """,
            ),

            base.TransformView(
                view_name="balloon_targets_cte",
                jinja_template=""" 
                with base as (

                    select 
                        *
                        ,  date(lag(start_date)
                            over(partition by courier_type, parcels_delivered_start, parcels_delivered_end
                                , balloon_bonus_amount order by start_date desc) - interval '1' day)
                        as end_date_lag
                    from balloon_targets
                )

                select
                    courier_type
                    , parcels_delivered_start
                    , coalesce(parcels_delivered_end, 999999) as parcels_delivered_end
                    , tier
                    , cast(balloon_bonus_amount as int) as balloon_bonus_amount
                    , start_date
                    , date(coalesce(end_date_lag, '2099-01-01')) as end_date
                from base

                """,
            ),

            base.TransformView(
                view_name="logic_cte",
                jinja_template=""" 
                with base as (

                    select 
                        *
                        ,  date(lag(start_date)
                            over(partition by courier_type order by start_date desc) - interval '1' day)
                            as end_date_lag
                    from logic
                    where lower(courier_type) like '%hybrid%'

                )

                select
                    logic
                    , courier_type
                    , start_date
                    , date(coalesce(end_date_lag, '2099-01-01')) as end_date
                from base

                """,
            ),

            base.TransformView(
                view_name="waypoint_group_cte",
                jinja_template=""" 
                -- Order_id and waypoint_id is the composite key and rank orders in same waypoint according to weight

                with dedup_rnk as (

                    select 
                        *
                        , row_number() over (partition by order_id, waypoint_id order by delivery_attempt_datetime desc)
                         as rnk
                    from orders_base_data
                    where 1=1
                        and transaction_type = 'DD'
                        and driver_type like '%HYBRID%DRIVER%'
                        and transaction_status = 'Success'

                ),

                dedup as (

                    select
                        order_id
                        , waypoint_id
                        , weight
                    from dedup_rnk
                    where rnk = 1

                )

                select
                    order_id
                    , waypoint_id
                    , weight
                    , row_number() over(partition by waypoint_id order by weight desc) weight_rank
                from dedup

                """,
            ),

            base.TransformView(
                view_name="hybrid_delivery_data",
                jinja_template="""
                with hybrid_delivery_base as (

                    select
                        orders_base_data.order_id
                        , date(orders_base_data.delivery_attempt_datetime) as route_date
                        , orders_base_data.driver_id
                        , orders_base_data.driver_display_name
                        , orders_base_data.driver_type
                        , orders_base_data.route_id
                        , orders_base_data.weight
                        , logic_cte.logic
                        , waypoint_group_cte.weight_rank
                        , if(waypoint_group_cte.weight_rank > 1 and lower(logic_cte.logic) like '%merged waypoint', 1,0)
                            as merged_deliveries_flag
                        , orders_base_data.transaction_status
                        , coalesce(hybrid_ppr.weight_tier, hybrid_ppr_special.weight_tier, ssd_ppr.weight_tier)
                            as weight_tier
                        -- Add category column to loop through for the column names at the end
                        , coalesce(hybrid_ppr.category, hybrid_ppr_special.category, ssd_ppr.category)
                            as category
                        , coalesce(hybrid_ppr.ppr, hybrid_ppr_special.ppr, ssd_ppr.ppr) as ppr
                        , free_parcels_cte.free_parcel_threshold
                        , 'delivery' as payscheme_component
                        , date_format(orders_base_data.delivery_attempt_datetime, 'yyyy-MM') created_month
                    from orders_base_data
                    left join delivery_rates_cte as hybrid_ppr
                        on orders_base_data.weight > hybrid_ppr.weight_start
                        and orders_base_data.weight <= hybrid_ppr.weight_end
                        and orders_base_data.driver_type = hybrid_ppr.courier_type
                        and date(orders_base_data.delivery_attempt_datetime) >= date(hybrid_ppr.start_date)
                        and date(orders_base_data.delivery_attempt_datetime) <= date(hybrid_ppr.end_date)
                        and orders_base_data.driver_id not in {{ special_hybrid_driver_id }}

                    left join delivery_rates_cte as hybrid_ppr_special
                        on orders_base_data.driver_id in {{ special_hybrid_driver_id }}
                        and orders_base_data.weight > hybrid_ppr_special.weight_start
                        and orders_base_data.weight <= hybrid_ppr_special.weight_end
                        and date(orders_base_data.delivery_attempt_datetime) >= date(hybrid_ppr_special.start_date)
                        and date(orders_base_data.delivery_attempt_datetime) <= date(hybrid_ppr_special.end_date)
                        and hybrid_ppr_special.courier_type = 'HYBRID DRIVER - HD - VAN(OLD) (SPECIAL)'

                    left join delivery_rates_cte as ssd_ppr
                        on orders_base_data.weight > ssd_ppr.weight_start
                        and orders_base_data.weight <= ssd_ppr.weight_end
                        and orders_base_data.driver_type = ssd_ppr.courier_type
                        and date(orders_base_data.delivery_attempt_datetime) >= date(ssd_ppr.start_date)
                        and date(orders_base_data.delivery_attempt_datetime) <= date(ssd_ppr.end_date)

                    left join free_parcels_cte
                        on orders_base_data.driver_type = free_parcels_cte.courier_type
                        and date(orders_base_data.delivery_attempt_datetime) >= date(free_parcels_cte.start_date)
                        and date(orders_base_data.delivery_attempt_datetime) <= date(free_parcels_cte.end_date)
                        and orders_base_data.driver_id not in {{ special_hybrid_driver_id }}

                    left join waypoint_group_cte
                        on orders_base_data.order_id = waypoint_group_cte.order_id
                        and orders_base_data.waypoint_id = waypoint_group_cte.waypoint_id

                    left join logic_cte
                        -- Hardcode the 4 special driver types to the psuedo driver type that we use on the gsheet
                        on if(orders_base_data.driver_id in {{ special_hybrid_driver_id }}
                            ,'HYBRID DRIVER - HD - VAN(OLD) (SPECIAL)', orders_base_data.driver_type) 
                                = logic_cte.courier_type
                        and date(orders_base_data.delivery_attempt_datetime) >= logic_cte.start_date
                        and date(orders_base_data.delivery_attempt_datetime) <= logic_cte.end_date

                    where 1=1
                        and orders_base_data.transaction_type = 'DD'
                        and (
                          orders_base_data.driver_type LIKE '%HYBRID%'
                          or orders_base_data.driver_type LIKE '%SAMEDAY%STAFF%'
                          )

                ),

                -- Apply Merged Waypoint logic
                merged_waypoint_ppr_cte as (

                    select
                        order_id
                        , route_date
                        , driver_id
                        , driver_display_name
                        , driver_type
                        , route_id
                        , weight
                        , transaction_status
                        , weight_tier
                        , category
                        , case when merged_deliveries_flag = 1
                            then 0.5 * ppr
                            else ppr end as ppr
                        , free_parcel_threshold
                        , payscheme_component
                        , merged_deliveries_flag
                        , created_month
                    from hybrid_delivery_base

                ),   

                payment_rank_cte as (

                    select
                        driver_id
                        , order_id
                        , route_date
                        -- Rank the orders by the ppr (non merged delivery) or weight (merged delivery)
                        , if(lower(driver_type) like '%driver%',
                            row_number() over(partition by driver_id, route_date order by ppr),
                            row_number() over(partition by driver_id, route_date order by weight)) as payment_rank
                    from merged_waypoint_ppr_cte
                    where 1=1
                        and transaction_status = 'Success'

                ),

                payable_lm_parcels_cte as (

                    select
                        merged_waypoint_ppr_cte.*
                        , payment_rank_cte.payment_rank
                        -- Orders will only be paid if their payment rank is greater than the free parcel threshold
                        -- No free parcel threshold for the special guys
                        , if((payment_rank_cte.payment_rank > merged_waypoint_ppr_cte.free_parcel_threshold
                            and merged_waypoint_ppr_cte.driver_id not in {{ special_hybrid_driver_id }})
                            or merged_waypoint_ppr_cte.driver_id in {{ special_hybrid_driver_id }}
                            , 1,0) as payable_parcel_flag
                    from merged_waypoint_ppr_cte
                    left join payment_rank_cte
                        on merged_waypoint_ppr_cte.order_id = payment_rank_cte.order_id
                        and merged_waypoint_ppr_cte.driver_id = payment_rank_cte.driver_id
                        and merged_waypoint_ppr_cte.route_date = payment_rank_cte.route_date

                ),

                aggregated_route_level as (

                    select
                        driver_id
                        , driver_display_name
                        , driver_type
                        , weight_tier
                        , category
                        , route_date
                        , ppr
                        , free_parcel_threshold
                        , created_month
                        , count(distinct order_id) filter(where transaction_status = 'Success') as parcels_delivered
                        --  Breakdown only includes payable parcels
                        , count(distinct order_id) filter(where transaction_status = 'Success' 
                            and payable_parcel_flag = 1 and merged_deliveries_flag = 1) 
                        as merged_parcels_delivered
                        , count(distinct order_id) filter(where transaction_status = 'Success' 
                            and payable_parcel_flag = 1 and merged_deliveries_flag = 0) 
                        as non_merged_parcels_delivered
                        , count(distinct order_id) filter(where transaction_status = 'Success'
                            and payable_parcel_flag = 1) 
                        as payable_lm_parcels
                    from payable_lm_parcels_cte
                    group by {{ range(1, 10) | join(',') }}

                ),

                final as (

                select 
                    * 
                    , payable_lm_parcels * ppr as delivery_bonus_per_weight_tier
                    , merged_parcels_delivered * ppr as merged_delivery_bonus_per_weight_tier
                    , non_merged_parcels_delivered * ppr as non_merged_delivery_bonus_per_weight_tier
                from aggregated_route_level

                )

                select *
                from final

                """,

                jinja_arguments={
                    "special_hybrid_driver_id": special_hybrid_driver_id,
                },

            ),

            base.TransformView(
                view_name="hybrid_rpu_data",
                jinja_template=""" 
                with hybrid_rpu_base as (

                    select
                        orders_base_data.order_id
                        , date(orders_base_data.pickup_datetime) as route_date
                        , orders_base_data.driver_id
                        , orders_base_data.driver_display_name
                        , orders_base_data.driver_type
                        , orders_base_data.route_id
                        , orders_base_data.weight
                        , orders_base_data.transaction_status
                        , orders_base_data.tracking_id
                        , coalesce(hybrid_ppr.weight_tier, hybrid_ppr_special.weight_tier) as weight_tier
                        -- Add category column to loop through for the column names at the end
                        , coalesce(hybrid_ppr.category, hybrid_ppr_special.category) as category
                        , coalesce(hybrid_ppr.ppr, hybrid_ppr_special.ppr) as ppr
                        , 'rpu' as payscheme_component
                        , date_format(orders_base_data.pickup_datetime, 'yyyy-MM') created_month
                    from orders_base_data
                    left join delivery_rates_cte as hybrid_ppr
                        on orders_base_data.weight > hybrid_ppr.weight_start
                        and orders_base_data.weight <= hybrid_ppr.weight_end
                        and orders_base_data.driver_type = hybrid_ppr.courier_type
                        and date(orders_base_data.pickup_datetime) >= date(hybrid_ppr.start_date)
                        and date(orders_base_data.pickup_datetime) <= date(hybrid_ppr.end_date)
                        and orders_base_data.driver_id not in {{ special_hybrid_driver_id }}

                    left join delivery_rates_cte as hybrid_ppr_special
                        on orders_base_data.driver_id in {{ special_hybrid_driver_id }}
                        and orders_base_data.weight > hybrid_ppr_special.weight_start
                        and orders_base_data.weight <= hybrid_ppr_special.weight_end
                        and date(orders_base_data.pickup_datetime) >= date(hybrid_ppr_special.start_date)
                        and date(orders_base_data.pickup_datetime) <= date(hybrid_ppr_special.end_date)
                        and hybrid_ppr_special.courier_type = 'HYBRID DRIVER - HD - VAN(OLD) (SPECIAL)'

                    where 1=1
                        and orders_base_data.transaction_type = 'PP'
                        and (
                          orders_base_data.driver_type LIKE '%HYBRID%'
                          )

                    ),

                rpu_aggregated as (

                    select
                        driver_id
                        , driver_display_name
                        , driver_type
                        , weight_tier
                        , category
                        , ppr
                        , route_date
                        , created_month
                        -- Split RDO from RPU pickup breakdown
                        , count(distinct order_id) filter(where transaction_status = 'Success' and tracking_id not like '%-DO') 
                        as return_parcels_picked_up
                        , count(distinct order_id) filter(where transaction_status = 'Success' and tracking_id like '%-DO') 
                        as rdo_picked_up
                    from hybrid_rpu_base
                    group by {{ range(1, 9) | join(',') }}

                )

                select 
                    * 
                    , (ppr * return_parcels_picked_up) as customer_pickup_bonus_per_weight_tier
                    , (ppr * rdo_picked_up) as rdo_pickup_bonus_per_weight_tier
                from rpu_aggregated

            """,

                jinja_arguments={
                    "special_hybrid_driver_id": special_hybrid_driver_id,
                },

            ),

            base.TransformView(
                view_name="hybrid_reservation_data",
                jinja_template=""" 
                with base as (

                select
                    route_driver_id as driver_id
                    , route_driver_name as driver_name
                    , route_driver_type as driver_type
                    , date(attempted_datetime) as route_date
                    , created_month
                    , count(reservation_id) filter(where status IN ('Success','COMPLETED')) as successful_reservations
                    , sum(payable_picked_up_orders) filter(where status IN ('Success','COMPLETED')
                    ) as payable_picked_up_orders
                from reservations_enriched
                where 1=1
                    and attempted_datetime is not null
                    and route_id is not null
                    and (route_driver_type LIKE '%HYBRID%'
                          or route_driver_type LIKE '%SAMEDAY%STAFF%')
                group by {{ range(1, 6) | join(',') }}

                )

                select
                    base.driver_name
                    , base.driver_id
                    , base.driver_type
                    , base.route_date
                    , 'Reservation' as weight_tier
                    , null as category
                    , null as parcels_on_route
                    , null as parcels_delivered
                    , null as merged_parcels_delivered
                    , null as non_merged_parcels_delivered
                    , null as failed_parcels
                    , null as free_parcel_threshold
                    , null as return_parcels_picked_up
                    , null as payable_lm_parcels
                    , base.created_month
                    , sum(base.successful_reservations) as total_reservation
                    , sum(null) as delivery_bonus_per_weight_tier
                    , sum(null) as merged_delivery_bonus_per_weight_tier
                    , sum(null) as non_merged_delivery_bonus_per_weight_tier
                    , sum(null) as customer_pickup_bonus_per_weight_tier
                    , sum(base.successful_reservations * reservation_rates_cte.rsvn_ppr 
                        + base.payable_picked_up_orders * reservation_rates_cte.picked_up_parcel_ppr
                    ) as merchant_pickup_bonus
                    , sum(null) as rdo_picked_up
                    , sum(null) as rdo_pickup_bonus_per_weight_tier
                from base
                left join reservation_rates_cte
                    on base.driver_type = reservation_rates_cte.courier_type
                    and base.route_date >= reservation_rates_cte.start_date
                    and base.route_date <= reservation_rates_cte.end_date
                group by {{ range(1, 16) | join(',') }}

                """,
            ),

            base.TransformView(
                view_name="parcels_on_route_agg",
                jinja_template=""" 
                with driver_inbound_scans_base as (       

                    select
                        order_id
                        , route_id
                        , max_by(driver_id, created_at) filter(where driver_id is not null) as driver_id
                        , max_by(type, created_at) as type
                        , max(created_at) as created_at
                    from planned_parcels_events
                    group by {{ range(1, 3) | join(',') }}

                ),

                driver_inbound_scans_agg as (

                    select
                        driver_id
                        , date(created_at) as route_date
                        , count(order_id) as parcels_on_route
                    from driver_inbound_scans_base
                    where type = 24
                    group by {{ range(1, 3) | join(',') }}

                )

                select * from driver_inbound_scans_agg

                """,
            ),

            base.TransformView(
                view_name="hybrid_delivery_data_final",
                jinja_template=""" 
                 with ppr_incentives_table as (

                    select
                        coalesce(hybrid_delivery_data.driver_display_name, hybrid_rpu_data.driver_display_name) 
                        as driver_display_name
                        , coalesce(hybrid_delivery_data.driver_id, hybrid_rpu_data.driver_id) as driver_id
                        , coalesce(hybrid_delivery_data.driver_type, hybrid_rpu_data.driver_type) as driver_type
                        , coalesce(hybrid_delivery_data.route_date, hybrid_rpu_data.route_date) as route_date
                        , coalesce(hybrid_delivery_data.weight_tier, hybrid_rpu_data.weight_tier) as weight_tier
                        , coalesce(hybrid_delivery_data.category, hybrid_rpu_data.category) as category
                        , hybrid_delivery_data.parcels_delivered
                        , hybrid_delivery_data.merged_parcels_delivered
                        , hybrid_delivery_data.non_merged_parcels_delivered
                        , hybrid_delivery_data.free_parcel_threshold
                        , hybrid_rpu_data.return_parcels_picked_up
                        , hybrid_delivery_data.payable_lm_parcels
                        , null as total_reservation
                        , hybrid_delivery_data.delivery_bonus_per_weight_tier
                        , hybrid_delivery_data.merged_delivery_bonus_per_weight_tier
                        , hybrid_delivery_data.non_merged_delivery_bonus_per_weight_tier
                        , hybrid_rpu_data.customer_pickup_bonus_per_weight_tier
                        , null as merchant_pickup_bonus
                        , hybrid_rpu_data.rdo_picked_up
                        , hybrid_rpu_data.rdo_pickup_bonus_per_weight_tier
                        , coalesce(hybrid_delivery_data.created_month, hybrid_rpu_data.created_month) as created_month
                    from hybrid_delivery_data
                    full outer join hybrid_rpu_data
                        on hybrid_delivery_data.driver_id = hybrid_rpu_data.driver_id
                        and hybrid_delivery_data.route_date = hybrid_rpu_data.route_date
                        and hybrid_delivery_data.weight_tier = hybrid_rpu_data.weight_tier

                    ),

                parcels_on_route_agg as (

                    select
                        driver_id
                        , driver_display_name
                        , driver_type
                        , date(delivery_attempt_datetime) as route_date
                        , created_month
                        , count(distinct order_id) as parcels_on_route
                    from orders_base_data
                    where orders_base_data.transaction_type = 'DD'
                        and driver_type not like '%INDEPENDENT%'
                    group by {{ range(1, 6) | join(',') }}

                ),

                failed_parcels_cte as (

                    select
                        driver_id
                        , driver_display_name
                        , driver_type
                        , date(delivery_attempt_datetime) as route_date
                        , created_month
                        , count(distinct order_id) as failed_parcels
                    from orders_base_data
                    where orders_base_data.transaction_type = 'DD'
                        and transaction_status = 'Fail'
                        and driver_type not like '%INDEPENDENT%'
                    group by {{ range(1, 6) | join(',') }}

                )

                select 
                    ppr_incentives_table.driver_display_name
                    , ppr_incentives_table.driver_id
                    , ppr_incentives_table.driver_type
                    , ppr_incentives_table.route_date
                    , ppr_incentives_table.weight_tier
                    , ppr_incentives_table.category
                    , parcels_on_route_agg.parcels_on_route
                    , ppr_incentives_table.parcels_delivered
                    , ppr_incentives_table.merged_parcels_delivered
                    , ppr_incentives_table.non_merged_parcels_delivered
                    , coalesce(failed_parcels_cte.failed_parcels,0) as failed_parcels
                    , ppr_incentives_table.free_parcel_threshold
                    , ppr_incentives_table.return_parcels_picked_up
                    , ppr_incentives_table.payable_lm_parcels
                    , ppr_incentives_table.created_month
                    , ppr_incentives_table.total_reservation
                    , ppr_incentives_table.delivery_bonus_per_weight_tier
                    , ppr_incentives_table.merged_delivery_bonus_per_weight_tier
                    , ppr_incentives_table.non_merged_delivery_bonus_per_weight_tier
                    , ppr_incentives_table.customer_pickup_bonus_per_weight_tier
                    , ppr_incentives_table.merchant_pickup_bonus
                    , ppr_incentives_table.rdo_picked_up
                    , ppr_incentives_table.rdo_pickup_bonus_per_weight_tier
                from ppr_incentives_table
                left join parcels_on_route_agg
                    on ppr_incentives_table.driver_id = parcels_on_route_agg.driver_id
                    and ppr_incentives_table.route_date = parcels_on_route_agg.route_date
                left join failed_parcels_cte
                    on ppr_incentives_table.driver_id = failed_parcels_cte.driver_id
                    and ppr_incentives_table.route_date = failed_parcels_cte.route_date

                """,
            ),

            base.TransformView(
                view_name="all_weight_tier_base",
                jinja_template="""
                -- This CTE is to get all weight tiers a driver should theoratically have for each route date
                    select
                        distinct orders_base_data.driver_id
                        , orders_base_data.driver_display_name
                        , orders_base_data.driver_type
                        , date(orders_base_data.delivery_attempt_datetime) as route_date
                        , coalesce(hybrid_ppr.weight_tier, hybrid_ppr_special.weight_tier, ssd_ppr.weight_tier) as weight_tier
                        , coalesce(hybrid_ppr.category, hybrid_ppr_special.category, ssd_ppr.category) as category
                        , orders_base_data.created_month
                    from orders_base_data
                    left join delivery_rates_cte as hybrid_ppr
                        on orders_base_data.driver_type = hybrid_ppr.courier_type
                        and date(orders_base_data.delivery_attempt_datetime) >= date(hybrid_ppr.start_date)
                        and date(orders_base_data.delivery_attempt_datetime) <= date(hybrid_ppr.end_date)
                        and orders_base_data.driver_id not in {{ special_hybrid_driver_id }}

                    left join delivery_rates_cte as hybrid_ppr_special
                        on orders_base_data.driver_id in {{ special_hybrid_driver_id }}
                        and date(orders_base_data.delivery_attempt_datetime) >= date(hybrid_ppr_special.start_date)
                        and date(orders_base_data.delivery_attempt_datetime) <= date(hybrid_ppr_special.end_date)
                        and hybrid_ppr_special.courier_type = 'HYBRID DRIVER - HD - VAN(OLD) (SPECIAL)'

                    left join delivery_rates_cte as ssd_ppr
                        on orders_base_data.driver_type = ssd_ppr.courier_type
                        and date(orders_base_data.delivery_attempt_datetime) >= date(ssd_ppr.start_date)
                        and date(orders_base_data.delivery_attempt_datetime) <= date(ssd_ppr.end_date)

                    where orders_base_data.driver_type LIKE '%HYBRID%'
                          or orders_base_data.driver_type LIKE '%SAMEDAY%STAFF%'
                """,
                jinja_arguments={
                    "special_hybrid_driver_id": special_hybrid_driver_id,
                },
            ),

            base.TransformView(
                view_name="combined_data",
                jinja_template=""" 
                with all_weight_tier_combined_cte as (

                     select 
                        all_weight_tier_base.driver_display_name
                        , all_weight_tier_base.driver_id
                        , all_weight_tier_base.driver_type
                        , all_weight_tier_base.route_date
                        , all_weight_tier_base.weight_tier
                        , all_weight_tier_base.category
                        , hybrid_delivery_data_final.parcels_on_route
                        , hybrid_delivery_data_final.parcels_delivered
                        , hybrid_delivery_data_final.merged_parcels_delivered
                        , hybrid_delivery_data_final.non_merged_parcels_delivered
                        , hybrid_delivery_data_final.failed_parcels
                        , hybrid_delivery_data_final.free_parcel_threshold
                        , hybrid_delivery_data_final.return_parcels_picked_up
                        , hybrid_delivery_data_final.payable_lm_parcels
                        , all_weight_tier_base.created_month
                        , hybrid_delivery_data_final.total_reservation
                        , hybrid_delivery_data_final.delivery_bonus_per_weight_tier
                        , hybrid_delivery_data_final.merged_delivery_bonus_per_weight_tier
                        , hybrid_delivery_data_final.non_merged_delivery_bonus_per_weight_tier
                        , hybrid_delivery_data_final.customer_pickup_bonus_per_weight_tier
                        , hybrid_delivery_data_final.merchant_pickup_bonus
                        , hybrid_delivery_data_final.rdo_picked_up
                        , hybrid_delivery_data_final.rdo_pickup_bonus_per_weight_tier
                    from all_weight_tier_base
                    left join hybrid_delivery_data_final
                        on all_weight_tier_base.driver_id = hybrid_delivery_data_final.driver_id
                        and all_weight_tier_base.route_date = hybrid_delivery_data_final.route_date
                        and all_weight_tier_base.weight_tier = hybrid_delivery_data_final.weight_tier

                ),

                combined_base as (

                    select *
                    from all_weight_tier_combined_cte
                    union all
                    select * 
                    from hybrid_reservation_data

                ),

                merge_all as (

                 select 
                     'my' as system_id
                     , combined_base.route_date
                     , date_format(combined_base.route_date, 'yyyy-MM') as route_month
                     , combined_base.driver_id
                     , combined_base.driver_type
                     , combined_base.driver_display_name
                     , if(datediff(combined_base.route_date, 
                            from_utc_timestamp(date(drivers.employment_start_date), '{{ local_timezone }}')) > 14
                                , hubs.name ,null) as hub_name
                     , if(datediff(combined_base.route_date, 
                            from_utc_timestamp(date(drivers.employment_start_date), '{{ local_timezone }}')) > 14
                                , drivers.hub_id ,null) as hub_id
                     , 'MYR' as currency
                     , '' as cycle_end_ppr_scheme
                     , combined_base.created_month
                     , cast(max(combined_base.free_parcel_threshold) as int) as target_parcel_count
                     , cast(max(combined_base.parcels_on_route) as int) as planned_parcel_count
                     , cast(coalesce(max(combined_base.parcels_on_route),0)
                         - coalesce(sum(combined_base.parcels_delivered),0)
                             - coalesce(max(combined_base.failed_parcels),0) as int) 
                    as pending_parcel_count
                     , cast(sum(combined_base.parcels_delivered) as int) as delivered_parcel_count
                     , cast(max(combined_base.failed_parcels) as int) as failed_parcel_count
                     , cast(sum(combined_base.payable_lm_parcels) as int) as daily_payable_lm_parcels
                     , cast(sum(combined_base.delivery_bonus_per_weight_tier) as double) as delivery_daily_bonus
                     , cast(sum(combined_base.delivery_bonus_per_weight_tier) as double) as delivery_daily_bonus_payroll
                     , cast(sum(null) as double) as waypoint_incentives
                     , cast(coalesce(sum(combined_base.delivery_bonus_per_weight_tier),0) 
                         + coalesce(sum(combined_base.merchant_pickup_bonus),0) 
                         + coalesce(sum(combined_base.customer_pickup_bonus_per_weight_tier),0) 
                         + coalesce(sum(rdo_pickup_bonus_per_weight_tier),0) as double)
                    as daily_bonus
                     , cast(coalesce(sum(combined_base.delivery_bonus_per_weight_tier),0) 
                         + coalesce(sum(combined_base.merchant_pickup_bonus),0) 
                         + coalesce(sum(combined_base.customer_pickup_bonus_per_weight_tier),0) 
                         + coalesce(sum(rdo_pickup_bonus_per_weight_tier),0) as double)
                    as daily_bonus_payroll           

                    -- Update the 2 loops to use the new category column instead of hardcoding via jinja args
                    {%- for category_number in range(1, 14) %}
                     , max(combined_base.weight_tier) 
                         filter(where combined_base.category = '{{ category_number }}')
                     as category_{{ category_number }}_name
                     , cast(sum(combined_base.payable_lm_parcels) 
                         filter(where combined_base.category = '{{ category_number }}') as int) 
                     as category_{{ category_number }}_parcels_delivered
                     , cast(sum(combined_base.delivery_bonus_per_weight_tier) 
                         filter(where combined_base.category = '{{ category_number }}') as double)
                    as category_{{ category_number }}_daily_bonus

                        -- Add breakdown for Merged waypoint deliveries and normal deliveries
                    , cast(sum(combined_base.merged_parcels_delivered) 
                        filter(where combined_base.category = '{{ category_number }}') as int) 
                    as category_{{ category_number }}_merged_parcels_delivered
                    , cast(sum(combined_base.merged_delivery_bonus_per_weight_tier) 
                        filter(where combined_base.category = '{{ category_number }}') as double) 
                    as category_{{ category_number }}_merged_deliveries_daily_bonus
                    , cast(sum(combined_base.non_merged_parcels_delivered) 
                        filter(where combined_base.category = '{{ category_number }}') as int) 
                    as category_{{ category_number }}_non_merged_parcels_delivered
                    , cast(sum(combined_base.non_merged_delivery_bonus_per_weight_tier) 
                        filter(where combined_base.category = '{{ category_number }}') as double) 
                    as category_{{ category_number }}_non_merged_deliveries_daily_bonus
                   {%- endfor %}

                    , cast(sum(combined_base.return_parcels_picked_up) as int) as pickedup_customer_count
                    , cast(sum(combined_base.customer_pickup_bonus_per_weight_tier) as double)
                    as pickedup_customer_amount
                    , cast(sum(combined_base.customer_pickup_bonus_per_weight_tier) as double)
                    as pickedup_customer_amount_payroll
                    , cast(sum(combined_base.total_reservation) as int) as pickedup_merchant_count
                    , cast(sum(combined_base.merchant_pickup_bonus) as double) as pickedup_merchant_amount
                    , cast(coalesce(sum(combined_base.merchant_pickup_bonus),0)
                        + coalesce(sum(combined_base.customer_pickup_bonus_per_weight_tier),0) as double)
                    as daily_pickup_bonus
                    , round(cast(sum(combined_base.parcels_delivered) 
                        / max(combined_base.parcels_on_route) as double),4) 
                    as success_rate
                    -- For Hyrbrid Riders, a parcel is counted twice if the weight is larger than 8kg (category 3)
                    -- Every other drivers and category, parcel is counted once
                    , cast(coalesce(sum(combined_base.parcels_delivered) 
                        filter(where combined_base.driver_type != 'HYBRID RIDER - HR - BIKE' 
                            or (combined_base.driver_type =  'HYBRID RIDER - HR - BIKE' 
                                and combined_base.category != 3)),0)
                                    + coalesce(sum(combined_base.parcels_delivered*2)
                                        filter(where combined_base.driver_type = 'HYBRID RIDER - HR - BIKE' 
                                            and combined_base.category = 3),0)
                    as int) as daily_parcels_delivered_for_balloon_bonus
                    , cast(sum(rdo_picked_up) as int) as picked_up_rdo_count
                    , cast(sum(rdo_pickup_bonus_per_weight_tier) as double) as picked_up_rdo_amount
                from combined_base
                left join drivers
                    on combined_base.driver_id = drivers.id
                    and drivers.system_id = '{{ system_id }}'
                left join hubs
                    on hubs.hub_id = drivers.hub_id
                    and hubs.system_id = '{{ system_id }}'
                group by {{ range(1, 12) | join(',') }}

                )

                select 
                    *
                from merge_all

                """,
                jinja_arguments={
                    "local_timezone": getattr(date.Timezone, system_id.upper())
                    , "system_id": system_id,
                }
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template=""" 
                with add_balloon_bonus_cte as (

                    select 
                        *
                        , cast(sum(daily_parcels_delivered_for_balloon_bonus) over(partition by driver_id, route_month 
                            order by route_date asc) as int) 
                         as cumulative_parcels_delivered_for_balloon_bonus
                    from combined_data

                ),

                /* This CTE is to create a table to obtain the targets for balloon bonus */
                balloon_targets_table as (

                    select
                        courier_type
                        , start_date
                        , end_date
                    {%- for tier_number in range(5) if tier_number > 0 %}
                        , cast(max(balloon_bonus_amount) filter(where tier = {{ tier_number }}) as double)
                         as tier_{{ tier_number }}_balloon_bonus_amount
                        , cast(max(parcels_delivered_start) filter(where tier = {{ tier_number }}) as int) 
                         as tier_{{ tier_number }}_balloon_targets_start
                        , cast(max(parcels_delivered_end) filter(where tier = {{ tier_number }}) as int)
                         as tier_{{ tier_number }}_balloon_targets_end
                    {%- endfor %}
                    from balloon_targets_cte
                    group by {{ range(1, 4) | join(',') }}

                ),

                balloon_amount_cte as (

                select 
                    add_balloon_bonus_cte.*
                    , cast(coalesce(balloon_targets_cte.balloon_bonus_amount, 0) as double) as balloon_bonus_amount
                {%- for i in range(5) if i > 0 %}
                    , tier_{{ i }}_balloon_targets_start
                    , tier_{{ i }}_balloon_targets_end
                    , tier_{{ i }}_balloon_bonus_amount
                {%- endfor %}
                from add_balloon_bonus_cte
                left join balloon_targets_cte
                    on add_balloon_bonus_cte.driver_type = balloon_targets_cte.courier_type
                    and date(add_balloon_bonus_cte.route_date) >= date(balloon_targets_cte.start_date)
                    and date(add_balloon_bonus_cte.route_date) <= date(balloon_targets_cte.end_date)
                    and add_balloon_bonus_cte.cumulative_parcels_delivered_for_balloon_bonus 
                        >= balloon_targets_cte.parcels_delivered_start
                    and add_balloon_bonus_cte.cumulative_parcels_delivered_for_balloon_bonus 
                        <= balloon_targets_cte.parcels_delivered_end
                    and add_balloon_bonus_cte.driver_id not in {{ special_hybrid_driver_id }}
                left join balloon_targets_table
                    on balloon_targets_table.courier_type = add_balloon_bonus_cte.driver_type
                    and date(add_balloon_bonus_cte.route_date) >= date(balloon_targets_table.start_date)
                    and date(add_balloon_bonus_cte.route_date) <= date(balloon_targets_table.end_date)
                    and add_balloon_bonus_cte.driver_id not in {{ special_hybrid_driver_id }}

                ),

                -- Ensure that balloon incentives appear only when the courier met the targets during the route day
                balloon_amount_change_cte as (

                select 
                    *
                    , cast(if(
                        lag(balloon_bonus_amount) over(
                            partition by driver_id, route_month order by route_date asc) != balloon_bonus_amount
                            , balloon_bonus_amount, 0) as double) as daily_balloon_bonus_amount
                from balloon_amount_cte

                )

                select * from balloon_amount_change_cte
                """,
                jinja_arguments={
                    "special_hybrid_driver_id": special_hybrid_driver_id,
                },
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).GAMIFICATION_MY_DAILY_REPORT_WITH_FREE_PARCELS,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()