import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked, parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.N0_PICKUP_SLA_KPI_BASE_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.N0_PICKUP_SLA_KPI_BASE_MASKED,
    depends_on=(data_warehouse.FleetDAG.Task.FLEET_PERFORMANCE_BASE_DATA_MASKED,
                data_warehouse.FleetDAG.Task.POH_ORDER_METRICS_MASKED,
                data_warehouse.FleetDAG.Task.POH_METRICS_MASKED,
                data_warehouse.FleetDAG.Task.ROUTE_LOGS_ENRICHED_MASKED,
                data_warehouse.FleetDAG.Task.FM_SHIPPER_EXCLUSIONS_MASKED,
                ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.RESERVATION_ROUTED_EVENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID, task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED,
        ),
        base.DependsOnExternal(dag_id=data_warehouse.HubsDAG.DAG_ID,
                               task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED, ),
        base.DependsOnExternal(dag_id=data_warehouse.OrdersDAG.DAG_ID,
                               task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED, ),
        base.DependsOnExternal(dag_id=data_warehouse.OrdersDAG.DAG_ID,
                               task_id=data_warehouse.OrdersDAG.Task.RESERVE_TRACKING_IDS_ENRICHED_MASKED, ),
        base.DependsOnExternal(dag_id=data_warehouse.DPDAG.DAG_ID,
                               task_id=data_warehouse.DPDAG.Task.DP_RESERVATIONS_ENRICHED_MASKED, ),
    ),
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.VN,
    ),
)

ph_laz_shipper_parent_id = '(341153, 401814, 931874, 744395)'
REPORT_START_DATE = "2022-01-01"


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 3, 2)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).FLEET_PERFORMANCE_BASE_DATA,
                view_name="fleet_performance_base_data",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).RESERVATION_ROUTED_EVENTS,
                view_name="reservation_routed_events",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).POH_ORDER_METRICS,
                view_name="poh_order_metrics",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).POH_METRICS,
                view_name="poh_metrics",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DP_RESERVATIONS_ENRICHED,
                view_name="dp_reservations_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ROUTE_LOGS_ENRICHED,
                view_name="route_logs_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).RESERVE_TRACKING_IDS_ENRICHED,
                view_name="reserve_tracking_ids_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).SHIPPER_PICKUP_CUTOFF,
                view_name="shipper_pickup_cutoff",
                system_id=system_id,
            ),
        ),

        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).INBOUND_SCANS,
                view_name="inbound_scans",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="earliest_scan",
                jinja_template="""
                select
                    order_id
                    , route_id
                    , min(from_utc_timestamp(created_at, '{{ local_timezone }}')) as created_at
                from inbound_scans
                where type = 1
                group by 1,2
                """,
                jinja_arguments={
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                },
            ),

            base.TransformView(
                view_name="first_sort_hub_inbound_scan_cte",
                jinja_template="""
                select
                    order_id
                    , min_by(hub_id, created_at) as hub_id
                from inbound_scans
                where type = 2
                    and result = 'S2'
                group by 1
                """,
            ),

            base.TransformView(
                view_name="first_add_to_route_cte",
                jinja_template="""
                select
                    reservation_routed_events.reservation_id
                    , min_by(route_logs_enriched.hub_id, reservation_routed_events.add_to_route_datetime) as hub_id
                from reservation_routed_events
                left join route_logs_enriched
                    on reservation_routed_events.route_id = route_logs_enriched.legacy_id
                where reservation_routed_events.system_id = 'my'
                group by 1
                """,
            ),

            base.TransformView(
                view_name="base_data",
                jinja_template="""
                with
                   base as (
                    select
                        order_milestones.system_id
                        , order_milestones.order_id
                        , order_milestones.tracking_id
                        , order_milestones.pickup_hub_id hub_id
                        , order_milestones.granular_status
                        , order_milestones.creation_datetime
                        , order_milestones.pickup_datetime
                        , order_milestones.inbound_datetime
                        , order_milestones.dp_dropoff_datetime
                        , order_milestones.shipper_id
                        , shipper_attributes.parent_id_coalesce
                        , shipper_attributes.sales_channel
                        , case when order_milestones.system_id = 'sg' and order_milestones.is_pickup_required is null THEN 0 
                            else order_milestones.is_pickup_required end as is_pickup_required
                        , if((order_milestones.source_id in (7,8))
                            or (order_milestones.system_id = 'vn' and lower(shipper_attributes.parent_name) like '%shopee%')
                            or (order_milestones.system_id = 'sg' and lower(shipper_attributes.parent_name) like '%shopee%' 
                                and order_milestones.creation_datetime >= date '2023-04-01')
                            or (date(order_milestones.creation_datetime) < date(reserve_tracking_ids_enriched.scheduled_pickup_date)),
                                    1,0) as dash_shipper_flag
                        , order_milestones.created_month
                    from order_milestones
                    left join shipper_attributes
                        on order_milestones.shipper_id = shipper_attributes.id
                    left join reserve_tracking_ids_enriched
                        on reserve_tracking_ids_enriched.tracking_id = order_milestones.tracking_id
                    where lower(order_milestones.order_type) != 'return'
                            and lower(shipper_attributes.sales_channel) NOT IN ('nbu','test')
                            and order_milestones.created_month >= date_format('{{ report_start_date }}', 'yyyy-MM')
                    ),

                route_id_cte as (

                select
                    base.system_id
                    , base.order_id
                    , base.tracking_id
                    , base.hub_id
                    , base.granular_status
                    , base.creation_datetime
                    , base.pickup_datetime
                    , base.dp_dropoff_datetime
                    , base.inbound_datetime
                    , base.shipper_id
                    , base.parent_id_coalesce
                    , sales_channel
                    , base.is_pickup_required
                    , earliest_scan.route_id
                    , base.dash_shipper_flag
                    , base.created_month
                from base
                left join earliest_scan
                    on earliest_scan.created_at = base.pickup_datetime
                    and earliest_scan.order_id = base.order_id
                )

                select * from route_id_cte
                """,
                jinja_arguments={
                    "report_start_date": REPORT_START_DATE,
                }
            ),
            base.TransformView(
                view_name="shipper_cutoff",
                jinja_template="""
                    select
                        system_id
                        , region
                        , shipper_segment
                        , date_format(cutoff, 'HHmm') as shipper_cutoff_time
                        , to_date(start_date, 'M/d/yyyy') as start_date
                        , to_date(end_date, 'M/d/yyyy') as end_date
                from shipper_pickup_cutoff                
                """,
            ),

            base.TransformView(
                view_name="fleet_data",
                jinja_template="""
                with 
                    fleet_data_base as (

                     select
                        base_data.system_id
                        , base_data.order_id
                        , base_data.tracking_id
                        , if(base_data.pickup_datetime is null, poh_metrics.hub_id, base_data.hub_id) as hub_id
                        , base_data.granular_status
                        , cast(fleet_performance_base_data.courier_id as bigint) as courier_id
                        , fleet_performance_base_data.courier_display_name as courier_name
                        , fleet_performance_base_data.courier_type
                        , base_data.creation_datetime
                        , base_data.pickup_datetime
                        , base_data.dp_dropoff_datetime
                        , base_data.inbound_datetime
                        , base_data.shipper_id
                        , base_data.parent_id_coalesce
                        , base_data.sales_channel
                        , base_data.route_id
                        , base_data.is_pickup_required
                        , fleet_performance_base_data.route_date
                        , base_data.dash_shipper_flag
                        , base_data.created_month
                    from base_data
                    left join fleet_performance_base_data
                        on date(base_data.pickup_datetime) = date(fleet_performance_base_data.route_date)
                        and base_data.route_id = fleet_performance_base_data.route_id   
                    left join poh_order_metrics
                        on base_data.order_id = poh_order_metrics.order_id
                    left join poh_metrics
                        on poh_order_metrics.hub_handover_id = poh_metrics.id
                    ),

                    hub_details_cte as (        

                        select
                            fleet_data_base.system_id
                            , fleet_data_base.order_id
                            , fleet_data_base.tracking_id
                            , fleet_data_base.hub_id
                            , hubs_enriched.name as hub_name
                            , hubs_enriched.parent_hub_id
                            , coalesce(hubs_enriched.address_city, first_sort_hub.address_city) as address_city
                            , hubs_enriched.region pickup_hub_region
                            , fleet_data_base.granular_status
                            , coalesce(hubs_enriched.parent_hub_id, fleet_data_base.hub_id) as parent_hub_id_coalesce
                            , fleet_data_base.courier_id
                            , fleet_data_base.courier_name
                            , fleet_data_base.courier_type
                            , fleet_data_base.creation_datetime
                            , fleet_data_base.pickup_datetime
                            , fleet_data_base.dp_dropoff_datetime
                            , fleet_data_base.inbound_datetime
                            , fleet_data_base.shipper_id
                            , fleet_data_base.parent_id_coalesce
                            , fleet_data_base.sales_channel
                            , fleet_data_base.route_id
                            , fleet_data_base.is_pickup_required
                            , fleet_data_base.route_date
                            , fleet_data_base.dash_shipper_flag
                            , fleet_data_base.created_month
                        from fleet_data_base
                        left join hubs_enriched
                            on fleet_data_base.hub_id = hubs_enriched.id
                        left join first_sort_hub_inbound_scan_cte
                            on fleet_data_base.order_id = first_sort_hub_inbound_scan_cte.order_id
                            and fleet_data_base.system_id = 'my'
                        left join hubs_enriched as first_sort_hub
                            on first_sort_hub_inbound_scan_cte.hub_id = first_sort_hub.id
                        where
                            (hubs_enriched.region <> 'Not in Use' or hubs_enriched.region is null)
                    ),

                    shippers_merge as (

                        select
                            hub_details_cte.system_id
                            , hub_details_cte.order_id
                            , hub_details_cte.tracking_id
                            , hub_details_cte.hub_id
                            , hub_details_cte.hub_name
                            , hub_details_cte.parent_hub_id
                            , hub_details_cte.address_city
                            , hub_details_cte.pickup_hub_region
                            , hub_details_cte.granular_status
                            , hub_details_cte.parent_hub_id_coalesce
                            , hub_details_cte.courier_id
                            , hub_details_cte.courier_name
                            , hub_details_cte.courier_type
                            , hub_details_cte.creation_datetime
                            , hub_details_cte.pickup_datetime
                            , hub_details_cte.dp_dropoff_datetime
                            , hub_details_cte.inbound_datetime
                            , hub_details_cte.shipper_id
                            , hub_details_cte.parent_id_coalesce
                            , hub_details_cte.is_pickup_required
                            , hub_details_cte.route_id
                            , hub_details_cte.route_date
                            , hub_details_cte.sales_channel
                            , if(dp_reservations_enriched.order_id is not null, 1, 0) dp_dropoff_flag
                            , coalesce(shipper_cutoff.shipper_cutoff_time, vn_pickup.shipper_cutoff_time, ph_laz.shipper_cutoff_time, vn_no_pickup.shipper_cutoff_time)
                                as shipper_cutoff_time
                            , if(hub_details_cte.sales_channel = 'Cross Border', 1, 0) as cross_border_flag
                            , hub_details_cte.dash_shipper_flag
                            , hub_details_cte.created_month
                            , row_number() over(partition by hub_details_cte.order_id, hub_details_cte.system_id order by hub_details_cte.order_id) rnk
                        from hub_details_cte
                        left join shipper_cutoff
                            on hub_details_cte.system_id = shipper_cutoff.system_id
                            and date(creation_datetime) >= date(shipper_cutoff.start_date)
                            and date(creation_datetime) <= date(shipper_cutoff.end_date)
                            and (
                                (hub_details_cte.system_id not in ('vn','ph')) 
                                or (hub_details_cte.system_id = 'ph' and parent_id_coalesce not in """ + ph_laz_shipper_parent_id + """ and shipper_cutoff.shipper_segment = 'all')
                                )
                        left join shipper_cutoff as vn_pickup
                            on hub_details_cte.system_id = vn_pickup.system_id 
                            and date(hub_details_cte.creation_datetime) >= date(vn_pickup.start_date)
                            and date(hub_details_cte.creation_datetime) <= date(vn_pickup.end_date)
                            and lower(hub_details_cte.pickup_hub_region) = vn_pickup.region
                            and hub_details_cte.system_id = 'vn'
                        left join shipper_cutoff as vn_no_pickup
                            on hub_details_cte.system_id = vn_no_pickup.system_id
                            and hub_details_cte.system_id = 'vn'
                        left join shipper_cutoff as ph_laz
                            on hub_details_cte.system_id = ph_laz.system_id
                            and date(hub_details_cte.creation_datetime) >= date(ph_laz.start_date)
                            and date(hub_details_cte.creation_datetime) <= date(ph_laz.end_date)
                            and hub_details_cte.system_id = 'ph' 
                            and hub_details_cte.parent_id_coalesce in """ + ph_laz_shipper_parent_id + """
                            and ph_laz.shipper_segment = 'lazada'
                        left join dp_reservations_enriched
                            on hub_details_cte.order_id = dp_reservations_enriched.order_id
                            and lower(dp_reservations_enriched.released_to) = 'driver'
                            and lower(dp_reservations_enriched.received_from) = 'shipper' 
                    )

                    select * from shippers_merge
                    where rnk = 1
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).N0_PICKUP_SLA_KPI_BASE,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()