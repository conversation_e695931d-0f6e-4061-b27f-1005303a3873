import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked, parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FleetDAG.Task.N0_PICKUP_SLA_KPI_MASKED + ".py",
    task_name=data_warehouse.FleetDAG.Task.N0_PICKUP_SLA_KPI_MASKED,
    depends_on=(data_warehouse.FleetDAG.Task.RESERVATIONS_ENRICHED_MASKED,
                data_warehouse.FleetDAG.Task.N0_PICKUP_SLA_KPI_BASE_MASKED,
                data_warehouse.FleetDAG.Task.ROUTE_LOGS_ENRICHED_MASKED,
                data_warehouse.FleetDAG.Task.FM_SHIPPER_EXCLUSIONS_MASKED,
                ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.PICKUP_SCAN_EVENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.RESERVATION_ROUTED_EVENTS_MASKED,
        ),
        base.DependsOnExternal(dag_id=data_warehouse.HubsDAG.DAG_ID,
                               task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED, ),
        base.DependsOnExternal(dag_id=data_warehouse.OrdersDAG.DAG_ID,
                               task_id=data_warehouse.OrdersDAG.Task.ORDERS_JOBS_ASSIGNMENTS_ENRICHED_MASKED, ),
    ),
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.VN,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)

ph_laz_config_change_date = "'2023-08-01'"
ph_laz_shipper_parent_id = '(341153, 401814, 931874, 744395)'
tt_parent_shipper_id = '(7474545,10853366, 7823651, 7717788, 9090233)'
REPORT_START_DATE = "2022-01-01"


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 3, 2)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    measurement_datetime_partition = f"/measurement_datetime={date.to_measurement_datetime_str(measurement_datetime)}"
    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).N0_PICKUP_SLA_KPI_BASE,
                view_name="n0_pickup_sla_kpi_base",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDERS_JOBS_ASSIGNMENTS_ENRICHED,
                view_name="orders_jobs_assignments_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).RESERVATIONS_ENRICHED,
                view_name="reservations_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PICKUP_SCAN_EVENTS,
                view_name="pickup_scan_events",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).RESERVATION_ROUTED_EVENTS,
                view_name="reservation_routed_events",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ROUTE_LOGS_ENRICHED,
                view_name="route_logs_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_EVENTS_PICKUP_SUCCESS,
                view_name="order_events_pickup_success",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.DataWarehouse(input_env).CALENDAR,
                view_name="calendar",
                system_id=system_id,
            ),
            base.InputTable(
                path=parquet_tables_masked.DataWarehouse(input_env).CALENDAR_SPEED,
                view_name="calendar_speed",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(
                    input_env).FM_SHIPPER_EXCLUSIONS + measurement_datetime_partition,
                view_name="fm_shipper_exclusions",
                system_id=system_id,
            ),
        ),

        delta_tables=(
            base.InputTable(
                path=delta_tables.DriverProdGL(input_env, is_masked).FAILURE_REASONS,
                view_name="failure_reasons",
            ),
            base.InputTable(
                path=delta_tables.DriverProdGL(input_env, is_masked).FAILURE_REASON_CODES,
                view_name="failure_reason_codes",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="earliest_reservation_routed_cte",
                jinja_template="""
                -- Get the earliest add to route event per reservation regardless of route_id
                    select
                         reservation_routed_events.reservation_id
                        , min_by(route_logs_enriched.date, reservation_routed_events.add_to_route_datetime) as earliest_reservation_route_date
                        , min(reservation_routed_events.add_to_route_datetime) add_to_route_datetime
                        , reservation_routed_events.system_id
                    from reservation_routed_events
                    left join route_logs_enriched
                        on reservation_routed_events.route_id = route_logs_enriched.legacy_id
                    group by 1,4
                """,
            ),

            base.TransformView(
                view_name="absolute_order_paj_reservation_mapping",
                jinja_template="""
                select
                    order_id
                    , min_by(job_id, job_assigned_datetime) as assigned_reservation_id
                    , min_by(job_type, job_assigned_datetime) as assigned_job_type
                    , min(job_assigned_datetime) as job_assignment_datetime
                from orders_jobs_assignments_enriched
                group by 1
                """,
            ),

            base.TransformView(
                view_name="order_paj_reservation_mapping_for_failure_reason",
                jinja_template="""
                with 
                    get_route_details_cte as (

                        select
                            orders_jobs_assignments_enriched.order_id
                            , earliest_reservation_routed_cte.earliest_reservation_route_date as route_date
                            , min_by(orders_jobs_assignments_enriched.job_id, orders_jobs_assignments_enriched.job_assigned_datetime) as assigned_reservation_id
                            , min_by(orders_jobs_assignments_enriched.job_type, orders_jobs_assignments_enriched.job_assigned_datetime) as assigned_job_type
                            , min_by(earliest_reservation_routed_cte.add_to_route_datetime, orders_jobs_assignments_enriched.job_assigned_datetime) as add_to_route_datetime
                        from orders_jobs_assignments_enriched
                        left join earliest_reservation_routed_cte
                            on orders_jobs_assignments_enriched.job_id = earliest_reservation_routed_cte.reservation_id
                        where 1=1
                            and earliest_reservation_routed_cte.earliest_reservation_route_date is not null
                        group by 1,2
                )

                select 
                    * 
                    , row_number() over(partition by order_id order by route_date) as rnk
                from get_route_details_cte
                """,
            ),
            base.TransformView(
                view_name="first_add_to_route_cte",
                jinja_template="""
                select
                    reservation_routed_events.reservation_id
                    , min_by(route_logs_enriched.hub_id, reservation_routed_events.add_to_route_datetime) as hub_id
                from reservation_routed_events
                left join route_logs_enriched
                    on reservation_routed_events.route_id = route_logs_enriched.legacy_id
                where reservation_routed_events.system_id = 'my'
                group by 1
                """,
            ),
            base.TransformView(
                view_name="working_day",
                jinja_template="""
                with 
                    pickup_exclusion_cte as (
                    select
                        n0_pickup_sla_kpi_base.system_id
                        , n0_pickup_sla_kpi_base.order_id
                        , n0_pickup_sla_kpi_base.tracking_id
                        , n0_pickup_sla_kpi_base.hub_id
                        , n0_pickup_sla_kpi_base.parent_hub_id
                        , n0_pickup_sla_kpi_base.address_city
                        , n0_pickup_sla_kpi_base.pickup_hub_region
                        , n0_pickup_sla_kpi_base.granular_status
                        , n0_pickup_sla_kpi_base.parent_hub_id_coalesce
                        , n0_pickup_sla_kpi_base.courier_id
                        , n0_pickup_sla_kpi_base.courier_name
                        , n0_pickup_sla_kpi_base.courier_type
                        , case when n0_pickup_sla_kpi_base.system_id = 'id' and n0_pickup_sla_kpi_base.dp_dropoff_flag = 1 THEN 1 ELSE 0 END AS mitra_flag
                        , n0_pickup_sla_kpi_base.creation_datetime
                        , n0_pickup_sla_kpi_base.pickup_datetime
                        , n0_pickup_sla_kpi_base.dp_dropoff_datetime
                        , n0_pickup_sla_kpi_base.dp_dropoff_flag
                        , n0_pickup_sla_kpi_base.inbound_datetime
                        , n0_pickup_sla_kpi_base.shipper_id
                        , n0_pickup_sla_kpi_base.parent_id_coalesce
                        , n0_pickup_sla_kpi_base.is_pickup_required
                        , n0_pickup_sla_kpi_base.route_id
                        , n0_pickup_sla_kpi_base.route_date
                        , n0_pickup_sla_kpi_base.is_pickup_required
                        , n0_pickup_sla_kpi_base.sales_channel
                        , n0_pickup_sla_kpi_base.shipper_cutoff_time
                        , case when fm_shipper_exclusions.shipper_id is not null THEN 1
                               when fm_shipper_exclusions.shipper_id is null and n0_pickup_sla_kpi_base.is_pickup_required = 0 and n0_pickup_sla_kpi_base.dp_dropoff_flag = 0 THEN 1
                            -- pickup hub exclusion for PH
                              when n0_pickup_sla_kpi_base.hub_id IN (103991,104930,103286,103280,103278,103277,103285,103279) and n0_pickup_sla_kpi_base.system_id = 'ph' THEN 1
                              when lower(n0_pickup_sla_kpi_base.hub_name) like '%sds%' and n0_pickup_sla_kpi_base.system_id = 'id' THEN 1
                               ELSE 0 END AS pickup_scan_exclusion_flag
                        , n0_pickup_sla_kpi_base.cross_border_flag
                        , n0_pickup_sla_kpi_base.dash_shipper_flag
                        , n0_pickup_sla_kpi_base.created_month
                    from n0_pickup_sla_kpi_base
                    left join fm_shipper_exclusions
                        on n0_pickup_sla_kpi_base.shipper_id = fm_shipper_exclusions.shipper_id
                        and n0_pickup_sla_kpi_base.system_id = fm_shipper_exclusions.system_id
                        and date(n0_pickup_sla_kpi_base.creation_datetime) >= date(fm_shipper_exclusions.start_date)
                        and date(n0_pickup_sla_kpi_base.creation_datetime) <= date(fm_shipper_exclusions.end_date)
                ),

                  add_earliest_assigned_reservation as (

                        select
                             pickup_exclusion_cte.system_id
                            , pickup_exclusion_cte.order_id
                            , pickup_exclusion_cte.tracking_id
                            , pickup_exclusion_cte.hub_id
                            , pickup_exclusion_cte.parent_hub_id
                            , coalesce(pickup_exclusion_cte.address_city, first_add_to_route_hub.address_city) as address_city
                            , pickup_exclusion_cte.pickup_hub_region
                            , pickup_exclusion_cte.granular_status
                            , pickup_exclusion_cte.parent_hub_id_coalesce
                            , pickup_exclusion_cte.courier_id
                            , pickup_exclusion_cte.courier_name
                            , pickup_exclusion_cte.courier_type
                            , pickup_exclusion_cte.mitra_flag
                            , pickup_exclusion_cte.creation_datetime
                            , pickup_exclusion_cte.pickup_datetime
                            , pickup_exclusion_cte.dp_dropoff_datetime
                            , pickup_exclusion_cte.dp_dropoff_flag
                            , pickup_exclusion_cte.inbound_datetime
                            , pickup_exclusion_cte.shipper_id
                            , pickup_exclusion_cte.parent_id_coalesce
                            , pickup_exclusion_cte.is_pickup_required
                            , pickup_exclusion_cte.route_id
                            , pickup_exclusion_cte.route_date
                            , pickup_exclusion_cte.sales_channel
                            , pickup_exclusion_cte.shipper_cutoff_time
                            , pickup_exclusion_cte.pickup_scan_exclusion_flag
                            , pickup_exclusion_cte.cross_border_flag
                            , pickup_exclusion_cte.dash_shipper_flag
                            , reservations_enriched.ready_datetime as reservation_ready_datetime
                            , absolute_order_paj_reservation_mapping.assigned_reservation_id
                            , absolute_order_paj_reservation_mapping.assigned_job_type
                            , absolute_order_paj_reservation_mapping.job_assignment_datetime
                            , pickup_exclusion_cte.created_month
                        from pickup_exclusion_cte
                        left join absolute_order_paj_reservation_mapping
                            on pickup_exclusion_cte.order_id = absolute_order_paj_reservation_mapping.order_id
                        left join reservations_enriched
                            on absolute_order_paj_reservation_mapping.assigned_reservation_id = reservations_enriched.reservation_id
                            and absolute_order_paj_reservation_mapping.assigned_job_type = reservations_enriched.data_source
                            and reservations_enriched.created_month >= date_format('{{ report_start_date }}', 'yyyy-MM')
                        left join first_add_to_route_cte
                            on absolute_order_paj_reservation_mapping.assigned_reservation_id = first_add_to_route_cte.reservation_id
                            and pickup_exclusion_cte.system_id = 'my'
                        left join hubs_enriched as first_add_to_route_hub
                            on first_add_to_route_cte.hub_id = first_add_to_route_hub.id
                    ),

                start_clock_cte as (
                        select 
                             system_id
                            , order_id
                            , tracking_id
                            , hub_id
                            , parent_hub_id
                            , address_city
                            , pickup_hub_region
                            , granular_status
                            , parent_hub_id_coalesce
                            , courier_id
                            , courier_name
                            , courier_type
                            , mitra_flag
                            , creation_datetime
                            , pickup_datetime
                            , dp_dropoff_datetime
                            , dp_dropoff_flag
                            , inbound_datetime
                            , shipper_id
                            , parent_id_coalesce
                            , is_pickup_required
                            , route_id
                            , route_date
                            , sales_channel
                            , shipper_cutoff_time
                            , pickup_scan_exclusion_flag
                            , cross_border_flag
                            , dash_shipper_flag
                            , assigned_reservation_id
                            , assigned_job_type
                            , reservation_ready_datetime
                            , if(dp_dropoff_flag = 1 and system_id != 'id', dp_dropoff_datetime, 
                                if(dash_shipper_flag = 1, coalesce(reservation_ready_datetime, creation_datetime), creation_datetime)) as start_clock
                            , created_month
                        from add_earliest_assigned_reservation                
                ),

                -- Create new CTE to calculate the number of minutes required to add to the next working day for TT's +24 hour logic
                tt_minutes_add_cte as (
                    select
                        *
                        , hour(start_clock) * 60 + minute(start_clock) as tt_minutes_add 
                    from start_clock_cte
                )

                -- Dash shippers do not consider shipper cutoff
                -- Create new nx_pickup_datetime column for TT logic of +24 hours instead of 2359
                        select 
                            tt_minutes_add_cte.*
                            {% for day in range(3) %}
                            , if(( coalesce(calendar.working_day,speed_cal.working_day) = 1 and date_format(tt_minutes_add_cte.start_clock, 'HHmm') < tt_minutes_add_cte.shipper_cutoff_time and tt_minutes_add_cte.dash_shipper_flag = 0)
                                OR (coalesce(calendar.working_day,speed_cal.working_day) = 1 and date_format(tt_minutes_add_cte.start_clock, 'HHmm') < tt_minutes_add_cte.shipper_cutoff_time and tt_minutes_add_cte.dash_shipper_flag = 1 AND tt_minutes_add_cte.dp_dropoff_flag = 1)
                                OR (coalesce(calendar.working_day,speed_cal.working_day) = 1 and tt_minutes_add_cte.dash_shipper_flag = 1 and tt_minutes_add_cte.dp_dropoff_flag = 0)
                                , coalesce(calendar.next_working_day_{{day}}, speed_cal.next_working_day_{{day}})
                                , coalesce(calendar.next_working_day_{{day+1}}, speed_cal.next_working_day_{{day+1}})
                                ) as n{{day}}_pickup_sla_datetime
                            {%- endfor %}
                            -- If tt orders created on non working day, next day 2359hrs
                            -- If tt orders created on working day before cutoff, same day 2359hrs 
                            -- If tt orders created on working day after cutoff, +24 hours to next working day
                            , case when tt_minutes_add_cte.parent_id_coalesce not in """ + tt_parent_shipper_id + """ then null
                                   when calendar.working_day = 0 then cast(calendar.next_working_day_1 as timestamp) + cast(1439|| ' minute' as interval)
                                   when date_format(tt_minutes_add_cte.start_clock, 'HHmm') < tt_minutes_add_cte.shipper_cutoff_time
                                       and (tt_minutes_add_cte.dash_shipper_flag = 0 
                                        or (tt_minutes_add_cte.dash_shipper_flag = 1 and tt_minutes_add_cte.dp_dropoff_flag = 1)
                                        or (tt_minutes_add_cte.dash_shipper_flag = 1 and tt_minutes_add_cte.dp_dropoff_flag = 0)
                                        )
                                    then cast(calendar.next_working_day_0 as timestamp)  + cast(1439|| ' minute' as interval)
                                   else cast(calendar.next_working_day_1 as timestamp) + cast(tt_minutes_add|| ' minute' as interval)
                                   end as tt_24hrs_n0_pickup_sla_datetime
                            , if(tt_minutes_add_cte.pickup_scan_exclusion_flag = 1 
                                or tt_minutes_add_cte.granular_status = 'Cancelled'
                                or tt_minutes_add_cte.cross_border_flag = 1, 1, 0) as exclusion_flag
                        from tt_minutes_add_cte
                        left join calendar
                            on date(tt_minutes_add_cte.start_clock) = calendar.next_working_day_0
                            and ((calendar.region = 'national' and tt_minutes_add_cte.system_id not in ('my','ph'))
                                or (tt_minutes_add_cte.system_id = 'my' and lower(calendar.region) = lower(tt_minutes_add_cte.address_city))
                                or (calendar.region = 'national' and tt_minutes_add_cte.system_id = 'ph' and date(tt_minutes_add_cte.creation_datetime) < date """ + ph_laz_config_change_date + """ and tt_minutes_add_cte.parent_id_coalesce IN """ + ph_laz_shipper_parent_id + """)
                                or (calendar.region = 'national' and tt_minutes_add_cte.system_id = 'ph' and tt_minutes_add_cte.parent_id_coalesce NOT IN """ + ph_laz_shipper_parent_id + """)
                                )
                        left join calendar_speed speed_cal
                            on date(tt_minutes_add_cte.start_clock) = speed_cal.next_working_day_0
                            and date(tt_minutes_add_cte.creation_datetime) >= date """ + ph_laz_config_change_date + """
                            and (speed_cal.region = 'national' and tt_minutes_add_cte.system_id = 'ph' and tt_minutes_add_cte.parent_id_coalesce IN """ + ph_laz_shipper_parent_id + """) 
                """,
                jinja_arguments={
                    "report_start_date": REPORT_START_DATE
                }
            ),
            base.TransformView(
                view_name="pickup_failure_reasons_cte",
                jinja_template="""
                with 
                    earliest_pickup_failure_event_by_route_date as (
                        select
                            order_id
                            , date(event_datetime) route_date
                            , min_by(failure_reason_id, event_datetime) earliest_failure_reason_id
                            , min(event_datetime) earliest_pickup_failure_datetime
                        from pickup_scan_events
                        where 
                            source_table = 'order_events'
                        group by 1,2
                    ),
                failure_reason_cte as (

                select
                    earliest_pickup_failure_event_by_route_date.order_id
                    , earliest_pickup_failure_event_by_route_date.route_date
                    , failure_reason_codes.description as failure_reason_liability_description
                    , failure_reasons.description as local_failure_reason
                    , failure_reasons.english_description as english_failure_reason
                    , earliest_pickup_failure_datetime as pickup_failure_datetime
                from earliest_pickup_failure_event_by_route_date
                left join failure_reasons
                    on failure_reasons.id = earliest_pickup_failure_event_by_route_date.earliest_failure_reason_id
                    and failure_reasons.system_id = '{{ system_id }}'
                left join failure_reason_codes
                    on failure_reasons.failure_reason_code_id = failure_reason_codes.id
                )

                select * from failure_reason_cte
                """,
                jinja_arguments={"system_id": system_id},
            ),

            base.TransformView(
                view_name="earliest_pickup_success_cte",
                jinja_template="""
                select
                     order_events_pickup_success.order_id
                    , min(order_events_pickup_success.created_at) as pickup_success_datetime
                from order_events_pickup_success
                group by 1
                """
            ),

            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with
                     sla_measurement as (
                        select 
                            working_day.*
                        {% for day in range(3) %}
                            , if(exclusion_flag = 0 
                                and date(pickup_datetime) <= n{{day}}_pickup_sla_datetime,1,0) n{{day}}_sla_met
                            , if(exclusion_flag = 0 
                                and n{{day}}_pickup_sla_datetime <= date('{{ measurement_datetime }}')
                            , 1, 0) as n{{day}}_sla_measured
                        {%- endfor %}
                            , if(exclusion_flag = 0 and n0_pickup_sla_datetime
                                <= date('{{ measurement_datetime }}'), 1,0) as sla_measured

                        -- TT 24 hours n0 sla met, sla_measured should be the same as existing n0
                          , if(tt_24hrs_n0_pickup_sla_datetime is null or exclusion_flag = 1, null,
                              if(pickup_datetime <= tt_24hrs_n0_pickup_sla_datetime, 1,0)) as tt_24hrs_n0_sla_met
                    from working_day
                ),

                    earliest_pickup_failure_event as (
                        select
                            order_id
                            , min_by(failure_reason_id, event_datetime) earliest_failure_reason_id
                            , min(event_datetime) earliest_pickup_failure_datetime
                        from pickup_scan_events
                        where 
                            source_table = 'order_events'
                        group by 1
                ),

                map_failure_reasons as (
                    select 
                        sla_measurement.*
                    {% for day in range(3) %}
                    ,   case
                            when sla_measurement.n0_sla_measured = 0 and sla_measurement.cross_border_flag = 1 then 'NOT MEASURED - CROSS BORDER EXCLUSION'
                            when sla_measurement.n0_sla_measured = 0 and sla_measurement.pickup_scan_exclusion_flag = 1 then 'NOT MEASURED - SHIPPER DIRECT INJECTION'
                            when sla_measurement.n0_sla_measured = 0 and sla_measurement.granular_status = 'Cancelled' then 'NOT MEASURED - ORDER CANCELLED'
                            when sla_measurement.n{{day}}_sla_measured = 1 and sla_measurement.n{{day}}_sla_met = 1 then 'MEASURED - N{{day}} SLA MET'
                            when sla_measurement.n{{day}}_sla_measured = 1 and sla_measurement.n{{day}}_sla_met = 0 then 'MEASURED - N{{day}} SLA MISSED'
                            when sla_measurement.n{{day}}_sla_measured = 0 and sla_measurement.exclusion_flag = 0 THEN 'TO BE MEASURED ONCE SLA DATE IS PASSED'
                            else 'ERROR'
                        end as n{{day}}_status_indicator
                    , n{{day}}_failure_reason_cte.local_failure_reason as n{{day}}_local_fr
                    , n{{day}}_failure_reason_cte.english_failure_reason as n{{day}}_eng_fr
                    , n{{day}}_failure_reason_cte.pickup_failure_datetime as n{{day}}_pickup_failure_datetime
                    , n{{day}}_failure_reason_cte.failure_reason_liability_description as n{{day}}_failure_reason_liability_description

                    {%- endfor %}

                    , earliest_pickup_failure_event.earliest_pickup_failure_datetime
                    -- to deduplicate for mutiple failures under same rsvn id
                    , row_number() over(partition by sla_measurement.order_id, sla_measurement.system_id order by sla_measurement.order_id) rnk2
                    from sla_measurement

                    {% for day in range(3) %}
                    left join pickup_failure_reasons_cte n{{day}}_failure_reason_cte
                        on sla_measurement.order_id = n{{day}}_failure_reason_cte.order_id
                        and sla_measurement.n{{day}}_pickup_sla_datetime = n{{day}}_failure_reason_cte.route_date
                    {%- endfor %}

                    left join earliest_pickup_failure_event
                        on sla_measurement.order_id = earliest_pickup_failure_event.order_id
                ),

                kpi_failure_reason_cte as (
                    select 
                    map_failure_reasons.system_id
                    , map_failure_reasons.order_id
                    , map_failure_reasons.tracking_id
                    , map_failure_reasons.hub_id
                    , map_failure_reasons.parent_hub_id
                    , map_failure_reasons.parent_hub_id_coalesce
                    , map_failure_reasons.pickup_hub_region
                    , map_failure_reasons.granular_status
                    , map_failure_reasons.courier_id
                    , map_failure_reasons.courier_name
                    , map_failure_reasons.courier_type
                    , map_failure_reasons.creation_datetime
                    , map_failure_reasons.pickup_datetime
                    , map_failure_reasons.inbound_datetime
                    , coalesce(map_failure_reasons.earliest_pickup_failure_datetime, map_failure_reasons.pickup_datetime) as first_pickup_attempt_datetime
                    , case when map_failure_reasons.earliest_pickup_failure_datetime is not null then 'Failure'
                           when map_failure_reasons.pickup_datetime is not null then 'Success'
                           else null end as first_pickup_attempt_status
                    , map_failure_reasons.reservation_ready_datetime
                    , map_failure_reasons.dp_dropoff_datetime
                    , map_failure_reasons.dp_dropoff_flag
                    , earliest_pickup_success_cte.pickup_success_datetime
                    , map_failure_reasons.shipper_id
                    , map_failure_reasons.is_pickup_required
                    , map_failure_reasons.route_id
                    , map_failure_reasons.route_date
                    , n0_auto_rollover_joins.add_to_route_datetime as earliest_add_to_route_datetime
                    , map_failure_reasons.shipper_cutoff_time
                    , map_failure_reasons.sales_channel
                    , map_failure_reasons.start_clock
                    , map_failure_reasons.pickup_scan_exclusion_flag
                    , map_failure_reasons.dash_shipper_flag
                    , map_failure_reasons.mitra_flag
                    , map_failure_reasons.exclusion_flag
                    , map_failure_reasons.assigned_reservation_id
                    , map_failure_reasons.assigned_job_type
                    , map_failure_reasons.sla_measured
                    , map_failure_reasons.tt_24hrs_n0_sla_met
                    , map_failure_reasons.tt_24hrs_n0_pickup_sla_datetime
                    {%- for day in range(3) %}

                    , map_failure_reasons.n{{day}}_pickup_sla_datetime
                    , map_failure_reasons.n{{day}}_sla_met
                    , map_failure_reasons.n{{day}}_sla_measured
                    , map_failure_reasons.n{{day}}_status_indicator

                    , case 
                    when coalesce(map_failure_reasons.n{{day}}_eng_fr, map_failure_reasons.n{{day}}_local_fr) is not null 
                        and map_failure_reasons.exclusion_flag = 0 
                        and map_failure_reasons.n{{day}}_sla_measured = 1
                        and map_failure_reasons.n{{day}}_sla_met = 0 
                    THEN coalesce(map_failure_reasons.n{{day}}_eng_fr, map_failure_reasons.n{{day}}_local_fr)
                    when map_failure_reasons.pickup_datetime is null
                        and map_failure_reasons.n{{day}}_sla_measured = 1
                        and map_failure_reasons.exclusion_flag = 0 
                        and map_failure_reasons.n{{day}}_sla_met = 0 
                        and coalesce(map_failure_reasons.n{{day}}_eng_fr, map_failure_reasons.n{{day}}_local_fr) is null
                        and map_failure_reasons.n{{day}}_pickup_failure_datetime is null
                        and (date(map_failure_reasons.inbound_datetime) = date(map_failure_reasons.n{{day}}_pickup_sla_datetime)
                            or map_failure_reasons.n{{day}}_pickup_sla_datetime is null
                            or date(map_failure_reasons.inbound_datetime) < date(map_failure_reasons.n{{day}}_pickup_sla_datetime)
                            )
                    THEN 'No Pickup Scan'
                    when map_failure_reasons.pickup_datetime is null
                        and map_failure_reasons.n{{day}}_sla_measured = 1
                        and map_failure_reasons.exclusion_flag = 0 
                        and map_failure_reasons.n{{day}}_sla_met = 0 
                        and coalesce(map_failure_reasons.n{{day}}_eng_fr, map_failure_reasons.n{{day}}_local_fr) is null
                        and map_failure_reasons.n{{day}}_pickup_failure_datetime is null
                    THEN 'No Pickup Attempts'
                    when map_failure_reasons.pickup_datetime is not null
                        and map_failure_reasons.n{{day}}_sla_measured = 1
                        and map_failure_reasons.exclusion_flag = 0 
                        and map_failure_reasons.n{{day}}_sla_met = 0 
                        and coalesce(map_failure_reasons.n{{day}}_eng_fr, map_failure_reasons.n{{day}}_local_fr) is null 
                        and coalesce(date(n{{day}}_auto_rollover_joins.add_to_route_datetime), map_failure_reasons.route_date) > date(map_failure_reasons.n{{day}}_pickup_sla_datetime) 
                        and date(map_failure_reasons.reservation_ready_datetime) > date(map_failure_reasons.n0_pickup_sla_datetime)
                    THEN 'Late Pickup: Routed after SLA due to Pickup Timeslot'
                    when map_failure_reasons.pickup_datetime is not null
                        and map_failure_reasons.n{{day}}_sla_measured = 1
                        and map_failure_reasons.exclusion_flag = 0 
                        and map_failure_reasons.n{{day}}_sla_met = 0 
                        and coalesce(map_failure_reasons.n{{day}}_eng_fr, map_failure_reasons.n{{day}}_local_fr) is null 
                        and coalesce(date(n{{day}}_auto_rollover_joins.add_to_route_datetime), map_failure_reasons.route_date) > date(map_failure_reasons.n{{day}}_pickup_sla_datetime) 
                    THEN 'Late Pickup: Routed after SLA'
                    when map_failure_reasons.pickup_datetime is not null
                        and map_failure_reasons.n{{day}}_sla_measured = 1
                        and map_failure_reasons.exclusion_flag = 0 
                        and map_failure_reasons.n{{day}}_sla_met = 0 
                        and coalesce(map_failure_reasons.n{{day}}_eng_fr, map_failure_reasons.n{{day}}_local_fr) is null 
                        and coalesce(date(n{{day}}_auto_rollover_joins.add_to_route_datetime), map_failure_reasons.route_date) <= date(map_failure_reasons.n{{day}}_pickup_sla_datetime) 
                    THEN 'Late Pickup: Routed before SLA'
                    ELSE null 
                    End as n{{day}}_failure_reason
                    {%- endfor %}

                    , map_failure_reasons.n0_eng_fr as english_failure_reason
                    , map_failure_reasons.n0_local_fr as local_failure_reason
                    , map_failure_reasons.n0_failure_reason_liability_description as failure_reason_liability_description
                    , map_failure_reasons.created_month
                from map_failure_reasons
                {%- for day in range(3) %}
                left join order_paj_reservation_mapping_for_failure_reason as n{{day}}_auto_rollover_joins
                    on map_failure_reasons.order_id = n{{day}}_auto_rollover_joins.order_id
                    and n{{day}}_auto_rollover_joins.route_date = date(map_failure_reasons.n{{day}}_pickup_sla_datetime)
                {%- endfor %}
                left join earliest_pickup_success_cte
                    on map_failure_reasons.order_id = earliest_pickup_success_cte.order_id
                where map_failure_reasons.rnk2 = 1
            ),

            n1_fr_cte as (
                select
                    system_id
                    , order_id
                    , tracking_id
                    , hub_id
                    , parent_hub_id
                    , parent_hub_id_coalesce
                    , pickup_hub_region
                    , granular_status
                    , courier_id
                    , courier_name
                    , courier_type
                    , creation_datetime
                    , pickup_datetime
                    , inbound_datetime
                    , first_pickup_attempt_datetime
                    , first_pickup_attempt_status
                    , reservation_ready_datetime
                    , dp_dropoff_datetime
                    , dp_dropoff_flag
                    , pickup_success_datetime
                    , shipper_id
                    , is_pickup_required
                    , route_id
                    , route_date
                    , earliest_add_to_route_datetime
                    , shipper_cutoff_time
                    , sales_channel
                    , start_clock
                    , n0_pickup_sla_datetime
                    , n1_pickup_sla_datetime
                    , n2_pickup_sla_datetime
                    , tt_24hrs_n0_pickup_sla_datetime
                    , tt_24hrs_n0_sla_met
                    , pickup_scan_exclusion_flag
                    , dash_shipper_flag as pickup_timeslot_as_start_clock_flag
                    , mitra_flag
                    , exclusion_flag
                    , n0_sla_met
                    , n1_sla_met
                    , n2_sla_met
                    , n0_sla_measured
                    , n1_sla_measured
                    , n2_sla_measured
                    , sla_measured
                    , n0_status_indicator
                    , n1_status_indicator
                    , n2_status_indicator
                    , assigned_reservation_id
                    , assigned_job_type
                    , n0_failure_reason
                    , case when (n0_failure_reason = 'No Pickup Scan') or (lower(n0_failure_reason) = 'duplicate reservation') THEN n0_failure_reason
                        ELSE n1_failure_reason END AS n1_failure_reason
                    , n2_failure_reason
                    , english_failure_reason
                    , local_failure_reason
                    , coalesce(failure_reason_liability_description, 
                                case when n0_failure_reason like '%Routed%SLA%' THEN 'Pickup success - NV Liable'
                                    when n0_failure_reason like 'No Pickup%' THEN 'Pickup failure - NV Liable'
                                    ELSE null END) failure_reason_liability_description
                    , created_month
                    from kpi_failure_reason_cte
                )

                select
                    system_id
                    , order_id
                    , tracking_id
                    , hub_id
                    , parent_hub_id
                    , parent_hub_id_coalesce
                    , pickup_hub_region
                    , granular_status
                    , courier_id
                    , courier_name
                    , courier_type
                    , creation_datetime
                    , pickup_datetime
                    , inbound_datetime
                    , first_pickup_attempt_datetime
                    , first_pickup_attempt_status
                    , reservation_ready_datetime
                    , dp_dropoff_datetime
                    , dp_dropoff_flag
                    , pickup_success_datetime
                    , shipper_id
                    , is_pickup_required
                    , route_id
                    , route_date
                    , earliest_add_to_route_datetime
                    , shipper_cutoff_time
                    , sales_channel
                    , start_clock
                    , n0_pickup_sla_datetime
                    , n1_pickup_sla_datetime
                    , n2_pickup_sla_datetime
                    , tt_24hrs_n0_pickup_sla_datetime
                    , tt_24hrs_n0_sla_met
                    , pickup_scan_exclusion_flag
                    , pickup_timeslot_as_start_clock_flag
                    , mitra_flag
                    , exclusion_flag
                    , n0_sla_met
                    , n1_sla_met
                    , n2_sla_met
                    , n0_sla_measured
                    , n1_sla_measured
                    , n2_sla_measured
                    , sla_measured
                    , n0_status_indicator
                    , n1_status_indicator
                    , n2_status_indicator
                    , assigned_reservation_id
                    , assigned_job_type
                    , n0_failure_reason
                    , n1_failure_reason
                    , case when (n1_failure_reason = 'No Pickup Scan') or (lower(n1_failure_reason) = 'duplicate reservation') THEN n1_failure_reason
                        ELSE n2_failure_reason END AS n2_failure_reason
                    , english_failure_reason
                    , local_failure_reason
                    , failure_reason_liability_description
                    , created_month
                    from n1_fr_cte
                """,
                jinja_arguments={"measurement_datetime": measurement_datetime},
            ),

        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).N0_PICKUP_SLA_KPI,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()