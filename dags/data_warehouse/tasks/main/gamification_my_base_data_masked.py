import sys

from pyspark.sql import SparkSession

from common import date
from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.GamificationDAG.Task.GAMIFICATION_MY_BASE_DATA_MASKED + ".py",
    task_name=data_warehouse.GamificationDAG.Task.GAMIFICATION_MY_BASE_DATA_MASKED,
    system_ids=(constants.SystemID.MY,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=delta_tables.CoreProdGL.DAG_ID_MY, task_id=delta_tables.CoreProdGL.Task_MY.ORDERS
        ),
        base.DependsOnExternal(
            dag_id=delta_tables.CoreProdGL.DAG_ID_MY, task_id=delta_tables.CoreProdGL.Task_MY.INBOUND_SCANS
        ),
        base.DependsOnExternal(
            dag_id=delta_tables.CoreProdGL.DAG_ID_MY, task_id=delta_tables.CoreProdGL.Task_MY.TRANSACTIONS
        ),
        base.DependsOnExternal(
            dag_id=delta_tables.CoreProdGL.DAG_ID_MY, task_id=delta_tables.CoreProdGL.Task_MY.ROUTE_WAYPOINT
        ),
        base.DependsOnExternal(
            dag_id=delta_tables.CoreProdGL.DAG_ID_MY, task_id=delta_tables.CoreProdGL.Task_MY.RESERVATIONS
        ),
        base.DependsOnExternal(
            dag_id=delta_tables.RouteProdGL.DAG_ID, task_id=delta_tables.RouteProdGL.Task.JOB_WAYPOINTS
        ),
        base.DependsOnExternal(
            dag_id=delta_tables.EventsProdGL.DAG_ID, task_id=delta_tables.EventsProdGL.Task.ORDER_EVENTS
        ),
        base.DependsOnExternal(
            dag_id=delta_tables.RouteProdGL.DAG_ID, task_id=delta_tables.RouteProdGL.Task.ROUTE_LOGS
        ),
        base.DependsOnExternal(dag_id=delta_tables.RouteProdGL.DAG_ID, task_id=delta_tables.RouteProdGL.Task.WAYPOINTS),
        base.DependsOnExternal(dag_id=delta_tables.DriverProdGL.DAG_ID, task_id=delta_tables.DriverProdGL.Task.DRIVERS),
        base.DependsOnExternal(
            dag_id=delta_tables.DriverProdGL.DAG_ID, task_id=delta_tables.DriverProdGL.Task.DRIVER_TYPES
        ),
        base.DependsOnExternal(
            dag_id=delta_tables.ControlProdGL.DAG_ID, task_id=delta_tables.ControlProdGL.Task.PICKUP_APPOINTMENT_JOBS
        ),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 2)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).ORDERS,
                view_name="orders",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).TRANSACTIONS,
                view_name="transactions",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).INBOUND_SCANS,
                view_name="inbound_scans",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.DriverProdGL(input_env, is_masked).DRIVERS,
                view_name="drivers",
            ),
            base.InputTable(
                path=delta_tables.DriverProdGL(input_env, is_masked).DRIVER_TYPES,
                view_name="driver_types",
            ),
            base.InputTable(
                path=delta_tables.RouteProdGL(input_env, is_masked).ROUTE_LOGS,
                view_name="route_logs",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="orders_base_data",
                jinja_template="""
                with base as (
                select
                    transactions.order_id
                    , orders.tracking_id
                    , transactions.status as transaction_status
                    , transactions.type as transaction_type
                    , orders.type as order_type
                    , orders.weight
                    , cast(transactions.waypoint_id as bigint) as waypoint_id
                    , route_logs.legacy_id as route_id
                    , drivers.id as driver_id
                    , driver_types.name as driver_type
                    , drivers.display_name as driver_display_name
                    , from_utc_timestamp(transactions.service_end_time, '{{ local_timezone }}') as delivery_attempt_datetime
                    , from_utc_timestamp(inbound_scans.created_at, '{{ local_timezone }}') as pickup_datetime
                from transactions
                left join route_logs
                    on transactions.route_id = route_logs.legacy_id
                    and route_logs.system_id = '{{ system_id }}'
                left join orders
                    on transactions.order_id = orders.id
                left join drivers
                    on route_logs.driver_id = drivers.id
                    and drivers.system_id = '{{ system_id }}'
                left join driver_types
                    on drivers.driver_type_id = driver_types.id
                    and driver_types.system_id = '{{ system_id }}'
                left join inbound_scans
                    on orders.id = inbound_scans.order_id
                    and orders.type = 'Return'
                    and inbound_scans.type = 1
                    and inbound_scans.result = 'S1'
                where 1=1
                    and (
                        driver_types.name like '%HYBRID%'
                        or driver_types.name like '%INDEPENDENT%'
                        or driver_types.name like '%SAMEDAY%'
                        or driver_types.name like '%RTM%'
                    )
                )

                select
                    *
                    , 'my' as system_id
                    , date_format(coalesce(delivery_attempt_datetime, pickup_datetime), 'yyyy-MM') as created_month
                from base
                """,
                jinja_arguments={"local_timezone": getattr(date.Timezone, system_id.upper()), "system_id": system_id},
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).GAMIFICATION_MY_BASE_DATA,
        measurement_datetime=measurement_datetime,
        partition_by=("created_month",),
        system_id=system_id,
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()