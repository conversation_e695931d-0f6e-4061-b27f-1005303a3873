import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.GSheetsDAG.Task.FINANCE_BILLING_FILES_MY_MASKED + ".py",
    task_name=data_warehouse.GSheetsDAG.Task.FINANCE_BILLING_FILES_MY_MASKED,
    system_ids=(constants.SystemID.GL,),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="pricing", partition_columns=("created_date")),
                           ),
)

def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.GDrive(input_env).FINANCE_BILLING_FILES_MY, view_name="finance_billing_files_my"
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    tracking_id
                    , billing_weight
                    , net_delivery_fee
                    , rts_fee
                    , cod_fee
                    , insurance_fee
                    , from_billing_zone
                    , to_location
                    , source_file
                    , current_date() as created_date
                from finance_billing_files_my
                """,
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).FINANCE_BILLING_FILES_MY,
        measurement_datetime=measurement_datetime,
        partition_by=("created_date",),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
