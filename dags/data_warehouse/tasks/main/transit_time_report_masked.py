import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked, delta_tables

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderSLADAG.Task.TRANSIT_TIME_REPORT_MASKED + ".py",
    task_name=data_warehouse.OrderSLADAG.Task.TRANSIT_TIME_REPORT_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    depends_on=(
        data_warehouse.OrderSLADAG.Task.SLA_REPORTS_BASE_MASKED,
        data_warehouse.OrderSLADAG.Task.SHIPPER_SLA_DAYS_MASKED,
        data_warehouse.OrderSLADAG.Task.SLA_EXTENSIONS_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.CalendarMaskedDAG.DAG_ID, task_id=data_warehouse.CalendarMaskedDAG.Task.CALENDAR_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID, task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED
        ),
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
                           base.HiveMetastoreTaskConfig(hive_schema="pricing"),),
    post_execution_check=True,
)

# shopee change RTS start clock to rts_trigger from forward_leg
ID_RTS_START_TIME = "2022-07-18"


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.DataWarehouse(input_env).CALENDAR,
                view_name="calendar",
                system_id=system_id,
            ),
            base.InputTable(path=parquet_tables_masked.GSheets(input_env).CUSTOM_MAPPING_MY, view_name="custom_mapping_my"),
        ),
        delta_tables=(
            base.InputTable(path=delta_tables.GDrive(input_env).SLA_HARDCODED, view_name="sla_hardcoded"),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SLA_REPORTS_BASE,
                view_name="sla_reports_base",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SLA_EXTENSIONS,
                view_name="sla_extensions",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_SLA_DAYS,
                view_name="shipper_sla_days",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="start_clock_conf",
                jinja_template="""
                SELECT base.order_id
                       , base.tracking_id
                       , base.sales_channel
                       , base.third_party_tracking_id
                       , base.inbound_datetime
                       , base.inbound_hub_id
                       , base.pickup_datetime
                       , base.pickup_hub_id
                       , base.dp_dropoff_datetime
                       , base.xb_handed_over_to_last_mile_datetime
                       , base.rts_trigger_datetime
                       , base.first_valid_delivery_attempt_timeslot_start
                       , base.first_valid_delivery_attempt_timeslot_end
                       , base.first_valid_delivery_attempt_datetime
                       , base.second_valid_delivery_attempt_datetime
                       , base.delivery_success_datetime
                       , base.from_billing_zone
                       , base.to_billing_zone
                       , base.from_l1_id
                       , base.from_l2_id
                       , base.to_l2_id
                       , base.to_l3_id
                       , base.from_name
                       , base.from_postcode
                       , base.dest_postcode
                       , base.service_type
                       , base.reporting_name
                       , base.shipper_id
                       , base.parent_id
                       , base.dest_hub_sla_region
                       , base.dest_hub_address_city
                       , base.rts_dest_hub_address_city
                       , base.rts_flag
                       , base.system_id
                       , {{ start_clock_type_conf[system_id] }} AS start_clock_type
                       , {{ rts_start_clock_type_conf[system_id] }} AS rts_start_clock_type
                FROM sla_reports_base AS base
                """,
                jinja_arguments={
                    "system_id": system_id,
                    "start_clock_type_conf": {
                        "id": "IF(base.parent_id = 849948, 'pickup', 'pickup_inbound')",
                        "my": """
                                case
                                    when base.reporting_name in ('lazada', 'shopee_malaysia')
                                    then 'dropoff_pickup_inbound'
                                    when base.reporting_name = 'tiktok' and date(base.creation_datetime) < date('2023-07-01')
                                    then 'dropoff_pickup_inbound'
                                    else 'pickup_inbound'
                                end
                                """,
                        "ph": """
                                IF(base.sales_channel = 'Cross Border'
                                    , 'holm_pickup_inbound'
                                    , 'pickup_inbound'
                                )
                                """,
                        "sg": "'pickup_inbound'",
                        "th": "'pickup_inbound'",
                        "vn": "IF(base.parent_id = 3272628, 'inbound', 'pickup_inbound')",
                    },
                    "rts_start_clock_type_conf": {
                        "id": """
                                CASE
                                    WHEN base.reporting_name = 'shopee'
                                        THEN IF(
                                            coalesce(least(base.pickup_datetime, base.inbound_datetime),
                                            base.creation_datetime
                                        )
                                            >= date('2022-07-18')
                                            , 'rts_trigger'
                                            , 'forward_leg_start_clock'
                                        )
                                    WHEN base.reporting_name = 'lazada'
                                        THEN IF(
                                            coalesce(least(base.pickup_datetime, base.inbound_datetime),
                                            base.creation_datetime
                                        )
                                            >= date('2022-12-21')
                                            , 'rts_trigger'
                                            , 'forward_leg_start_clock'
                                        )
                                    WHEN base.reporting_name in ('tokopedia', 'btpn') then 'rts_trigger'
                                    WHEN base.shipper_id in (8020488, 5469964, 5799192, 8255172)
                                        THEN 'rts_trigger'
                                    ELSE 'forward_leg_start_clock'
                                END
                                """,
                        "my": """
                                CASE
                                    WHEN base.reporting_name in ('lazada', 'shopee_malaysia')
                                    THEN 'rts_trigger'
                                    WHEN base.reporting_name = 'tiktok' and date(base.creation_datetime) < date('2023-07-01')
                                    THEN 'rts_trigger'
                                    ELSE 'forward_leg_start_clock'
                                END
                                """,
                        "ph": """
                                CASE
                                    WHEN base.reporting_name = 'lazada' and date(base.creation_datetime) < date('2023-11-01')
                                    THEN 'forward_leg_start_clock'
                                    WHEN base.reporting_name = 'tiktok' and date(base.creation_datetime) >= date('2024-08-01')
                                    THEN 'forward_leg_start_clock'
                                    ELSE 'rts_trigger'
                                END
                                """,
                        "sg": "'forward_leg_start_clock'",
                        "th": "'forward_leg_start_clock'",
                        "vn": """
                                IF(base.reporting_name in ('lazada', 'tiki'), 'forward_leg_start_clock', 'rts_trigger')
                                """,
                    },
                },
            ),
            base.TransformView(
                view_name="start_clock_calc_1",
                jinja_template="""
                SELECT base.*
                       , CASE
                            WHEN base.start_clock_type = 'pickup'
                            THEN IF(base.pickup_datetime > base.rts_trigger_datetime, NULL, base.pickup_datetime)
                            WHEN base.start_clock_type = 'inbound'
                            THEN IF(base.inbound_datetime > base.rts_trigger_datetime, NULL, base.inbound_datetime)
                            WHEN base.start_clock_type = 'pickup_inbound'
                            THEN
                                IF(least(base.pickup_datetime, base.inbound_datetime) > base.rts_trigger_datetime
                                   , NULL
                                   , least(base.pickup_datetime, base.inbound_datetime))
                            WHEN base.start_clock_type = 'dropoff_pickup_inbound'
                            THEN
                                IF(least(
                                    base.pickup_datetime, base.inbound_datetime, base.dp_dropoff_datetime
                                ) > base.rts_trigger_datetime
                                   , NULL
                                   , least(
                                    base.pickup_datetime, base.inbound_datetime, base.dp_dropoff_datetime
                                ))
                            WHEN base.start_clock_type = 'holm_pickup_inbound'
                            THEN
                                IF(least(
                                    base.pickup_datetime
                                    , base.inbound_datetime
                                    , base.dp_dropoff_datetime
                                    , xb_handed_over_to_last_mile_datetime
                                ) > base.rts_trigger_datetime
                                   , NULL
                                   , least(
                                    base.pickup_datetime, base.inbound_datetime, base.xb_handed_over_to_last_mile_datetime
                                ))
                         END AS start_clock_datetime
                       , CASE
                            WHEN base.start_clock_type = 'pickup'
                            THEN IF(base.pickup_datetime > base.rts_trigger_datetime, NULL, base.pickup_hub_id)
                            WHEN base.start_clock_type = 'inbound'
                            THEN IF(base.inbound_datetime > base.rts_trigger_datetime, NULL, base.inbound_hub_id)
                            WHEN base.start_clock_type in ('holm_pickup_inbound', 'pickup_inbound', 'dropoff_pickup_inbound')
                            THEN
                                CASE
                                    WHEN least(base.pickup_datetime, base.inbound_datetime) > base.rts_trigger_datetime
                                    THEN NULL
                                    WHEN base.inbound_datetime IS NULL THEN base.pickup_hub_id
                                    WHEN base.pickup_datetime IS NULL THEN base.inbound_hub_id
                                    WHEN base.pickup_datetime <= base.inbound_datetime THEN base.pickup_hub_id
                                    WHEN base.inbound_datetime < base.pickup_datetime THEN base.inbound_hub_id
                                END
                         END AS origin_hub_id
                       , base.rts_start_clock_type
                FROM start_clock_conf AS base
                """,
            ),
            base.TransformView(
                view_name="start_clock_calc_2",
                jinja_template="""
                SELECT base.*
                       , origin_hub.name AS origin_hub_name
                       , origin_hub.region AS origin_hub_region
                       , origin_hub.sla_region AS origin_hub_sla_region
                       , origin_hub.address_city AS origin_hub_address_city
                       , {{ start_clock_cutoff_conf[system_id] }} AS start_clock_cutoff
                FROM start_clock_calc_1 AS base
                LEFT JOIN hubs_enriched AS origin_hub
                ON base.origin_hub_id = origin_hub.id
                """,
                jinja_arguments={
                    "system_id": system_id,
                    "start_clock_cutoff_conf": {
                        "id": """
                            CASE
                                WHEN base.parent_id = 849948 THEN '21:15:00'
                                WHEN base.from_name IN ('LeL ID - Surabaya Bulky', 'LeL ID - Bulky WH')
                                     AND base.reporting_name = 'lazada'
                                     AND base.service_type IN ('EXPRESS', 'NEXTDAY', 'SAMEDAY')
                                THEN '13:00:00'
                                WHEN base.from_name IN ('LeL ID - Medan'
                                                         , 'LeL ID - Medan Bulky'
                                                         , 'LeL ID - Balikpapan'
                                                         , 'LeL ID - Makassar')
                                     AND base.reporting_name = 'lazada'
                                     AND base.service_type IN ('EXPRESS', 'NEXTDAY', 'SAMEDAY')
                                THEN '14:00:00'
                                WHEN base.from_name IN ('LeL ID - Main WH', 'LeL ID - CBD', 'LeL ID - Surabaya')
                                     AND base.reporting_name = 'lazada'
                                     AND base.service_type IN ('EXPRESS', 'NEXTDAY', 'SAMEDAY')
                                THEN '15:00:00'
                                WHEN base.reporting_name = 'lazada' THEN '22:00:00'
                                ELSE '23:59:59'
                            END
                            """,
                        "my": """
                            IF(base.reporting_name in ('tiktok', 'lazada', 'shopee_malaysia')
                                and base.start_clock_datetime = base.dp_dropoff_datetime
                                    , if(origin_hub.sla_region = 'Klang Valley', '16:59:59', '13:59:59')
                                    , '23:59:59'
                            )
                            """,
                        "ph": "'23:59:59'",
                        "sg": "'23:59:59'",
                        "th": "'23:59:59'",
                        "vn": "IF(base.reporting_name = 'tiki', '22:00:00', '23:59:59')",
                    },
                },
            ),
            base.TransformView(
                view_name="stop_clock_conf",
                jinja_template="""
                SELECT *
                       , {{ sc_conf[system_id] }} AS sc_type
                FROM start_clock_calc_2 AS base
                """,
                jinja_arguments={
                    "system_id": system_id,
                    "sc_conf": {
                        "id": "'1_attempt'",
                        "my": "'1_attempt'",
                        "ph": """
                            CASE
                                WHEN base.reporting_name in ('shopee') OR base.shipper_id = 3585384
                                THEN '2_attempt'
                                WHEN base.reporting_name = 'lazada' and date(start_clock_datetime) < date('2023-11-01')
                                THEN '2_attempt'
                                ELSE '1_attempt'
                            END
                            """,
                        "sg": "'1_attempt_timeslot'",
                        "th": "'1_attempt'",
                        "vn": "'1_attempt'",
                    },
                },
            ),
            base.TransformView(
                view_name="stop_clock_calc",
                jinja_template="""
                SELECT *
                       , CASE
                             WHEN base.sc_type = '1_attempt'
                             THEN least(base.first_valid_delivery_attempt_datetime, base.rts_trigger_datetime)
                             WHEN base.sc_type = '1_attempt_timeslot'
                             THEN base.first_valid_delivery_attempt_datetime
                             WHEN base.sc_type = '2_attempt'
                             THEN LEAST(IF(base.delivery_success_datetime < base.rts_trigger_datetime
                                           OR base.rts_trigger_datetime IS NULL
                                           , base.delivery_success_datetime
                                           , NULL)
                                        , base.second_valid_delivery_attempt_datetime
                                        , base.rts_trigger_datetime)
                         END AS sc_datetime
                       , IF(base.rts_flag = 0, base.delivery_success_datetime, NULL) AS nc_datetime
                       , IF(base.rts_flag = 1, base.delivery_success_datetime, NULL) AS rts_nc_datetime
                FROM stop_clock_conf AS base
                """,
            ),
            base.TransformView(
                view_name="custom_mapping",
                jinja_template="""
                {%- if system_id == 'my' %}
                SELECT base.*
                       , origin.custom_class custom_origin
                       , dest.custom_class custom_dest
                FROM stop_clock_calc AS base
                LEFT JOIN custom_mapping_my origin
                    on base.from_postcode = origin.mapped_value
                    and date(base.start_clock_datetime) >= date(origin.start_date)
                    and date(base.start_clock_datetime) < date(origin.end_date)
                LEFT JOIN custom_mapping_my dest
                    on base.dest_postcode = dest.mapped_value
                    and date(base.start_clock_datetime) >= date(dest.start_date)
                    and date(base.start_clock_datetime) < date(dest.end_date)
                {%- else %}
                SELECT base.*
                       , cast(null as string) custom_origin
                       , cast(null as string) custom_dest
                FROM stop_clock_calc AS base
                {%- endif %}
                """,
                jinja_arguments={
                    "system_id": system_id,
                },
            ),
            base.TransformView(
                view_name="sla_date_conf",
                jinja_template="""
                SELECT distinct base.*
                {%- for col in shipper_sla_days_column_fetch %}
                       , coalesce(
                    {%- for type in shipper_sla_days_joins[system_id].keys() %}
                                    sla_{{ type }}.{{ col }}{% if not loop.last %}, {% endif %}
                    {% endfor %}
                                 ) AS {{ col }}
                {% endfor %}
                       , sla_hardcoded.sla_date AS hardcoded_sla_date
                       , coalesce(fwd_extensions.extension_days, 0) AS extension_days
                       , fwd_extensions.extension_reason
                       , coalesce(rts_extensions.extension_days, 0) AS rts_extension_days
                       , rts_extensions.extension_reason AS rts_extension_reason
                       , IF(base.sc_type = '1_attempt_timeslot'
                            , base.first_valid_delivery_attempt_timeslot_start
                            , NULL) AS sla_timeslot_start
                       , IF(base.sc_type = '1_attempt_timeslot'
                            , base.first_valid_delivery_attempt_timeslot_end
                            , NULL) AS sla_timeslot_end
                FROM custom_mapping AS base
                LEFT JOIN sla_extensions AS fwd_extensions ON fwd_extensions.tracking_id = base.tracking_id
                    AND fwd_extensions.delivery_leg = 'delivery'
                LEFT JOIN sla_extensions AS rts_extensions ON rts_extensions.tracking_id = base.tracking_id
                    AND rts_extensions.delivery_leg = 'rts'
                LEFT JOIN sla_hardcoded ON sla_hardcoded.tracking_id = base.tracking_id
                    AND sla_hardcoded.country = base.system_id
                {%- for type, configs in shipper_sla_days_joins[system_id].items() %}
                LEFT JOIN shipper_sla_days AS sla_{{ type }} ON sla_{{ type }}.sla_type = 'service'
                    AND date(sla_{{ type }}.start_datetime) <= date(base.start_clock_datetime)
                    AND date(sla_{{ type }}.end_datetime) > date(base.start_clock_datetime)
                {%- for config, value in configs.items() %}
                {%- if value %}
                    AND sla_{{ type }}.{{ config }} = {{ value }}
                {%- else %}
                    AND sla_{{ type }}.{{ config }} IS NULL
                {%- endif %}
                {%- endfor %}
                {%- endfor %}
                """,
                jinja_arguments={
                    "system_id": system_id,
                    "shipper_sla_days_column_fetch": (
                        "sla_days",
                        "rts_sla_days",
                        "calendar_config",
                        "shipper_config_type",
                        "origin_dest_config_type",
                        "extra_config_type",
                    ),
                    "shipper_sla_days_joins": {
                        "id": {
                            "tokopedia_l2l2": {
                                "shipper_config_type": "'reporting_name'",
                                "shipper_config": "base.reporting_name AND base.reporting_name = 'tokopedia'",
                                "origin_dest_config_type": "'l2l2'",
                                "origin_config": "base.from_l2_id",
                                "dest_config": "base.to_l2_id",
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                            "shopee_l2l2": {
                                "shipper_config_type": "'reporting_name'",
                                "shipper_config": "base.reporting_name AND base.reporting_name = 'shopee'",
                                "origin_dest_config_type": "'l2l2'",
                                "origin_config": "base.from_l2_id",
                                "dest_config": "base.to_l2_id",
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                            "lazada_l1l3": {
                                "shipper_config_type": "'reporting_name'",
                                "shipper_config": "base.reporting_name AND base.reporting_name = 'lazada'",
                                "origin_dest_config_type": "'l1l3'",
                                "origin_config": "base.from_l1_id",
                                "dest_config": "base.to_l3_id",
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                            "btpn_l1l3": {
                                "shipper_config_type": "'reporting_name'",
                                "shipper_config": "base.reporting_name AND base.reporting_name = 'btpn'",
                                "origin_dest_config_type": "'l1l3'",
                                "origin_config": "base.from_l1_id",
                                "dest_config": "base.to_l3_id",
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                            "shipper_id_l1l3": {
                                "shipper_config_type": "'shipper_id'",
                                "shipper_config": "base.shipper_id",
                                "origin_dest_config_type": "'l1l3'",
                                "origin_config": "base.from_l1_id",
                                "dest_config": "base.to_l3_id",
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                            "l2l3": {
                                "shipper_config_type": "'default'",
                                "shipper_config": None,
                                "origin_dest_config_type": "'l2l3'",
                                "origin_config": "base.from_l2_id",
                                "dest_config": "base.to_l3_id",
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                            "billing_zone": {
                                "shipper_config_type": "'default'",
                                "shipper_config": None,
                                "origin_dest_config_type": "'billing_zone'",
                                "origin_config": "base.from_billing_zone",
                                "dest_config": "base.to_billing_zone",
                                "extra_config_type": "'service_type'",
                                "extra_config": "base.service_type",
                            },
                        },
                        "my": {
                            "shopee_malaysia_custom": {
                                "shipper_config_type": "'reporting_name'",
                                "shipper_config": "base.reporting_name AND base.reporting_name = 'shopee_malaysia'",
                                "origin_dest_config_type": "'custom_mapping'",
                                "origin_config": "base.custom_origin",
                                "dest_config": "base.custom_dest",
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                            "tiktok_state": {
                                "shipper_config_type": "'reporting_name'",
                                "shipper_config": "base.reporting_name AND base.reporting_name = 'tiktok'",
                                "origin_dest_config_type": "'state'",
                                "origin_config": "base.origin_hub_address_city",
                                "dest_config": "base.dest_hub_address_city",
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                            "lazada": {
                                "shipper_config_type": "'reporting_name'",
                                "shipper_config": "base.reporting_name AND base.reporting_name = 'lazada'",
                                "origin_dest_config_type": "'region'",
                                "origin_config": "base.origin_hub_sla_region",
                                "dest_config": "base.dest_hub_sla_region",
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                            "default": {
                                "shipper_config_type": "'default'",
                                "shipper_config": None,
                                "origin_dest_config_type": "'billing_zone'",
                                "origin_config": "base.from_billing_zone",
                                "dest_config": "base.to_billing_zone",
                                "extra_config_type": "'service_type'",
                                "extra_config": "base.service_type",
                            },
                        },
                        "ph": {
                            "shopee": {
                                "shipper_config_type": "'reporting_name'",
                                "shipper_config": "base.reporting_name AND base.reporting_name = 'shopee'",
                                "origin_dest_config_type": "'region'",
                                "origin_config": "base.origin_hub_sla_region",
                                "dest_config": "base.dest_hub_sla_region",
                                "extra_config_type": "'TID'",
                                "extra_config": "left(base.tracking_id, 3)",
                            },
                            "shopee_express": {
                                "shipper_config_type": "'shipper_id'",
                                "shipper_config": "base.shipper_id AND base.shipper_id = 3585384",
                                "origin_dest_config_type": "'region'",
                                "origin_config": "base.origin_hub_sla_region",
                                "dest_config": "base.dest_hub_sla_region",
                                "extra_config_type": "'TID'",
                                "extra_config": "left(base.tracking_id, 3)",
                            },
                            "shopee_province": {
                                "shipper_config_type": "'reporting_name'",
                                "shipper_config": "base.reporting_name AND base.reporting_name = 'shopee'",
                                "origin_dest_config_type": "'province'",
                                "origin_config": "base.origin_hub_address_city",
                                "dest_config": "base.dest_hub_address_city",
                                "extra_config_type": "'TID'",
                                "extra_config": "left(base.tracking_id, 3)",
                            },
                            "lazada": {
                                "shipper_config_type": "'reporting_name'",
                                "shipper_config": "base.reporting_name AND base.reporting_name = 'lazada'",
                                "origin_dest_config_type": "'region'",
                                "origin_config": "base.origin_hub_sla_region",
                                "dest_config": "base.dest_hub_sla_region",
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                            "lazada_province": {
                                "shipper_config_type": "'reporting_name'",
                                "shipper_config": "base.reporting_name AND base.reporting_name = 'lazada'",
                                "origin_dest_config_type": "'province'",
                                "origin_config": "base.origin_hub_address_city",
                                "dest_config": "base.dest_hub_address_city",
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                            "lazada_billing_zone": {
                                "shipper_config_type": "'reporting_name'",
                                "shipper_config": "base.reporting_name AND base.reporting_name = 'lazada'",
                                "origin_dest_config_type": "'billing_zone'",
                                "origin_config": "base.from_billing_zone",
                                "dest_config": "base.to_billing_zone",
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                            "tiktok_province": {
                                "shipper_config_type": "'reporting_name'",
                                "shipper_config": "base.reporting_name AND base.reporting_name = 'tiktok'",
                                "origin_dest_config_type": "'province'",
                                "origin_config": "base.origin_hub_address_city",
                                "dest_config": "base.dest_hub_address_city",
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                            "zalora_province": {
                                "shipper_config_type": "'reporting_name'",
                                "shipper_config": "base.reporting_name AND base.reporting_name = 'zalora'",
                                "origin_dest_config_type": "'province'",
                                "origin_config": "base.origin_hub_address_city",
                                "dest_config": "base.dest_hub_address_city",
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                            "shipper_id": {
                                "shipper_config_type": "'shipper_id'",
                                "shipper_config": "base.shipper_id",
                                "origin_dest_config_type": "'province'",
                                "origin_config": "base.origin_hub_address_city",
                                "dest_config": "base.dest_hub_address_city",
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                            "sales_channel": {
                                "shipper_config_type": "'sales_channel'",
                                "shipper_config": "base.sales_channel",
                                "origin_dest_config_type": "'province'",
                                "origin_config": "base.origin_hub_address_city",
                                "dest_config": "base.dest_hub_address_city",
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                            "default_billing_zone": {
                                "shipper_config_type": "'default'",
                                "origin_dest_config_type": "'billing_zone'",
                                "origin_config": "base.from_billing_zone",
                                "dest_config": "base.to_billing_zone",
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                            "default": {
                                "shipper_config_type": "'default'",
                                "shipper_config": None,
                                "origin_dest_config_type": "'billing_zone'",
                                "origin_config": None,
                                "dest_config": None,
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                        },
                        "sg": {
                            "default": {
                                "shipper_config_type": "'default'",
                                "shipper_config": None,
                                "origin_dest_config_type": None,
                                "origin_config": None,
                                "dest_config": None,
                                "extra_config_type": "'service_type'",
                                "extra_config": "base.service_type",
                            }
                        },
                        "th": {
                            "default_billing_zone": {
                                "shipper_config_type": "'default'",
                                "shipper_config": None,
                                "origin_dest_config_type": "'billing_zone'",
                                "origin_config": "base.from_billing_zone",
                                "dest_config": "base.to_billing_zone",
                                "extra_config_type": "'service_type'",
                                "extra_config": "base.service_type",
                            },
                            "default_province": {
                                "shipper_config_type": "'default'",
                                "shipper_config": None,
                                "origin_dest_config_type": "'province'",
                                "origin_config": "base.origin_hub_address_city",
                                "dest_config": "base.dest_hub_address_city",
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                        },
                        "vn": {
                            "lazada_tid_billing_zone": {
                                "shipper_config_type": "'parent_id'",
                                "shipper_config": "base.parent_id AND base.parent_id = 341167",
                                "origin_dest_config_type": "'billing_zone'",
                                "origin_config": "base.from_billing_zone",
                                "dest_config": "base.to_billing_zone",
                                "extra_config_type": "'TID'",
                                "extra_config": "left(base.tracking_id, 6)",
                            },
                            "parent_id_region": {
                                "shipper_config_type": "'parent_id'",
                                "shipper_config": "base.parent_id",
                                "origin_dest_config_type": "'region'",
                                "origin_config": "base.origin_hub_sla_region",
                                "dest_config": "base.dest_hub_sla_region",
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                            "parent_id_billing_zone": {
                                "shipper_config_type": "'parent_id'",
                                "shipper_config": "base.parent_id",
                                "origin_dest_config_type": "'billing_zone'",
                                "origin_config": "base.from_billing_zone",
                                "dest_config": "base.to_billing_zone",
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                            "shipper_id_billing_zone": {
                                "shipper_config_type": "'shipper_id'",
                                "shipper_config": "base.shipper_id",
                                "origin_dest_config_type": "'billing_zone'",
                                "origin_config": "base.from_billing_zone",
                                "dest_config": "base.to_billing_zone",
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                            "default_region": {
                                "shipper_config_type": "'default'",
                                "shipper_config": None,
                                "origin_dest_config_type": "'region'",
                                "origin_config": "base.origin_hub_sla_region",
                                "dest_config": "base.dest_hub_sla_region",
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                            "default_billing_zone_sameday": {
                                "shipper_config_type": "'default'",
                                "shipper_config": None,
                                "origin_dest_config_type": "'billing_zone'",
                                "origin_config": "base.from_billing_zone",
                                "dest_config": "base.to_billing_zone",
                                "extra_config_type": "'service_type'",
                                "extra_config": "base.service_type",
                            },
                            "default_billing_zone": {
                                "shipper_config_type": "'default'",
                                "shipper_config": None,
                                "origin_dest_config_type": "'billing_zone'",
                                "origin_config": "base.from_billing_zone",
                                "dest_config": "base.to_billing_zone",
                                "extra_config_type": None,
                                "extra_config": None,
                            },
                        },
                    },
                },
            ),
            base.TransformView(
                view_name="start_clock_calc_3",
                jinja_template="""
                SELECT base.*
                       , CASE
                           WHEN base.calendar_config = 'calendar_days' THEN
                               IF(date_format(base.start_clock_datetime, 'HH:mm:ss') > base.start_clock_cutoff
                                    , date(base.start_clock_datetime + interval '1' day)
                                    , date(base.start_clock_datetime))
                           ELSE
                               IF(date_format(base.start_clock_datetime, 'HH:mm:ss') > base.start_clock_cutoff
                                    , coalesce(c_start_dt_reg.next_working_day_1, c_start_dt_nat.next_working_day_1)
                                    , date(base.start_clock_datetime))
                        END AS start_clock_date
                FROM sla_date_conf AS base
                LEFT JOIN calendar AS c_start_dt_nat on c_start_dt_nat.date = date(base.start_clock_datetime)
                    AND c_start_dt_nat.region = 'national'
                LEFT JOIN calendar AS c_start_dt_reg on c_start_dt_reg.date = date(base.start_clock_datetime)
                    AND c_start_dt_reg.region = base.origin_hub_address_city
                    AND c_start_dt_reg.system_id = 'my'
                """,
            ),
            base.TransformView(
                view_name="start_clock_calc_4",
                jinja_template="""
                SELECT base.*
                       , CASE
                             WHEN base.rts_start_clock_type = 'forward_leg_start_clock' THEN base.start_clock_date
                             WHEN base.rts_start_clock_type = 'rts_trigger' THEN date(base.rts_trigger_datetime)
                         END AS rts_start_clock_date
                FROM start_clock_calc_3 AS base
                """,
            ),
            base.TransformView(
                view_name="sla_date_calc",
                jinja_template="""
                SELECT *
                       , CASE
                             WHEN base.calendar_config = 'calendar_days'
                             THEN date_add(start_clock_date, cast(base.sla_days as int))
                {%- for day in range(max_sla_days + 1) %}
                             WHEN base.sla_days = {{ day }}
                             THEN coalesce(c_start_reg.next_working_day_{{ day }}
                                           , c_start_nat.next_working_day_{{ day }})
                {%- endfor %}
                         END AS base_sla_date
                       , CASE
                             WHEN base.hardcoded_sla_date IS NOT NULL THEN base.hardcoded_sla_date
                             WHEN base.calendar_config = 'calendar_days'
                             THEN date_add(
                                start_clock_date, cast(base.sla_days as int) + CAST(base.extension_days AS int)
                            )
                {%- for day in range(max_sla_days + 1) %}
                             WHEN base.sla_days + base.extension_days = {{ day }}
                             THEN coalesce(c_start_reg.next_working_day_{{ day }}
                                           , c_start_nat.next_working_day_{{ day }})
                {%- endfor %}
                         END AS sla_date
                       , CASE
                             WHEN base.calendar_config = 'calendar_days'
                             THEN date_add(rts_start_clock_date, cast(base.rts_sla_days as int))
                {%- for day in range(max_sla_days + 1) %}
                             WHEN base.rts_sla_days = {{ day }}
                             THEN coalesce(c_rts_start_reg.next_working_day_{{ day }}
                                           , c_rts_start_nat.next_working_day_{{ day }})
                {%- endfor %}
                         END AS rts_base_sla_date
                       , CASE
                             WHEN base.calendar_config = 'calendar_days'
                             THEN date_add(
                                rts_start_clock_date
                                , cast(base.rts_sla_days as int) + CAST(base.rts_extension_days AS int)
                            )
                {%- for day in range(max_sla_days + 1) %}
                             WHEN base.rts_sla_days + base.rts_extension_days = {{ day }}
                             THEN coalesce(c_rts_start_reg.next_working_day_{{ day }}
                                           , c_rts_start_nat.next_working_day_{{ day }})
                {%- endfor %}
                         END AS rts_sla_date
                FROM start_clock_calc_4 AS base
                LEFT JOIN calendar AS c_start_nat ON c_start_nat.date = base.start_clock_date
                    AND c_start_nat.region = 'national'
                LEFT JOIN calendar AS c_start_reg ON c_start_reg.date = base.start_clock_date
                    AND c_start_reg.region = base.dest_hub_address_city
                    AND c_start_reg.system_id = 'my'
                LEFT JOIN calendar AS c_rts_start_nat ON c_rts_start_nat.date = base.rts_start_clock_date
                    AND c_rts_start_nat.region = 'national'
                LEFT JOIN calendar AS c_rts_start_reg ON c_rts_start_reg.date = base.rts_start_clock_date
                    AND c_rts_start_reg.region = base.rts_dest_hub_address_city
                    AND c_rts_start_reg.system_id = 'my'
                """,
                jinja_arguments={"max_sla_days": 90},
            ),
            base.TransformView(
                view_name="kpi_measured_calc",
                jinja_template="""
                SELECT *
                       , IF(base.sla_date IS NOT NULL
                            AND base.sla_date < date('{{ measurement_datetime_local }}')
                            AND base.third_party_tracking_id IS NULL, 1, 0) AS sc_measured
                       , IF(base.sla_date IS NOT NULL
                            AND base.sla_date < date('{{ measurement_datetime_local }}')
                            AND base.third_party_tracking_id IS NULL
                            AND base.rts_flag = 0, 1, 0) AS nc_measured
                       , IF(base.rts_sla_date IS NOT NULL
                            AND base.rts_sla_date < date('{{ measurement_datetime_local }}')
                            AND base.rts_flag = 1
                            AND base.third_party_tracking_id IS NULL, 1, 0) AS rts_nc_measured
                FROM sla_date_calc AS base
                """,
                jinja_arguments={
                    "measurement_datetime_local": measurement_datetime.in_tz(getattr(date.Timezone, system_id.upper()))
                },
            ),
            base.TransformView(
                view_name="kpi_met_calc",
                jinja_template="""
                SELECT *
                       , CASE
                             WHEN base.sc_measured = 0 THEN NULL
                             WHEN base.sc_type <> '1_attempt_timeslot'
                                  AND date(base.sc_datetime) <= base.sla_date THEN 1
                             WHEN base.sc_type = '1_attempt_timeslot'
                                  AND date(base.sc_datetime) <= base.sla_date
                                  AND date_format(base.sc_datetime, 'HH:mm:ss') >= base.sla_timeslot_start
                                  AND date_format(base.sc_datetime, 'HH:mm:ss') <= base.sla_timeslot_end
                             THEN 1
                             WHEN base.sc_type = '1_attempt_timeslot'
                                  AND base.sc_datetime IS NULL
                                  AND date(base.rts_trigger_datetime) <= base.sla_date
                             THEN 1
                             ELSE 0
                        END AS sc_met
                       , CASE
                             WHEN base.nc_measured = 0 THEN NULL
                             WHEN date(base.nc_datetime) <= base.sla_date THEN 1
                             ELSE 0
                        END AS nc_met
                       , CASE
                             WHEN base.rts_nc_measured = 0 THEN NULL
                             WHEN date(base.rts_nc_datetime) <= base.rts_sla_date THEN 1
                             ELSE 0
                         END AS rts_nc_met
                FROM kpi_measured_calc AS base
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                SELECT base.country
                       , base.order_id
                       , base.tracking_id
                       , base.third_party_tracking_id
                       , base.shipper_id
                       , base.shipper_name
                       , base.parent_id
                       , base.parent_name
                       , base.parent_id_coalesce
                       , base.parent_name_coalesce
                       , base.sales_channel
                       , base.reporting_name
                       , base.business_unit
                       , base.acquisition_endpoint
                       , base.marketplace
                       , base.shipper_order_type
                       , base.cod_flag
                       , base.from_postcode
                       , base.to_postcode
                       , base.dest_postcode
                       , base.from_billing_zone
                       , base.to_billing_zone
                       {%- for level in ('l1', 'l2', 'l3') %}
                       , base.from_{{ level }}_id
                       , base.from_{{ level }}_name
                       , base.to_{{ level }}_id
                       , base.to_{{ level }}_name
                       {%- endfor %}
                       , base.from_name
                       , base.service_type
                       , base.delivery_type
                       , base.parcel_size
                       , base.nv_length
                       , base.nv_width
                       , base.nv_height
                       , base.nv_weight
                       , base.weight
                       , base.granular_status
                       , base.creation_datetime
                       , sla_calc.origin_hub_id
                       , sla_calc.origin_hub_name
                       , sla_calc.origin_hub_region
                       , sla_calc.origin_hub_sla_region
                       , sla_calc.origin_hub_address_city
                       , sla_calc.custom_origin
                       , base.dest_hub_id
                       , base.dest_hub_name
                       , base.dest_hub_region
                       , base.dest_hub_sla_region
                       , base.dest_hub_address_city
                       , sla_calc.custom_dest
                       , base.dest_zone
                       , base.dp_dropoff_dp_id
                       , base.dp_dropoff_dp_name
                       , base.dp_dropoff_datetime
                       , base.xb_handed_over_to_last_mile_datetime
                       , base.pickup_hub_id
                       , base.pickup_hub_name
                       , base.pickup_hub_region
                       , base.pickup_datetime
                       , base.nv_pickup_datetime
                       , base.inbound_hub_id
                       , base.inbound_hub_name
                       , base.inbound_hub_region
                       , base.inbound_datetime
                       , base.inbound_type
                       , base.fourth_party_partner_name
                       , base.fourth_party_handover_time
                       , base.fourth_party_handover_type
                       , base.delivery_attempts
                       , base.first_valid_delivery_attempt_datetime
                       , base.first_valid_delivery_failure_reason
                       , base.second_valid_delivery_attempt_datetime
                       , base.second_valid_delivery_failure_reason
                       , base.third_valid_delivery_attempt_datetime
                       , base.third_valid_delivery_failure_reason
                       , base.last_valid_delivery_attempt_datetime
                       , base.last_valid_delivery_failure_reason
                       , base.rts_flag
                       , base.rts_reason
                       , base.rts_origin_hub_id
                       , base.rts_origin_hub_name
                       , base.rts_origin_hub_region
                       , base.rts_dest_hub_id
                       , base.rts_dest_hub_name
                       , base.rts_dest_hub_region
                       , base.rts_dest_hub_address_city
                       , base.rts_dest_zone
                       , base.rts_trigger_datetime
                       , base.rts_dest_hub_inbound_datetime
                       , base.first_valid_rts_attempt_datetime
                       , base.rts_attempts
                       , base.delivery_success_datetime
                       , sla_calc.start_clock_type
                       , sla_calc.start_clock_datetime
                       , sla_calc.start_clock_cutoff
                       , sla_calc.start_clock_date
                       , sla_calc.rts_start_clock_type
                       , sla_calc.rts_start_clock_date
                       , concat(
                           coalesce(sla_calc.shipper_config_type, '')
                           , '-'
                           , coalesce(sla_calc.origin_dest_config_type, '')
                           , '-'
                           , coalesce(sla_calc.extra_config_type, '')
                       ) as sla_config
                       , sla_calc.sla_days
                       , sla_calc.base_sla_date
                       , sla_calc.extension_days
                       , sla_calc.extension_reason
                       , sla_calc.sla_date
                       , c_sla.day AS sla_date_dow
                       , sla_calc.sla_timeslot_start
                       , sla_calc.sla_timeslot_end
                       , sla_calc.sc_type
                       , sla_calc.sc_datetime
                       , sla_calc.nc_datetime
                       , sla_calc.sc_measured
                       , sla_calc.sc_met
                       , sla_calc.nc_measured
                       , sla_calc.nc_met
                       , sla_calc.rts_sla_days
                       , sla_calc.rts_sla_date
                       , sla_calc.rts_extension_days
                       , sla_calc.rts_extension_reason
                       , sla_calc.rts_nc_datetime
                       , sla_calc.rts_base_sla_date
                       , c_rts_sla.day AS rts_sla_date_dow
                       , sla_calc.rts_nc_measured
                       , sla_calc.rts_nc_met
                       , base.is_pickup_required

                {%- for name, ref_dates in dest_hub_datediff_cols.items() %}
                       , CASE
                           WHEN {{ calendar_joins[ref_dates[0]] }} is null
                               or {{ calendar_joins[ref_dates[1]] }} is null
                           THEN null
                           WHEN sla_calc.calendar_config = 'calendar_days'
                           THEN greatest(
                               datediff({{ calendar_joins[ref_dates[1]] }}, {{ calendar_joins[ref_dates[0]] }})
                               , 0
                           )
                           ELSE
                {%- if system_id == 'my' %}
                       greatest(
                           coalesce({{ ref_dates[1] }}_reg.working_day_cum - {{ ref_dates[0] }}_reg.working_day_cum
                                  , {{ ref_dates[1] }}.working_day_cum - {{ ref_dates[0] }}.working_day_cum)
                            , 0
                       )
                {%- else %}
                       greatest({{ ref_dates[1] }}.working_day_cum - {{ ref_dates[0] }}.working_day_cum, 0)
                {%- endif %}
                        END AS {{ name }}
                {%- endfor %}

                {%- for name, ref_dates in rts_dest_hub_datediff_cols.items() %}
                       , CASE
                           WHEN {{ calendar_joins[ref_dates[0]] }} is null
                               or {{ calendar_joins[ref_dates[1]] }} is null
                           THEN null
                           WHEN sla_calc.calendar_config = 'calendar_days'
                               THEN greatest(
                                   datediff({{ calendar_joins[ref_dates[1]] }}, {{ calendar_joins[ref_dates[0]] }})
                                   , 0
                               )
                           ELSE
                {%- if system_id == 'my' %}
                       greatest(
                            coalesce({{ ref_dates[1] }}_rts_reg.working_day_cum
                                  - {{ ref_dates[0] }}_rts_reg.working_day_cum
                                  , {{ ref_dates[1] }}.working_day_cum - {{ ref_dates[0] }}.working_day_cum)
                            , 0
                       )
                {%- else %}
                       greatest({{ ref_dates[1] }}.working_day_cum - {{ ref_dates[0] }}.working_day_cum, 0)
                {%- endif %}
                        END AS {{ name }}
                {%- endfor %}

                       , base.created_month
                FROM sla_reports_base AS base
                LEFT JOIN kpi_met_calc AS sla_calc ON sla_calc.order_id = base.order_id
                {%- for alias, definition in calendar_joins.items() %}
                LEFT JOIN calendar AS {{ alias }} on {{ alias }}.date = {{ definition }}
                    AND {{ alias }}.region = 'national'
                {%- if system_id == 'my' %}
                {%- if alias in dest_hub_datediff_cols.values() | sum(start = ()) %}
                LEFT JOIN calendar AS {{ alias }}_reg ON {{ alias }}_reg.date = {{ definition }}
                    AND {{ alias }}_reg.region = base.dest_hub_address_city
                {%- endif %}
                {%- if alias in rts_dest_hub_datediff_cols.values() | sum(start = ()) %}
                LEFT JOIN calendar AS {{ alias }}_rts_reg ON {{ alias }}_rts_reg.date = {{ definition }}
                    AND {{ alias }}_rts_reg.region = base.rts_dest_hub_address_city
                {%- endif %}
                {%- endif %}
                {%- endfor %}
                group by {{ range(1, 145) | join(',') }}
                """,
                jinja_arguments={
                    "system_id": system_id,
                    "calendar_joins": {
                        "c_start": "sla_calc.start_clock_date",
                        "c_first_attempt": "date(base.first_valid_delivery_attempt_datetime)",
                        "c_second_attempt": "date(base.second_valid_delivery_attempt_datetime)",
                        "c_third_attempt": "date(base.third_valid_delivery_attempt_datetime)",
                        "c_last_attempt": "date(base.last_valid_delivery_attempt_datetime)",
                        "c_rts": "date(base.rts_trigger_datetime)",
                        "c_success": "date(base.delivery_success_datetime)",
                        "c_rts_attempt": "date(base.first_valid_rts_attempt_datetime)",
                        "c_rts_start": "sla_calc.rts_start_clock_date",
                        "c_sc": "date(sla_calc.sc_datetime)",
                        "c_nc": "date(sla_calc.nc_datetime)",
                        "c_rts_nc": "date(sla_calc.rts_nc_datetime)",
                        "c_sla": "sla_calc.sla_date",
                        "c_rts_sla": "sla_calc.rts_sla_date",
                    },
                    "dest_hub_datediff_cols": {
                        "days_to_first_valid_delivery_attempt": ("c_start", "c_first_attempt"),
                        "days_to_second_valid_delivery_attempt": ("c_start", "c_second_attempt"),
                        "days_to_third_valid_delivery_attempt": ("c_start", "c_third_attempt"),
                        "days_to_last_valid_delivery_attempt": ("c_start", "c_last_attempt"),
                        "last_attempt_to_rts_trigger": ("c_last_attempt", "c_rts"),
                        "days_to_rts_trigger": ("c_start", "c_rts"),
                        "days_to_delivery_success": ("c_start", "c_success"),
                        "days_to_sc": ("c_start", "c_sc"),
                        "days_to_nc": ("c_start", "c_nc"),
                    },
                    "rts_dest_hub_datediff_cols": {
                        "rts_trigger_to_first_rts_attempt": ("c_rts", "c_rts_attempt"),
                        "rts_trigger_to_delivery_success": ("c_rts", "c_success"),
                        "days_to_rts_nc": ("c_rts_start", "c_rts_nc"),
                    },
                },
            ),
            base.TransformView(
                view_name="final",
                jinja_template="""
                        select
                            country
                            , order_id
                            , tracking_id
                            , third_party_tracking_id
                            , shipper_id
                            , shipper_name
                            , parent_id
                            , parent_name
                            , parent_id_coalesce
                            , parent_name_coalesce
                            , sales_channel
                            , reporting_name
                            , business_unit
                            , acquisition_endpoint
                            , marketplace
                            , shipper_order_type
                            , cod_flag
                            , from_postcode
                            , to_postcode
                            , dest_postcode
                            , from_billing_zone
                            , to_billing_zone
                            , from_l1_id
                            , from_l1_name
                            , to_l1_id
                            , to_l1_name
                            , from_l2_id
                            , from_l2_name
                            , to_l2_id
                            , to_l2_name
                            , from_l3_id
                            , from_l3_name
                            , to_l3_id
                            , to_l3_name
                            , from_name
                            , service_type
                            , delivery_type
                            , parcel_size
                            , nv_length
                            , nv_width
                            , nv_height
                            , nv_weight
                            , weight
                            , granular_status
                            , creation_datetime
                            , origin_hub_id
                            , origin_hub_name
                            , origin_hub_region
                            , origin_hub_sla_region
                            , origin_hub_address_city
                            , custom_origin
                            , dest_hub_id
                            , dest_hub_name
                            , dest_hub_region
                            , dest_hub_sla_region
                            , dest_hub_address_city
                            , custom_dest
                            , dest_zone
                            , dp_dropoff_dp_id
                            , dp_dropoff_dp_name
                            , dp_dropoff_datetime
                            , xb_handed_over_to_last_mile_datetime
                            , pickup_hub_id
                            , pickup_hub_name
                            , pickup_hub_region
                            , pickup_datetime
                            , nv_pickup_datetime
                            , inbound_hub_id
                            , inbound_hub_name
                            , inbound_hub_region
                            , inbound_datetime
                            , inbound_type
                            , fourth_party_partner_name
                            , fourth_party_handover_time
                            , fourth_party_handover_type
                            , delivery_attempts
                            , first_valid_delivery_attempt_datetime
                            , first_valid_delivery_failure_reason
                            , second_valid_delivery_attempt_datetime
                            , second_valid_delivery_failure_reason
                            , third_valid_delivery_attempt_datetime
                            , third_valid_delivery_failure_reason
                            , last_valid_delivery_attempt_datetime
                            , last_valid_delivery_failure_reason
                            , rts_flag
                            , rts_reason
                            , rts_origin_hub_id
                            , rts_origin_hub_name
                            , rts_origin_hub_region
                            , rts_dest_hub_id
                            , rts_dest_hub_name
                            , rts_dest_hub_region
                            , rts_dest_hub_address_city
                            , rts_dest_zone
                            , rts_trigger_datetime
                            , rts_dest_hub_inbound_datetime
                            , first_valid_rts_attempt_datetime
                            , rts_attempts
                            , delivery_success_datetime
                            , start_clock_type
                            , start_clock_datetime
                            , start_clock_cutoff
                            , start_clock_date
                            , rts_start_clock_type
                            , rts_start_clock_date
                            , sla_config
                            , sla_days
                            , base_sla_date
                            , extension_days
                            , extension_reason
                            , sla_date
                            , sla_date_dow
                            , sla_timeslot_start
                            , sla_timeslot_end
                            , sc_type
                            , sc_datetime
                            , nc_datetime
                            , sc_measured
                            , sc_met
                            , nc_measured
                            , nc_met
                            , rts_sla_days
                            , rts_sla_date
                            , rts_extension_days
                            , rts_extension_reason
                            , rts_nc_datetime
                            , rts_base_sla_date
                            , rts_sla_date_dow
                            , rts_nc_measured
                            , rts_nc_met
                            , is_pickup_required
                            , days_to_first_valid_delivery_attempt
                            , days_to_second_valid_delivery_attempt
                            , days_to_third_valid_delivery_attempt
                            , days_to_last_valid_delivery_attempt
                            , last_attempt_to_rts_trigger
                            , days_to_rts_trigger
                            , days_to_delivery_success
                            , days_to_sc
                            , days_to_nc
                            , rts_trigger_to_first_rts_attempt
                            , rts_trigger_to_delivery_success
                            , days_to_rts_nc
                            , created_month
                        from final_view
                        """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).TRANSIT_TIME_REPORT,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.config("spark.sql.analyzer.maxIterations", "200").getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
