# Data dictionary

table_name: warehouse_scan_events

overview:
  purpose: Tracks all warehouse-related scanning events for orders, including inbound scans at sorting hubs and warehouse sweeps. This table helps monitor order movement and processing within warehouses and hubs.
  granularity: Each row represents a single warehouse scanning event (either an inbound scan or warehouse sweep) for a specific order.
  business_rules:
    - Only includes inbound scans of type 2 (SORTING_HUB)
    - Excludes records where order_id is NULL
    - Excludes soft-deleted records (where deleted_at is not NULL)
    - Combines both route-based inbound scans and global inbound scans
    - Includes warehouse sweeps as a separate type of scanning event

input_tables:
  - core_prod_gl.inbound_scans
  - core_prod_gl.warehouse_sweeps
  - route_prod_gl.route_logs

fields:
  - name: scan_id
    description: Unique identifier for the scan event
    source: core_prod_gl.inbound_scans.id, core_prod_gl.warehouse_sweeps.id
    technical_description: "Direct field from source tables - id field from either inbound_scans or warehouse_sweeps"

  - name: order_id
    description: Unique identifier of the order being scanned
    source: core_prod_gl.inbound_scans.order_id, core_prod_gl.warehouse_sweeps.order_id
    technical_description: "Direct field from source tables - order_id from either inbound_scans or warehouse_sweeps"

  - name: route_id
    description: Identifier of the route associated with the scan, if applicable
    source: core_prod_gl.inbound_scans.route_id
    technical_description: "Direct field from inbound_scans for route_inbound scans; NULL for warehouse sweeps"

  - name: scan_hub_id
    description: Identifier of the hub where the scan occurred
    source: core_prod_gl.inbound_scans.hub_id, core_prod_gl.warehouse_sweeps.hub_id
    technical_description: "Direct field from source tables - hub_id from either inbound_scans or warehouse_sweeps"

  - name: route_driver_id
    description: Identifier of the driver associated with the route during the scan
    source: route_prod_gl.route_logs.driver_id
    technical_description: "Cast as bigint from route_logs.driver_id when joined with inbound_scans on route_id; NULL for warehouse sweeps"

  - name: route_hub_id
    description: Identifier of the hub associated with the route
    source: route_prod_gl.route_logs.hub_id
    technical_description: "Cast as bigint from route_logs.hub_id when joined with inbound_scans on route_id; NULL for warehouse sweeps"

  - name: type
    description: Classification of the scan event
    source: "Derived"
    technical_description: "CASE: 'route_inbound' when route_id is not NULL, 'global_inbound' for other inbound scans, 'warehouse_sweep' for warehouse sweeps"

  - name: source_table
    description: Indicates which source table the record came from
    source: "Derived"
    technical_description: "String literal - either 'inbound_scans' or 'warehouse_sweeps'"

  - name: event_datetime
    description: Local timestamp when the scan event occurred
    source: core_prod_gl.inbound_scans.created_at, core_prod_gl.warehouse_sweeps.created_at
    technical_description: "Converted from UTC to local timezone using from_utc_timestamp(created_at, local_timezone)"

  - name: created_month
    description: Month and year when the scan event occurred
    source: core_prod_gl.inbound_scans.created_at, core_prod_gl.warehouse_sweeps.created_at
    technical_description: "Formatted as YYYY-MM from created_at using date_format(created_at, 'yyyy-MM')" 