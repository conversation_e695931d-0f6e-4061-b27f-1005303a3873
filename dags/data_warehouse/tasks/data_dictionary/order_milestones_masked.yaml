# Data dictionary

table_name: order_milestones

overview:
  purpose: A comprehensive table that combines order tracking information with milestones throughout the delivery journey, including pickup, delivery attempts, returns, and final status. This table serves as the primary source for order tracking analysis and reporting.
  granularity: Each row represents a unique order with its complete journey information from creation to final status.
  business_rules:
    - Orders are tracked through multiple stages including pickup, inbound, delivery attempts, and returns
    - Multiple delivery attempts are recorded with timestamps and status for each attempt
    - Return to sender (RTS) information is captured when applicable
    - Cross-border shipment information is included via xb_outbound_flag
    - Force success cases are tracked separately from normal deliveries

input_tables:
  - data_warehouse.orders_enriched
  - data_warehouse.order_pickups
  - data_warehouse.order_dp_milestones
  - data_warehouse.order_inbounds
  - data_warehouse.order_destinations
  - data_warehouse.order_cancellations
  - data_warehouse.order_force_successes
  - data_warehouse.order_deliveries
  - data_warehouse.order_rts_triggers
  - data_warehouse.order_third_party_transfers
  - data_warehouse.xb_outbound_orders
  - data_warehouse.reserve_tracking_ids_enriched
  - data_warehouse.implanted_manifest_scan_events_enriched
  - data_warehouse.update_cash_events_enriched
  - data_warehouse.lnk_orders_shippers
  - data_warehouse.order_added_to_implant_manifests
  - data_warehouse.order_dimensions

fields:
  - name: order_id
    description: Unique identifier for the order
    source: data_warehouse.orders_enriched.order_id
    technical_description: "Direct field from source"

  - name: country
    description: Country code where the order was created
    source: data_warehouse.orders_enriched.system_id
    technical_description: "Direct field from source, aliased as country"

  - name: tracking_id
    description: Tracking number assigned to the order
    source: data_warehouse.orders_enriched.tracking_id
    technical_description: "Direct field from source"

  - name: third_party_tracking_id
    description: Tracking ID assigned by third-party delivery partners
    source: data_warehouse.orders_enriched.third_party_tracking_id
    technical_description: "Direct field from source"

  - name: xb_outbound_flag
    description: Indicates if the order is a cross-border outbound shipment
    source: data_warehouse.xb_outbound_orders.outbound_flag
    technical_description: "Direct field from source, aliased as xb_outbound_flag"

  - name: shipper_reference_number
    description: Reference number provided by the shipper
    source: data_warehouse.orders_enriched.shipper_reference_number
    technical_description: "Direct field from source"

  - name: status
    description: Current status of the order
    source: data_warehouse.orders_enriched.status
    technical_description: "Direct field from source"

  - name: granular_status
    description: Detailed status of the order
    source: data_warehouse.orders_enriched.granular_status
    technical_description: "Direct field from source"

  - name: status_enriched
    description: Simplified categorization of order status
    source: data_warehouse.orders_enriched.status_enriched
    technical_description: "Direct field from source"

  - name: shipper_id
    description: Unique identifier for the shipper
    source: data_warehouse.lnk_orders_shippers.shipper_id
    technical_description: "Direct field from source"

  - name: seller_id
    description: Unique identifier for the seller
    source: data_warehouse.orders_enriched.seller_id
    technical_description: "Direct field from source"

  - name: seller_name
    description: Name of the seller
    source: data_warehouse.orders_enriched.seller_name
    technical_description: "Direct field from source"

  - name: lazmall_flag
    description: Indicates if the order is from Lazada Mall
    source: data_warehouse.orders_enriched.lazmall_flag
    technical_description: "Direct field from source"

  - name: lex_flag
    description: Indicates if the order is a LEX (Lazada Express) order
    source: data_warehouse.orders_enriched.lex_flag
    technical_description: "Direct field from source"

  - name: source_id
    description: Identifier for the order source system
    source: data_warehouse.orders_enriched.source_id
    technical_description: "Direct field from source"

  - name: plugin_name
    description: Name of the plugin used for order processing
    source: data_warehouse.orders_enriched.plugin_name
    technical_description: "Direct field from source"

  - name: stamp_id
    description: Stamp identifier for the order
    source: data_warehouse.orders_enriched.stamp_id
    technical_description: "Direct field from source"

  - name: cod_id
    description: Cash on Delivery identifier
    source: data_warehouse.orders_enriched.cod_id
    technical_description: "Direct field from source"

  - name: cost
    description: Cost of the order in local currency
    source: data_warehouse.orders_enriched.cost
    technical_description: "Direct field from source"

  - name: cost_sgd
    description: Cost of the order converted to Singapore dollars
    source: data_warehouse.orders_enriched.cost_sgd
    technical_description: "Direct field from source"

  - name: insurance_value
    description: Insurance value of the order in local currency
    source: data_warehouse.orders_enriched.insurance_value
    technical_description: "Direct field from source"

  - name: insurance_value_sgd
    description: Insurance value of the order converted to Singapore dollars
    source: data_warehouse.orders_enriched.insurance_value_sgd
    technical_description: "Direct field from source"

  - name: gst_fee
    description: GST (Goods and Services Tax) fee in local currency
    source: data_warehouse.orders_enriched.gst_fee
    technical_description: "Direct field from source"

  - name: gst_fee_sgd
    description: GST fee converted to Singapore dollars
    source: data_warehouse.orders_enriched.gst_fee_sgd
    technical_description: "Direct field from source"

  - name: delivery_fee
    description: Delivery fee in local currency
    source: data_warehouse.orders_enriched.delivery_fee
    technical_description: "Direct field from source"

  - name: delivery_fee_sgd
    description: Delivery fee converted to Singapore dollars
    source: data_warehouse.orders_enriched.delivery_fee_sgd
    technical_description: "Direct field from source"

  - name: cod_value
    description: Cash on Delivery value in local currency
    source: data_warehouse.orders_enriched.cod_value
    technical_description: "Direct field from source"

  - name: cod_value_sgd
    description: Cash on Delivery value converted to Singapore dollars
    source: data_warehouse.orders_enriched.cod_value_sgd
    technical_description: "Direct field from source"

  - name: ninjapack_flag
    description: Indicates if the order is a Ninjapack order
    source: data_warehouse.orders_enriched.ninjapack_flag
    technical_description: "Direct field from source"

  - name: rts_flag
    description: Indicates if the order is marked for return to sender
    source: data_warehouse.orders_enriched.rts_flag
    technical_description: "Direct field from source"

  - name: parcel_size
    description: Size category of the parcel
    source: data_warehouse.orders_enriched.parcel_size
    technical_description: "Direct field from source"

  - name: nv_width
    description: Width of the parcel as measured by Ninja Van
    source: data_warehouse.orders_enriched.nv_width
    technical_description: "Direct field from source"

  - name: nv_height
    description: Height of the parcel as measured by Ninja Van
    source: data_warehouse.orders_enriched.nv_height
    technical_description: "Direct field from source"

  - name: nv_length
    description: Length of the parcel as measured by Ninja Van
    source: data_warehouse.orders_enriched.nv_length
    technical_description: "Direct field from source"

  - name: nv_weight
    description: Weight of the parcel as measured by Ninja Van
    source: data_warehouse.orders_enriched.nv_weight
    technical_description: "Direct field from source"

  - name: weight
    description: Declared weight of the parcel
    source: data_warehouse.orders_enriched.weight
    technical_description: "Direct field from source"

  - name: original_weight
    description: Original declared weight of the parcel before any adjustments
    source: data_warehouse.orders_enriched.original_weight
    technical_description: "Direct field from source"

  - name: estimated_weight
    description: Estimated weight based on parcel dimensions
    source: data_warehouse.order_dimensions.estimated_weight
    technical_description: "Direct field from source"

  - name: estimated_volume
    description: Estimated volume based on parcel dimensions
    source: data_warehouse.order_dimensions.estimated_volume
    technical_description: "Direct field from source"

  - name: items
    description: Description of items in the order
    source: data_warehouse.orders_enriched.items
    technical_description: "Direct field from source"

  - name: delivery_instructions
    description: Special instructions for delivery
    source: data_warehouse.orders_enriched.delivery_instructions
    technical_description: "Direct field from source"

  - name: from_billing_zone
    description: Billing zone of the pickup location
    source: data_warehouse.orders_enriched.from_billing_zone
    technical_description: "Direct field from source"

  - name: to_billing_zone
    description: Billing zone of the delivery location
    source: data_warehouse.orders_enriched.to_billing_zone
    technical_description: "Direct field from source"

  - name: from_l1_id
    description: Level 1 geographic ID of the pickup location
    source: data_warehouse.orders_enriched.from_l1_id
    technical_description: "Direct field from source"

  - name: from_l1_name
    description: Level 1 geographic name of the pickup location
    source: data_warehouse.orders_enriched.from_l1_name
    technical_description: "Direct field from source"

  - name: to_l1_id
    description: Level 1 geographic ID of the delivery location
    source: data_warehouse.orders_enriched.to_l1_id
    technical_description: "Direct field from source"

  - name: to_l1_name
    description: Level 1 geographic name of the delivery location
    source: data_warehouse.orders_enriched.to_l1_name
    technical_description: "Direct field from source"

  - name: from_l2_id
    description: Level 2 geographic ID of the pickup location
    source: data_warehouse.orders_enriched.from_l2_id
    technical_description: "Direct field from source"

  - name: from_l2_name
    description: Level 2 geographic name of the pickup location
    source: data_warehouse.orders_enriched.from_l2_name
    technical_description: "Direct field from source"

  - name: to_l2_id
    description: Level 2 geographic ID of the delivery location
    source: data_warehouse.orders_enriched.to_l2_id
    technical_description: "Direct field from source"

  - name: to_l2_name
    description: Level 2 geographic name of the delivery location
    source: data_warehouse.orders_enriched.to_l2_name
    technical_description: "Direct field from source"

  - name: from_l3_id
    description: Level 3 geographic ID of the pickup location
    source: data_warehouse.orders_enriched.from_l3_id
    technical_description: "Direct field from source"

  - name: from_l3_name
    description: Level 3 geographic name of the pickup location
    source: data_warehouse.orders_enriched.from_l3_name
    technical_description: "Direct field from source"

  - name: to_l3_id
    description: Level 3 geographic ID of the delivery location
    source: data_warehouse.orders_enriched.to_l3_id
    technical_description: "Direct field from source"

  - name: to_l3_name
    description: Level 3 geographic name of the delivery location
    source: data_warehouse.orders_enriched.to_l3_name
    technical_description: "Direct field from source"

  - name: service_type
    description: Type of delivery service requested
    source: data_warehouse.orders_enriched.service_type
    technical_description: "Direct field from source"

  - name: delivery_type
    description: Type of delivery method used
    source: data_warehouse.orders_enriched.delivery_type
    technical_description: "Direct field from source"

  - name: order_type
    description: Classification of the order type
    source: data_warehouse.orders_enriched.order_type
    technical_description: "Direct field from source"

  - name: delivery_verification_mode
    description: Method used to verify delivery completion
    source: data_warehouse.orders_enriched.delivery_verification_mode
    technical_description: "Direct field from source"

  - name: from_postcode
    description: Postal code of the pickup location
    source: data_warehouse.orders_enriched.from_postcode
    technical_description: "Direct field from source"

  - name: to_postcode
    description: Postal code of the delivery location
    source: data_warehouse.orders_enriched.to_postcode
    technical_description: "Direct field from source"

  - name: dest_postcode
    description: Final destination postal code, considering address updates
    source: 
      - data_warehouse.order_deliveries.previous_to_postcode_before_update_address_events
      - data_warehouse.orders_enriched.to_postcode
    technical_description: "Derived field: If delivery has address update events, use previous_to_postcode_before_update_address_events, else use to_postcode"

  - name: to_name
    description: Name of the recipient
    source: data_warehouse.orders_enriched.to_name
    technical_description: "Direct field from source"

  - name: from_name
    description: Name of the sender
    source: data_warehouse.orders_enriched.from_name
    technical_description: "Direct field from source"

  - name: from_address1
    description: First line of sender's address
    source: data_warehouse.orders_enriched.from_address1
    technical_description: "Direct field from source"

  - name: from_address2
    description: Second line of sender's address
    source: data_warehouse.orders_enriched.from_address2
    technical_description: "Direct field from source"

  - name: from_city
    description: City of the sender
    source: data_warehouse.orders_enriched.from_city
    technical_description: "Direct field from source"

  - name: from_country
    description: Country of the sender
    source: data_warehouse.orders_enriched.from_country
    technical_description: "Direct field from source"

  - name: to_address1
    description: First line of recipient's address
    source: data_warehouse.orders_enriched.to_address1
    technical_description: "Direct field from source"

  - name: to_address2
    description: Second line of recipient's address
    source: data_warehouse.orders_enriched.to_address2
    technical_description: "Direct field from source"

  - name: to_city
    description: City of the recipient
    source: data_warehouse.orders_enriched.to_city
    technical_description: "Direct field from source"

  - name: to_country
    description: Country of the recipient
    source: data_warehouse.orders_enriched.to_country
    technical_description: "Direct field from source"

  - name: original_to_address1
    description: Original first line of recipient's address before any updates
    source: data_warehouse.orders_enriched.original_to_address1
    technical_description: "Direct field from source"

  - name: original_to_address2
    description: Original second line of recipient's address before any updates
    source: data_warehouse.orders_enriched.original_to_address2
    technical_description: "Direct field from source"

  - name: original_to_city
    description: Original city of the recipient before any updates
    source: data_warehouse.orders_enriched.original_to_city
    technical_description: "Direct field from source"

  - name: original_to_country
    description: Original country of the recipient before any updates
    source: data_warehouse.orders_enriched.original_to_country
    technical_description: "Direct field from source"

  - name: original_to_latitude
    description: Original latitude of the recipient's location before any updates
    source: data_warehouse.orders_enriched.original_to_latitude
    technical_description: "Direct field from source"

  - name: original_to_longitude
    description: Original longitude of the recipient's location before any updates
    source: data_warehouse.orders_enriched.original_to_longitude
    technical_description: "Direct field from source"

  - name: creation_datetime
    description: Timestamp when the order was created in the system
    source: data_warehouse.orders_enriched.creation_datetime
    technical_description: "Direct field from source"

  - name: platform_creation_datetime
    description: Timestamp when the order was created on the platform
    source: data_warehouse.orders_enriched.platform_creation_datetime
    technical_description: "Direct field from source"

  - name: dest_hub_id
    description: ID of the destination hub for delivery
    source: data_warehouse.order_destinations.delivery_dest_hub_id
    technical_description: "Direct field from source, aliased as dest_hub_id"

  - name: dest_zone
    description: Delivery destination zone
    source: data_warehouse.order_destinations.delivery_dest_zone
    technical_description: "Direct field from source, aliased as dest_zone"

  - name: dp_dropoff_datetime
    description: Timestamp when the order was dropped off at the delivery point
    source: data_warehouse.order_dp_milestones.shipper_to_dp_datetime
    technical_description: "Direct field from source, aliased as dp_dropoff_datetime"

  - name: dp_dropoff_dp_id
    description: ID of the delivery point where the order was dropped off
    source: data_warehouse.order_dp_milestones.shipper_to_dp_dpms_id
    technical_description: "Direct field from source, aliased as dp_dropoff_dp_id"

  - name: first_pickup_attempt_failure_reason_id
    description: Reason ID for the first failed pickup attempt
    source: data_warehouse.order_pickups.first_attempt_failure_reason_id
    technical_description: "Direct field from source"

  - name: first_pickup_attempt_reservation_id
    description: Reservation ID for the first pickup attempt
    source: data_warehouse.order_pickups.first_attempt_reservation_id
    technical_description: "Direct field from source"

  - name: first_pickup_attempt_hub_id
    description: Hub ID associated with the first pickup attempt
    source: data_warehouse.order_pickups.first_attempt_route_hub_id
    technical_description: "Direct field from source"

  - name: first_pickup_attempt_datetime
    description: Timestamp of the first pickup attempt
    source: data_warehouse.order_pickups.first_attempt_datetime
    technical_description: "Direct field from source"

  - name: pickup_datetime
    description: Timestamp when the order was successfully picked up
    source: data_warehouse.order_pickups.success_datetime
    technical_description: "Direct field from source"

  - name: nv_pickup_datetime
    description: Ninja Van's recorded timestamp of successful pickup
    source: data_warehouse.order_pickups.nv_success_datetime
    technical_description: "Direct field from source"

  - name: pickup_hub_id
    description: ID of the hub where the order was successfully picked up
    source: data_warehouse.order_pickups.success_route_hub_id
    technical_description: "Direct field from source"

  - name: pickup_success_driver_id
    description: ID of the driver who successfully picked up the order
    source: data_warehouse.order_pickups.success_route_driver_id
    technical_description: "Direct field from source"

  - name: pickup_attempts
    description: Total number of pickup attempts made
    source: data_warehouse.order_pickups.total_attempts
    technical_description: "Direct field from source"

  - name: pickup_flag
    description: Indicates if the order was successfully picked up
    source: data_warehouse.order_pickups.success_datetime
    technical_description: "Derived field: CAST(IF(success_datetime IS NOT NULL, 1, 0) AS BIGINT)"

  - name: inbound_datetime
    description: Timestamp when the order arrived at the inbound facility
    source: data_warehouse.order_inbounds.event_datetime
    technical_description: "Direct field from source"

  - name: inbound_type
    description: Type of inbound process used
    source: data_warehouse.order_inbounds.type
    technical_description: "Direct field from source"

  - name: inbound_hub_id
    description: ID of the hub where the order was processed for inbound
    source: data_warehouse.order_inbounds.scan_hub_id
    technical_description: "Direct field from source"

  - name: inbound_flag
    description: Indicates if the order has been processed for inbound
    source: data_warehouse.order_inbounds.event_datetime
    technical_description: "Derived field: CAST(IF(event_datetime IS NOT NULL, 1, 0) AS BIGINT)"

  - name: driver_to_dp_datetime
    description: Timestamp when the driver delivered to delivery point
    source: data_warehouse.order_dp_milestones.driver_to_dp_datetime
    technical_description: "Direct field from source"

  - name: driver_to_dp_dpms_id
    description: Delivery point ID where driver delivered the order
    source: data_warehouse.order_dp_milestones.driver_to_dp_dpms_id
    technical_description: "Direct field from source"

  - name: first_valid_delivery_attempt_datetime
    description: Timestamp of the first valid delivery attempt
    source: data_warehouse.order_deliveries.first_valid_delivery_attempt_datetime
    technical_description: "Direct field from source"

  - name: first_valid_delivery_attempt_status
    description: Status of the first valid delivery attempt
    source: data_warehouse.order_deliveries.first_valid_delivery_attempt_status
    technical_description: "Direct field from source"

  - name: first_valid_delivery_failure_reason_id
    description: Reason ID if the first valid delivery attempt failed
    source: data_warehouse.order_deliveries.first_valid_delivery_attempt_failure_reason_id
    technical_description: "Direct field from source"

  - name: first_valid_delivery_attempt_timeslot_start
    description: Start time of the delivery window for first attempt
    source: data_warehouse.order_deliveries.first_valid_delivery_attempt_timeslot_start
    technical_description: "Direct field from source"

  - name: first_valid_delivery_attempt_timeslot_end
    description: End time of the delivery window for first attempt
    source: data_warehouse.order_deliveries.first_valid_delivery_attempt_timeslot_end
    technical_description: "Direct field from source"

  - name: first_valid_delivery_attempt_hub_id
    description: Hub ID associated with the first valid delivery attempt
    source: data_warehouse.order_deliveries.first_valid_delivery_attempt_hub_id
    technical_description: "Direct field from source"

  - name: first_valid_delivery_attempt_flag
    description: Indicates if there was a first valid delivery attempt
    source: data_warehouse.order_deliveries.first_valid_delivery_attempt_datetime
    technical_description: "Derived field: CAST(IF(first_valid_delivery_attempt_datetime IS NOT NULL, 1, 0) AS BIGINT)"

  - name: second_valid_delivery_attempt_datetime
    description: Timestamp of the second valid delivery attempt
    source: data_warehouse.order_deliveries.second_valid_delivery_attempt_datetime
    technical_description: "Direct field from source"

  - name: second_valid_delivery_attempt_status
    description: Status of the second valid delivery attempt
    source: data_warehouse.order_deliveries.second_valid_delivery_attempt_status
    technical_description: "Direct field from source"

  - name: second_valid_delivery_failure_reason_id
    description: Reason ID if the second valid delivery attempt failed
    source: data_warehouse.order_deliveries.second_valid_delivery_attempt_failure_reason_id
    technical_description: "Direct field from source"

  - name: second_valid_delivery_attempt_timeslot_start
    description: Start time of the delivery window for second attempt
    source: data_warehouse.order_deliveries.second_valid_delivery_attempt_timeslot_start
    technical_description: "Direct field from source"

  - name: second_valid_delivery_attempt_timeslot_end
    description: End time of the delivery window for second attempt
    source: data_warehouse.order_deliveries.second_valid_delivery_attempt_timeslot_end
    technical_description: "Direct field from source"

  - name: second_valid_delivery_attempt_hub_id
    description: Hub ID associated with the second valid delivery attempt
    source: data_warehouse.order_deliveries.second_valid_delivery_attempt_hub_id
    technical_description: "Direct field from source"

  - name: second_valid_delivery_attempt_flag
    description: Indicates if there was a second valid delivery attempt
    source: data_warehouse.order_deliveries.second_valid_delivery_attempt_datetime
    technical_description: "Derived field: CAST(IF(second_valid_delivery_attempt_datetime IS NOT NULL, 1, 0) AS BIGINT)"

  - name: third_valid_delivery_attempt_datetime
    description: Timestamp of the third valid delivery attempt
    source: data_warehouse.order_deliveries.third_valid_delivery_attempt_datetime
    technical_description: "Direct field from source"

  - name: third_valid_delivery_attempt_status
    description: Status of the third valid delivery attempt
    source: data_warehouse.order_deliveries.third_valid_delivery_attempt_status
    technical_description: "Direct field from source"

  - name: third_valid_delivery_failure_reason_id
    description: Reason ID if the third valid delivery attempt failed
    source: data_warehouse.order_deliveries.third_valid_delivery_attempt_failure_reason_id
    technical_description: "Direct field from source"

  - name: third_valid_delivery_attempt_timeslot_start
    description: Start time of the delivery window for third attempt
    source: data_warehouse.order_deliveries.third_valid_delivery_attempt_timeslot_start
    technical_description: "Direct field from source"

  - name: third_valid_delivery_attempt_timeslot_end
    description: End time of the delivery window for third attempt
    source: data_warehouse.order_deliveries.third_valid_delivery_attempt_timeslot_end
    technical_description: "Direct field from source"

  - name: third_valid_delivery_attempt_hub_id
    description: Hub ID associated with the third valid delivery attempt
    source: data_warehouse.order_deliveries.third_valid_delivery_attempt_hub_id
    technical_description: "Direct field from source"

  - name: third_valid_delivery_attempt_flag
    description: Indicates if there was a third valid delivery attempt
    source: data_warehouse.order_deliveries.third_valid_delivery_attempt_datetime
    technical_description: "Derived field: CAST(IF(third_valid_delivery_attempt_datetime IS NOT NULL, 1, 0) AS BIGINT)"

  - name: last_valid_delivery_attempt_datetime
    description: Timestamp of the last valid delivery attempt
    source: data_warehouse.order_deliveries.last_valid_delivery_attempt_datetime
    technical_description: "Direct field from source"

  - name: last_valid_delivery_attempt_status
    description: Status of the last valid delivery attempt
    source: data_warehouse.order_deliveries.last_valid_delivery_attempt_status
    technical_description: "Direct field from source"

  - name: last_valid_delivery_failure_reason_id
    description: Reason ID if the last valid delivery attempt failed
    source: data_warehouse.order_deliveries.last_valid_delivery_attempt_failure_reason_id
    technical_description: "Direct field from source"

  - name: last_valid_delivery_attempt_timeslot_start
    description: Start time of the delivery window for last attempt
    source: data_warehouse.order_deliveries.last_valid_delivery_attempt_timeslot_start
    technical_description: "Direct field from source"

  - name: last_valid_delivery_attempt_timeslot_end
    description: End time of the delivery window for last attempt
    source: data_warehouse.order_deliveries.last_valid_delivery_attempt_timeslot_end
    technical_description: "Direct field from source"

  - name: last_valid_delivery_attempt_hub_id
    description: Hub ID associated with the last valid delivery attempt
    source: data_warehouse.order_deliveries.last_valid_delivery_attempt_hub_id
    technical_description: "Direct field from source"

  - name: delivery_attempts
    description: Total number of delivery attempts made
    source: data_warehouse.order_deliveries.delivery_attempts
    technical_description: "Direct field from source, defaulted to 0 if null"

  - name: rts_dest_hub_id
    description: Hub ID for return to sender destination
    source: data_warehouse.order_destinations.rts_dest_hub_id
    technical_description: "Direct field from source"

  - name: rts_dest_zone
    description: Zone for return to sender destination
    source: data_warehouse.order_destinations.rts_dest_zone
    technical_description: "Direct field from source"

  - name: rts_trigger_datetime
    description: Timestamp when return to sender was triggered
    source: data_warehouse.order_rts_triggers.event_datetime
    technical_description: "Direct field from source"

  - name: rts_reason
    description: Reason for return to sender
    source: data_warehouse.order_rts_triggers.rts_reason
    technical_description: "Direct field from source"

  - name: first_valid_rts_attempt_datetime
    description: Timestamp of first valid return to sender attempt
    source: data_warehouse.order_deliveries.first_valid_rts_attempt_datetime
    technical_description: "Direct field from source"

  - name: first_valid_rts_attempt_status
    description: Status of first valid return to sender attempt
    source: data_warehouse.order_deliveries.first_valid_rts_attempt_status
    technical_description: "Direct field from source"

  - name: first_valid_rts_failure_reason_id
    description: Reason ID if first valid RTS attempt failed
    source: data_warehouse.order_deliveries.first_valid_rts_attempt_failure_reason_id
    technical_description: "Direct field from source"

  - name: first_valid_rts_attempt_hub_id
    description: Hub ID associated with first valid RTS attempt
    source: data_warehouse.order_deliveries.first_valid_rts_attempt_hub_id
    technical_description: "Direct field from source"

  - name: first_valid_rts_attempt_flag
    description: Indicates if there was a first valid RTS attempt
    source: data_warehouse.order_deliveries.first_valid_rts_attempt_datetime
    technical_description: "Derived field: CAST(IF(first_valid_rts_attempt_datetime IS NOT NULL, 1, 0) AS BIGINT)"

  - name: last_valid_rts_attempt_datetime
    description: Timestamp of last valid return to sender attempt
    source: data_warehouse.order_deliveries.last_valid_rts_attempt_datetime
    technical_description: "Direct field from source"

  - name: last_valid_rts_attempt_status
    description: Status of last valid return to sender attempt
    source: data_warehouse.order_deliveries.last_valid_rts_attempt_status
    technical_description: "Direct field from source"

  - name: last_valid_rts_failure_reason_id
    description: Reason ID if last valid RTS attempt failed
    source: data_warehouse.order_deliveries.last_valid_rts_attempt_failure_reason_id
    technical_description: "Direct field from source"

  - name: last_valid_rts_attempt_hub_id
    description: Hub ID associated with last valid RTS attempt
    source: data_warehouse.order_deliveries.last_valid_rts_attempt_hub_id
    technical_description: "Direct field from source"

  - name: rts_attempts
    description: Total number of return to sender attempts
    source: data_warehouse.order_deliveries.rts_attempts
    technical_description: "Direct field from source, defaulted to 0 if null"

  - name: rts_before_first_attempt_flag
    description: Indicates if RTS was triggered before first delivery attempt
    source: 
      - data_warehouse.order_deliveries.delivery_attempts
      - data_warehouse.orders_enriched.rts_flag
    technical_description: "Derived field: CAST(IF((delivery_attempts = 0 OR delivery_attempts IS NULL) AND rts_flag = 1, 1, 0) AS BIGINT)"

  - name: delivery_success_datetime
    description: Timestamp when delivery was successful
    source: data_warehouse.order_deliveries.delivery_success_datetime
    technical_description: "Direct field from source"

  - name: delivery_success_hub_id
    description: Hub ID where delivery was successful
    source: data_warehouse.order_deliveries.delivery_success_hub_id
    technical_description: "Direct field from source"

  - name: delivery_success_driver_id
    description: ID of driver who successfully delivered the order
    source: data_warehouse.order_deliveries.delivery_success_driver_id
    technical_description: "Direct field from source"

  - name: delivery_success_flag
    description: Indicates if delivery was successful
    source: data_warehouse.order_deliveries.delivery_success_datetime
    technical_description: "Derived field: CAST(IF(delivery_success_datetime IS NOT NULL, 1, 0) AS BIGINT)"

  - name: force_success_datetime
    description: Timestamp when order was marked as force success
    source: data_warehouse.order_force_successes.event_datetime
    technical_description: "Direct field from source"

  - name: force_success_flag
    description: Indicates if order was marked as force success
    source: data_warehouse.order_force_successes.event_datetime
    technical_description: "Derived field: CAST(IF(event_datetime IS NOT NULL, 1, 0) AS BIGINT)"

  - name: third_party_transfer_datetime
    description: Timestamp when order was transferred to third party
    source: data_warehouse.order_third_party_transfers.event_datetime
    technical_description: "Direct field from source"

  - name: third_party_transfer_flag
    description: Indicates if order was transferred to third party
    source: data_warehouse.order_third_party_transfers.event_datetime
    technical_description: "Derived field: CAST(IF(event_datetime IS NOT NULL, 1, 0) AS BIGINT)"

  - name: is_pickup_required
    description: Indicates if pickup is required for the order
    source: data_warehouse.reserve_tracking_ids_enriched.is_pickup_required
    technical_description: "Direct field from source"

  - name: implant_manifest_datetime
    description: Timestamp when order was added to implant manifest
    source: 
      - data_warehouse.implanted_manifest_scan_events_enriched.min_implant_manifest_datetime
      - data_warehouse.order_added_to_implant_manifests.event_datetime
    technical_description: "Derived field: COALESCE(min_implant_manifest_datetime, event_datetime)"

  - name: cancellation_datetime
    description: Timestamp when order was cancelled
    source: data_warehouse.order_cancellations.event_datetime
    technical_description: "Direct field from source"

  - name: min_old_cod_value
    description: Minimum previous COD value before updates
    source: data_warehouse.update_cash_events_enriched.min_old_cod_value
    technical_description: "Direct field from source"

  - name: max_new_cod_value
    description: Maximum new COD value after updates
    source: data_warehouse.update_cash_events_enriched.max_new_cod_value
    technical_description: "Direct field from source"

  - name: cod_update_frequency
    description: Number of times COD value was updated
    source: data_warehouse.update_cash_events_enriched.cod_update_frequency
    technical_description: "Direct field from source"

  - name: comments
    description: Additional comments or notes on the order
    source: data_warehouse.orders_enriched.comments
    technical_description: "Direct field from source"

  - name: billing_weight
    description: Weight used for billing calculations
    source: data_warehouse.orders_enriched.billing_weight
    technical_description: "Direct field from source"

  - name: third_party_shipper_name
    description: Name of third party shipper if applicable
    source: data_warehouse.orders_enriched.third_party_shipper_name
    technical_description: "Direct field from source"

  - name: package_id
    description: Unique identifier for the package
    source: data_warehouse.orders_enriched.package_id
    technical_description: "Direct field from source"

  - name: shipper_group_my
    description: Shipper group for Malaysia orders
    source: data_warehouse.orders_enriched.shipper_group_my
    technical_description: "Direct field from source"

  - name: created_month
    description: Month when the order was created
    source: data_warehouse.orders_enriched.created_month
    technical_description: "Direct field from source" 