# Data dictionary

table_name: pickup_scan_events

overview:
  purpose: Tracks all pickup attempts (both successful and failed) for orders across different systems. This table combines successful pickups recorded in inbound scans with failed pickup events, providing a complete view of pickup operations.
  granularity: Each row represents a single pickup attempt (success or failure) for an order
  business_rules:
    - Only includes inbound scans of type VAN_FROM_SHIPPER (type = 1)
    - For successful pickups, only non-deleted scans with valid order_ids are included
    - Failed pickups are sourced from order_events with type 40 (PICKUP_FAILURE)
    - Route information is enriched from route_logs

input_tables:
  - events_prod_gl.order_events
  - core_prod_gl.inbound_scans
  - route_prod_gl.route_logs

fields:
  - name: id
    description: Unique identifier for the pickup event
    source: events_prod_gl.order_events.id OR core_prod_gl.inbound_scans.id
    technical_description: "Direct field from source, cast as bigint for order_events"

  - name: source_table
    description: Indicates which source system the pickup event came from
    source: Derived
    technical_description: "CASE-based field: 'inbound_scans' for successful pickups, 'order_events' for failed pickups"

  - name: order_id
    description: Unique identifier of the order
    source: events_prod_gl.order_events.order_id OR core_prod_gl.inbound_scans.order_id
    technical_description: "Direct field from source, cast as bigint for order_events"

  - name: status
    description: Status of the pickup attempt
    source: Derived
    technical_description: "CASE-based field: 'Success' for inbound_scans records, 'Failed' for order_events records"

  - name: scan_result_id
    description: Identifier code for the scan result (for successful pickups only)
    source: core_prod_gl.inbound_scans.result
    technical_description: "Direct field from source, only populated for successful pickups"

  - name: scan_result
    description: Human-readable description of the scan result
    source: Derived from core_prod_gl.inbound_scans.result
    technical_description: "CASE statement mapping result codes to readable values: S1=RECEIVED_BY_DRIVER_FROM_CUSTOMER, S2=RECEIVED_BY_SORTING_HUB, S2A=RECEIVED_BY_SORTING_HUB_SAMEDAY, S3=RECEIVED_BY_VAN, S4=RECEIVED_BY_TRUCK, S5=RECEIVED_BY_SORTING_HUB_CMI, E1=TRACKING_ID_NOT_FOUND, E2=INCORRECT_ROUTE, E3=DUPLICATE_SCAN, E4=ORDER_COMPLETED, E5=OTHERS"

  - name: failure_reason_id
    description: Identifier for the reason of pickup failure
    source: events_prod_gl.order_events.data
    technical_description: "Extracted from JSON data field using get_json_object(data, '$.failure_reason_id'), cast as integer"

  - name: reservation_id
    description: Identifier of the pickup reservation
    source: events_prod_gl.order_events.data
    technical_description: "Extracted from JSON data field using get_json_object(data, '$.reservation_id'), cast as integer"

  - name: route_id
    description: Identifier of the delivery route
    source: events_prod_gl.order_events.data OR core_prod_gl.inbound_scans.route_id
    technical_description: "For failed pickups: Extracted from JSON data field using get_json_object(data, '$.route_id'). For successful pickups: Direct field from inbound_scans"

  - name: route_driver_id
    description: Identifier of the driver assigned to the route
    source: route_prod_gl.route_logs.driver_id
    technical_description: "Joined from route_logs using route_id and system_id, cast as bigint"

  - name: route_hub_id
    description: Identifier of the hub associated with the route
    source: route_prod_gl.route_logs.hub_id
    technical_description: "Joined from route_logs using route_id and system_id, cast as bigint"

  - name: event_datetime
    description: Local timestamp when the pickup event occurred
    source: events_prod_gl.order_events.time OR core_prod_gl.inbound_scans.created_at
    technical_description: "Converted from UTC to local timezone using from_utc_timestamp() based on system_id"

  - name: system_id
    description: Identifier of the system where the event originated
    source: events_prod_gl.order_events.system_id OR core_prod_gl.inbound_scans.system_id
    technical_description: "Direct field from source"

  - name: created_month
    description: Month when the pickup event was created, used for partitioning
    source: Derived from events_prod_gl.order_events.time OR core_prod_gl.inbound_scans.created_at
    technical_description: "Formatted as YYYY-MM using date_format() on the source timestamp" 