# Data dictionary

table_name: added_to_implant_manifest_events

overview:
  purpose: Tracks when orders are added to the implant manifest, which represents a key event in the order lifecycle
  granularity: Each row represents a single event of an order being added to the implant manifest
  business_rules:
    - Only includes events of type 71 (ADDED_TO_IMPLANT_MANIFEST)
    - Events are partitioned by system_id and created_month for efficient querying

input_tables:
  - events_prod_gl.order_events

fields:
  - name: order_event_id
    description: Unique identifier for the order event
    source: events_prod_gl.order_events.id
    technical_description: "Direct field from source"

  - name: order_id
    description: Unique identifier for the order
    source: events_prod_gl.order_events.order_id
    technical_description: "Direct field from source"

  - name: type
    description: Event type identifier (always 71 for ADDED_TO_IMPLANT_MANIFEST)
    source: events_prod_gl.order_events.type
    technical_description: "Direct field from source, filtered where type = 71"

  - name: event_datetime
    description: Timestamp when the order was added to implant manifest in local timezone
    source: events_prod_gl.order_events.time
    technical_description: "Converted from UTC to local timezone using from_utc_timestamp(time, timezone based on system_id)"

  - name: system_id
    description: Identifier for the system where the event originated
    source: events_prod_gl.order_events.system_id
    technical_description: "Direct field from source"

  - name: created_month
    description: Month and year when the event was created, used for partitioning
    source: events_prod_gl.order_events.time
    technical_description: "Derived using date_format(time, 'yyyy-MM')" 