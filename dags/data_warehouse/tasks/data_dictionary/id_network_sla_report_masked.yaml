# Data dictionary

table_name: id_network_sla_report

overview:
  purpose: Provides comprehensive SLA tracking and measurement for orders moving through the ID network by comparing actual timestamps against expected timestamps at each milestone
  granularity: Each row represents a single order with its complete journey details including all transit points and SLA metrics
  business_rules:
    - Builds upon id_network_sla_report_base to add actual timestamps and SLA measurements
    - Compares actual timestamps with target timestamps to calculate SLA met/missed at each milestone
    - Tracks both expected and actual order paths through the network
    - Includes various operational timestamps from order_hub_timestamps_flat
    - Measures SLA performance for each processing stage (inbound, outbound, transit)

input_tables:
  - data_warehouse.id_network_sla_report_base
  - data_warehouse.transit_time_report
  - data_warehouse.order_hub_timestamps_flat
  - data_warehouse.hubs_enriched

fields:
  - name: order_id
    description: Unique identifier for the order
    source: data_warehouse.id_network_sla_report_base.order_id
    technical_description: "Direct field from source"

  - name: tracking_id
    description: Tracking number associated with the order
    source: data_warehouse.transit_time_report.tracking_id
    technical_description: "Direct field from source"

  - name: shipper_id
    description: ID of the shipper who created the order
    source: data_warehouse.transit_time_report.shipper_id
    technical_description: "Direct field from source"

  - name: shipper_name
    description: Name of the shipper who created the order
    source: data_warehouse.transit_time_report.shipper_name
    technical_description: "Direct field from source"

  - name: parent_id_coalesce
    description: ID of the parent shipper organization
    source: data_warehouse.transit_time_report.parent_id_coalesce
    technical_description: "Direct field from source"

  - name: parent_name_coalesce
    description: Name of the parent shipper organization
    source: data_warehouse.transit_time_report.parent_name_coalesce
    technical_description: "Direct field from source"

  - name: reporting_name
    description: Name used for reporting purposes
    source: data_warehouse.transit_time_report.reporting_name
    technical_description: "Direct field from source"

  - name: dp_flag
    description: Flag indicating if the order was delivered to a drop point
    source: data_warehouse.transit_time_report.dp_dropoff_dp_id
    technical_description: "If dp_dropoff_dp_id is not null then 1, else 0"

  - name: dp_dropoff_dp_id
    description: ID of the droppoint if delivered to a drop point
    source: data_warehouse.transit_time_report.dp_dropoff_dp_id
    technical_description: "Direct field from source"

  - name: dp_dropoff_dp_name
    description: Name of the droppoint if delivered to a drop point
    source: data_warehouse.transit_time_report.dp_dropoff_dp_name
    technical_description: "Direct field from source"

  - name: pickup_hub_id
    description: ID of the hub where the order was picked up
    source: data_warehouse.id_network_sla_report_base.pickup_hub_id
    technical_description: "Direct field from source"

  - name: pickup_hub_name
    description: Name of the hub where the order was picked up
    source: data_warehouse.transit_time_report.pickup_hub_name
    technical_description: "Direct field from source"

  - name: inbound_hub_id
    description: ID of the hub where the order was first inbounded
    source: data_warehouse.id_network_sla_report_base.inbound_hub_id
    technical_description: "Direct field from source"

  - name: inbound_hub_name
    description: Name of the hub where the order was first inbounded
    source: data_warehouse.transit_time_report.inbound_hub_name
    technical_description: "Direct field from source"

  - name: origin_hub_id
    description: ID of the origin hub where the order started
    source: data_warehouse.id_network_sla_report_base.origin_hub_id
    technical_description: "Direct field from source"

  - name: origin_hub_name
    description: Name of the origin hub where the order started
    source: data_warehouse.transit_time_report.origin_hub_name
    technical_description: "Direct field from source"

  - name: origin_hub_region
    description: Region of the origin hub
    source: data_warehouse.transit_time_report.origin_hub_region
    technical_description: "Direct field from source"

  - name: origin_msh_hub_id
    description: ID of the origin main sorting hub
    source: data_warehouse.id_network_sla_report_base.origin_msh_hub_id
    technical_description: "Direct field from source"

  - name: origin_msh
    description: Name of the origin main sorting hub
    source: data_warehouse.id_network_sla_report_base.origin_msh
    technical_description: "Direct field from source"

  - name: transit1_hub through transit6_hub
    description: Names of transit hubs in the order's journey
    source: data_warehouse.id_network_sla_report_base.transitX_hub
    technical_description: "Direct field from source"

  - name: transit1_hub_id through transit6_hub_id
    description: IDs of transit hubs in the order's journey
    source: data_warehouse.hubs_enriched.id
    technical_description: "Lookup hub ID based on transitX_hub name"

  - name: dest_msh_hub_id
    description: ID of the destination main sorting hub
    source: data_warehouse.id_network_sla_report_base.dest_msh_hub_id
    technical_description: "Direct field from source"

  - name: dest_msh
    description: Name of the destination main sorting hub
    source: data_warehouse.id_network_sla_report_base.dest_msh
    technical_description: "Direct field from source"

  - name: dest_hub_id
    description: ID of the final destination hub
    source: data_warehouse.id_network_sla_report_base.dest_hub_id
    technical_description: "Direct field from source"

  - name: dest_hub_name
    description: Name of the final destination hub
    source: data_warehouse.id_network_sla_report_base.dest_hub_name
    technical_description: "Direct field from source"

  - name: dest_hub_region
    description: Region of the destination hub
    source: data_warehouse.transit_time_report.dest_hub_region
    technical_description: "Direct field from source"

  - name: pickup_datetime
    description: Timestamp when the order was picked up
    source: data_warehouse.id_network_sla_report_base.pickup_datetime
    technical_description: "Direct field from source"

  - name: nv_pickup_datetime
    description: Ninja Van pickup datetime
    source: data_warehouse.transit_time_report.nv_pickup_datetime
    technical_description: "Direct field from source"

  - name: wave_group
    description: Group categorizing orders by pickup or start clock hour
    source: data_warehouse.id_network_sla_report_base.pickup_datetime, start_clock_datetime
    technical_description: "If hour is 0-17 then 'wave_0_17', if hour is 18-23 then 'wave_18_23'"

  - name: expected_poh_hour_adjustment
    description: Hour adjustment for pickup on hand based on pickup time
    source: data_warehouse.id_network_sla_report_base.expected_poh_hour_adjustment
    technical_description: "Direct field from source"

  - name: inbound_datetime
    description: Timestamp when the order was first inbounded
    source: data_warehouse.id_network_sla_report_base.inbound_datetime
    technical_description: "Direct field from source"

  - name: start_clock_datetime
    description: Timestamp when SLA measurement starts
    source: data_warehouse.id_network_sla_report_base.start_clock_datetime
    technical_description: "Direct field from source"

  - name: start_clock_classification
    description: Classification of how the order entered the network
    source: data_warehouse.id_network_sla_report_base.start_clock_classification
    technical_description: "Direct field from source"

  - name: start_clock_granular_classification
    description: Detailed classification of how the order entered the network
    source: data_warehouse.id_network_sla_report_base.start_clock_granular_classification
    technical_description: "Direct field from source"

  - name: crossdock_transit_flow
    description: Classification of the order's transit flow through crossdock hubs
    source: data_warehouse.id_network_sla_report_base.crossdock_transit_flow
    technical_description: "Direct field from source"

  - name: milkrun_transit_flow
    description: Classification of the order's transit flow through milkrun hubs
    source: data_warehouse.id_network_sla_report_base.milkrun_transit_flow
    technical_description: "Direct field from source"

  - name: cdst_data_issue
    description: Indicator of any data issues in the crossdock-station transit flow
    source: data_warehouse.id_network_sla_report_base.cdst_data_issue
    technical_description: "Direct field from source"

  - name: origin_station_iv_datetime
    description: Actual invan timestamp at origin station
    source: data_warehouse.order_hub_timestamps_flat.iv_datetime
    technical_description: "Coalesce of iv_datetime from proper_station_inbound and direct_ats_station_inbound"

  - name: origin_msh_poh_datetime
    description: Actual picked-on-hand timestamp at origin MSH
    source: data_warehouse.order_hub_timestamps_flat
    technical_description: "Coalesce of first_poh_datetime, trip_arrival_datetime, and ih_datetime at origin MSH"

  - name: origin_msh_ib_datetime
    description: Actual inbound timestamp at origin MSH
    source: data_warehouse.order_hub_timestamps_flat.ib_datetime
    technical_description: "Direct field from order_hub_timestamps_flat for origin MSH"

  - name: origin_msh_cs_datetime
    description: Actual close shipment timestamp at origin MSH
    source: data_warehouse.order_hub_timestamps_flat.cs_datetime
    technical_description: "Direct field from order_hub_timestamps_flat for origin MSH"

  - name: origin_msh_iv_datetime
    description: Actual invan timestamp at origin MSH
    source: data_warehouse.order_hub_timestamps_flat.iv_datetime
    technical_description: "Direct field from order_hub_timestamps_flat for origin MSH"

  - name: transitX_poa_datetime
    description: Actual point of arrival timestamps at transit hubs
    source: data_warehouse.order_hub_timestamps_flat.trip_arrival_datetime
    technical_description: "Direct field from order_hub_timestamps_flat for each transit hub"

  - name: transitX_ih_datetime
    description: Actual inhouse timestamps at transit hubs
    source: data_warehouse.order_hub_timestamps_flat.ih_datetime
    technical_description: "Direct field from order_hub_timestamps_flat for each transit hub"

  - name: transitX_iv_datetime
    description: Actual invan timestamps at transit hubs
    source: data_warehouse.order_hub_timestamps_flat.iv_datetime
    technical_description: "Direct field from order_hub_timestamps_flat for each transit hub"

  - name: dest_msh_poa_datetime
    description: Actual point of arrival timestamp at destination MSH
    source: data_warehouse.order_hub_timestamps_flat.trip_arrival_datetime
    technical_description: "Direct field from order_hub_timestamps_flat for destination MSH"

  - name: dest_msh_ih_datetime
    description: Actual inhouse timestamp at destination MSH
    source: data_warehouse.order_hub_timestamps_flat
    technical_description: "Coalesce of first_poh_datetime and ih_datetime at destination MSH"

  - name: dest_msh_ib_datetime
    description: Actual inbound timestamp at destination MSH
    source: data_warehouse.order_hub_timestamps_flat.ib_datetime
    technical_description: "Direct field from order_hub_timestamps_flat for destination MSH"

  - name: dest_msh_cs_datetime
    description: Actual close shipment timestamp at destination MSH
    source: data_warehouse.order_hub_timestamps_flat.cs_datetime
    technical_description: "Direct field from order_hub_timestamps_flat for destination MSH"

  - name: dest_msh_iv_datetime
    description: Actual invan timestamp at destination MSH
    source: data_warehouse.order_hub_timestamps_flat.iv_datetime
    technical_description: "Direct field from order_hub_timestamps_flat for destination MSH"

  - name: dest_hub_poa_datetime
    description: Actual point of arrival timestamp at destination hub
    source: data_warehouse.order_hub_timestamps_flat
    technical_description: "Coalesce of trip_arrival_datetime and ih_datetime at destination hub"

  - name: dest_hub_ih_datetime
    description: Actual inhouse timestamp at destination hub
    source: data_warehouse.order_hub_timestamps_flat.ih_datetime
    technical_description: "Direct field from order_hub_timestamps_flat for destination hub"

  - name: dest_hub_ib_datetime
    description: Actual inbound timestamp at destination hub
    source: data_warehouse.order_hub_timestamps_flat.ib_datetime
    technical_description: "Direct field from order_hub_timestamps_flat for destination hub"

  - name: target_origin_station_ib_datetime
    description: Target inbound timestamp at origin station
    source: data_warehouse.id_network_sla_report_base
    technical_description: "For pickup_station_inbound: expected_departure_datetime_to_origin_msh minus 1 hour"

  - name: target_origin_station_iv_datetime
    description: Target invan timestamp at origin station
    source: data_warehouse.id_network_sla_report_base.expected_departure_datetime_to_origin_msh
    technical_description: "Direct field from source"

  - name: target_origin_msh_poh_datetime
    description: Target picked-on-hand timestamp at origin MSH
    source: data_warehouse.id_network_sla_report_base
    technical_description: "For station inbound: expected_arrival_datetime_at_origin_msh plus 30 minutes; For MSH pickup: date(pickup_datetime) plus expected_poh_hour_adjustment hours"

  - name: target_origin_msh_ib_datetime
    description: Target inbound timestamp at origin MSH
    source: data_warehouse.id_network_sla_report_base
    technical_description: "For station inbound: expected_arrival_datetime_at_origin_msh plus 1.5 hours; For MSH pickup: date(pickup_datetime) plus expected_poh_hour_adjustment hours plus 1 hour"

  - name: target_origin_msh_cs_datetime
    description: Target close shipment timestamp at origin MSH
    source: data_warehouse.id_network_sla_report_base
    technical_description: "Various calculations based on start_clock_classification; Generally arrival time plus processing buffer"

  - name: target_origin_msh_iv_datetime
    description: Target invan timestamp at origin MSH
    source: data_warehouse.id_network_sla_report_base
    technical_description: "Based on crossdock_transit_flow - uses appropriate departure datetime to next destination"

  - name: target_transitX_poa_datetime
    description: Target point of arrival timestamps at transit hubs
    source: data_warehouse.id_network_sla_report_base.expected_arrival_datetime_at_transitX
    technical_description: "Direct field from source"

  - name: target_transitX_ih_datetime
    description: Target inhouse timestamps at transit hubs
    source: data_warehouse.id_network_sla_report_base
    technical_description: "expected_arrival_datetime_at_transitX plus 1 hour"

  - name: target_transitX_iv_datetime
    description: Target invan timestamps at transit hubs
    source: data_warehouse.id_network_sla_report_base
    technical_description: "Based on crossdock_transit_flow - uses appropriate departure datetime to next destination"

  - name: target_dest_msh_poa_datetime
    description: Target point of arrival timestamp at destination MSH
    source: data_warehouse.id_network_sla_report_base.expected_arrival_datetime_at_dest_msh
    technical_description: "Direct field from source"

  - name: target_dest_msh_ih_datetime
    description: Target inhouse timestamp at destination MSH
    source: data_warehouse.id_network_sla_report_base
    technical_description: "For same MSH: based on origin processing; Otherwise expected_arrival_datetime_at_dest_msh plus 30 minutes"

  - name: target_dest_msh_ib_datetime
    description: Target inbound timestamp at destination MSH
    source: data_warehouse.id_network_sla_report_base
    technical_description: "For same MSH: based on origin processing; Otherwise expected_arrival_datetime_at_dest_msh plus 1.5 hours"

  - name: target_dest_msh_cs_datetime
    description: Target close shipment timestamp at destination MSH
    source: data_warehouse.id_network_sla_report_base
    technical_description: "For same MSH: based on origin processing; Otherwise expected_arrival_datetime_at_dest_msh plus 3 hours"

  - name: target_dest_msh_iv_datetime
    description: Target invan timestamp at destination MSH
    source: data_warehouse.id_network_sla_report_base.expected_departure_datetime_to_dest_hub
    technical_description: "Direct field from source"

  - name: target_dest_hub_poa_datetime
    description: Target point of arrival timestamp at destination hub
    source: data_warehouse.id_network_sla_report_base.expected_arrival_datetime_at_dest_hub
    technical_description: "Direct field from source"

  - name: target_dest_hub_ih_datetime
    description: Target inhouse timestamp at destination hub
    source: data_warehouse.id_network_sla_report_base
    technical_description: "expected_arrival_datetime_at_dest_hub plus 30 minutes"

  - name: target_first_attempt_datetime
    description: Target time for first delivery attempt
    source: data_warehouse.id_network_sla_report_base.expected_first_delivery_attempt_datetime
    technical_description: "Direct field from source"

  - name: first_valid_delivery_attempt_datetime
    description: Actual timestamp of first valid delivery attempt
    source: data_warehouse.transit_time_report.first_valid_delivery_attempt_datetime
    technical_description: "Direct field from source"

  - name: first_attempt_measured
    description: Flag indicating if first attempt SLA was measured
    source: "Derived field"
    technical_description: "1 if both target_first_attempt_datetime and first_valid_delivery_attempt_datetime are not null, otherwise null"

  - name: first_attempt_met
    description: Flag indicating if first attempt SLA was met
    source: "Derived field"
    technical_description: "1 if first_valid_delivery_attempt_datetime <= target_first_attempt_datetime, 0 if not met, null if not measured"

  - name: second_valid_delivery_attempt_datetime
    description: Actual timestamp of second valid delivery attempt
    source: data_warehouse.transit_time_report.second_valid_delivery_attempt_datetime
    technical_description: "Direct field from source"

  - name: third_valid_delivery_attempt_datetime
    description: Actual timestamp of third valid delivery attempt
    source: data_warehouse.transit_time_report.third_valid_delivery_attempt_datetime
    technical_description: "Direct field from source"

  - name: delivery_success_datetime
    description: Timestamp when delivery was successful
    source: data_warehouse.transit_time_report.delivery_success_datetime
    technical_description: "Direct field from source"

  - name: real_order_path
    description: Actual path the order took through the network
    source: data_warehouse.order_hub_timestamps_flat
    technical_description: "String-join of hub_name values ordered by min_seq from order_hub_timestamps_flat"

  - name: expected_order_path
    description: Expected path the order should have taken through the network
    source: "Multiple source tables"
    technical_description: "Concatenated string of hub names based on inbound_hub_name, origin_msh, transit hubs, dest_msh, and dest_hub_name"

  - name: X_Y_measured
    description: Flag indicating if SLA was measured for milestone X at timestamp Y
    source: "Derived field"
    technical_description: "1 if both target and actual timestamps exist, 0 otherwise"

  - name: X_Y_met
    description: Flag indicating if SLA was met for milestone X at timestamp Y
    source: "Derived field"
    technical_description: "1 if actual timestamp <= target timestamp, 0 if not met, null if not measured"

  - name: created_month
    description: Month when the order was created
    source: data_warehouse.id_network_sla_report_base.created_month
    technical_description: "Direct field from source" 