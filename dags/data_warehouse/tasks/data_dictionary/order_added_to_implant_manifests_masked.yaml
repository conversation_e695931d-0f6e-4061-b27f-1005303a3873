# Data dictionary

table_name: order_added_to_implant_manifests

overview:
  purpose: Tracks when orders are added to implant manifests, providing a historical record of when orders were included in implant manifest processing
  granularity: Each row represents a unique order and when it was first added to an implant manifest
  business_rules:
    - Only includes orders that have been added to implant manifests
    - Records the earliest event datetime when an order was added to an implant manifest
    - Joins with the orders table to get additional order context

input_tables:
  - data_warehouse.added_to_implant_manifest_events
  - core_prod_gl.orders

fields:
  - name: order_id
    description: Unique identifier for the order
    source: core_prod_gl.orders.id
    technical_description: "Direct field from source"

  - name: event_datetime
    description: Timestamp when the order was first added to an implant manifest
    source: data_warehouse.added_to_implant_manifest_events.event_datetime
    technical_description: "MIN(event_datetime) grouped by order_id from added_to_implant_manifest_events"

  - name: created_month
    description: Month when the order was created, used for partitioning
    source: core_prod_gl.orders.created_at
    technical_description: "DATE_FORMAT(orders.created_at, 'yyyy-MM')" 