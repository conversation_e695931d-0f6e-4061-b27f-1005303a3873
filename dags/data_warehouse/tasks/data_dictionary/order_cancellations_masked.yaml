# Data dictionary

table_name: order_cancellations

overview:
  purpose: Tracks order cancellation events by recording when orders were cancelled
  granularity: Each row represents a unique cancelled order with its cancellation timestamp
  business_rules:
    - Only includes orders that have a cancellation event
    - Takes the earliest cancellation event timestamp when multiple cancellations exist for an order
    - Joins with orders table to ensure only valid orders are included

input_tables:
  - data_warehouse.cancelled_events
  - core_prod_gl.orders

fields:
  - name: order_id
    description: Unique identifier for the order
    source: core_prod_gl.orders.id
    technical_description: "Direct field from source, joined with cancelled events"

  - name: event_datetime
    description: Timestamp when the order was cancelled
    source: data_warehouse.cancelled_events.created_at
    technical_description: "MIN(created_at) from cancelled_events grouped by order_id"

  - name: created_month
    description: Month and year when the order was created
    source: core_prod_gl.orders.created_at
    technical_description: "DATE_FORMAT(orders.created_at, 'yyyy-MM')" 