# Data dictionary

table_name: rts_trigger_events

overview:
  purpose: Tracks Return to Sender (RTS) trigger events for orders, capturing when an order is marked for return and the associated reasons.
  granularity: Each row represents a single RTS event for a specific order.
  business_rules:
    - For events before 2019-11-14, includes both direct RTS events (type 6) and PETS tickets resolved with RTS outcome (type 44)
    - For events after 2019-11-14, only includes direct RTS events (type 6)

input_tables:
  - events_prod_gl.order_events

fields:
  - name: order_event_id
    description: Unique identifier for the RTS event
    source: events_prod_gl.order_events.id
    technical_description: "Direct field from source"

  - name: order_id
    description: Unique identifier of the order
    source: events_prod_gl.order_events.order_id
    technical_description: "Cast to bigint from source order_id"

  - name: system_id
    description: Identifier of the system where the event originated
    source: events_prod_gl.order_events.system_id
    technical_description: "Direct field from source"

  - name: order_event_type_id
    description: Type identifier for the order event
    source: events_prod_gl.order_events.type
    technical_description: "Direct field from source, filtered for type 6 (RTS) and historically type 44 (TICKET_RESOLVED with RTS outcome)"

  - name: rts_reason
    description: Reason provided for the Return to Sender action
    source: events_prod_gl.order_events.data
    technical_description: "Extracted from JSON data field using get_json_object(data, '$.reason')"

  - name: event_datetime
    description: Timestamp when the RTS event occurred in local timezone
    source: events_prod_gl.order_events.time
    technical_description: "Converted from UTC to local timezone based on system_id using from_utc_timestamp()"

  - name: created_month
    description: Month and year when the event was created
    source: events_prod_gl.order_events.time
    technical_description: "Formatted from time field using date_format(time, 'yyyy-MM')" 