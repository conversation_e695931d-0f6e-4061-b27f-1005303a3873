# Data dictionary

table_name: delivery_transaction_events

overview:
  purpose: Tracks delivery transaction events including successful deliveries, failures, and RTS (Return to Station) events. This table provides detailed information about delivery attempts, their outcomes, and associated geographical and operational data.
  granularity: Each row represents a single delivery transaction attempt for an order
  business_rules:
    - Only includes transactions of type 'DD' (Direct Delivery)
    - Non-deleted transactions only (deleted_at is null)
    - Valid flag is set to 0 for specific failure reason codes (5, 6, and 13 after 2021-10-18)
    - Type is determined based on timing relative to RTS events (with 5-minute buffer)

input_tables:
  - data_warehouse.order_rts_triggers
  - data_warehouse.delivery_failure_events
  - core_prod_gl.transactions
  - core_prod_gl.transaction_failure_reason
  - route_prod_gl.route_logs
  - route_prod_gl.waypoints
  - addressing_prod_gl.zones
  - driver_prod_gl.failure_reasons
  - order_prod_gl.multi_piece_shipments
  - order_prod_gl.multi_piece_shipment_orders

fields:
  - name: transaction_id
    description: Unique identifier for the delivery transaction
    source: core_prod_gl.transactions.id
    technical_description: "Direct field from source"

  - name: order_id
    description: Unique identifier for the order
    source: core_prod_gl.transactions.order_id
    technical_description: "Direct field from source"

  - name: status
    description: Current status of the delivery transaction
    source: core_prod_gl.transactions.status
    technical_description: "Direct field from source, possible values include 'Success', 'Forced Success', 'Fail', 'Pending', 'Cancelled'"

  - name: postcode
    description: Postal code of the delivery location
    source: core_prod_gl.transactions.postcode
    technical_description: "Direct field from source"

  - name: transit_flag
    description: Indicates if the delivery is in transit
    source: core_prod_gl.transactions.transit
    technical_description: "Cast from source transit field to bigint"

  - name: failure_reason_id
    description: Identifier for the reason of delivery failure
    source: 
      - core_prod_gl.transaction_failure_reason.failure_reason_id
      - driver_prod_gl.failure_reasons.failure_reason_id
    technical_description: "For TH: Coalesced from transaction_failure_reason and delivery_failure_events. For others: Directly from transaction_failure_reason"

  - name: event_datetime
    description: Local timestamp when the delivery event occurred
    source: core_prod_gl.transactions.service_end_time
    technical_description: "Converted from UTC to local timezone based on system_id"

  - name: valid_flag
    description: Indicates if the delivery attempt is considered valid
    source: 
      - core_prod_gl.transactions.status
      - core_prod_gl.transactions.failure_reason_code_id
      - core_prod_gl.transactions.created_at
    technical_description: "Set to null if service_end_time is null or status not in ('Success', 'Forced Success', 'Fail'). Set to 0 if status is 'Fail' and failure_reason_code_id in (5, 6) or (failure_reason_code_id = 13 and created_at >= '2021-10-18'). Set to 1 otherwise"

  - name: type
    description: Categorizes the event as either delivery or RTS
    source: 
      - core_prod_gl.transactions.service_end_time
      - core_prod_gl.transactions.status
      - core_prod_gl.transactions.created_at
      - core_prod_gl.transactions.updated_at
      - data_warehouse.order_rts_triggers.event_datetime
    technical_description: "Complex logic based on service_end_time, status, and timing relative to RTS event_datetime with 5-minute buffer"

  - name: timeslot_start
    description: Start time of the delivery timeslot
    source: core_prod_gl.transactions.start_time
    technical_description: "Converted from UTC to local timezone and formatted as HH:mm:ss"

  - name: timeslot_end
    description: End time of the delivery timeslot
    source: core_prod_gl.transactions.end_time
    technical_description: "Converted from UTC to local timezone and formatted as HH:mm:ss"

  - name: sla_datetime
    description: Service Level Agreement deadline in local timezone
    source: core_prod_gl.transactions.sla_date
    technical_description: "Converted from UTC to local timezone"

  - name: route_id
    description: Identifier for the delivery route
    source: core_prod_gl.transactions.route_id
    technical_description: "Direct field from source"

  - name: route_hub_id
    description: Identifier for the hub associated with the route
    source: route_prod_gl.route_logs.hub_id
    technical_description: "Cast to bigint from route_logs hub_id"

  - name: route_driver_id
    description: Identifier for the driver assigned to the route
    source: route_prod_gl.route_logs.driver_id
    technical_description: "Cast to bigint from route_logs driver_id"

  - name: route_zone
    description: Name of the zone associated with the route
    source: addressing_prod_gl.zones.name
    technical_description: "Joined from zones table using route.zone_id = zones.legacy_zone_id"

  - name: route_zone_short_name
    description: Short name of the zone associated with the route
    source: addressing_prod_gl.zones.short_name
    technical_description: "Joined from zones table using route.zone_id = zones.legacy_zone_id"

  - name: waypoint_id
    description: Identifier for the delivery waypoint
    source: core_prod_gl.transactions.waypoint_id
    technical_description: "Cast to bigint from source waypoint_id"

  - name: waypoint_timewindow_id
    description: Identifier for the waypoint's time window
    source: route_prod_gl.waypoints.timewindow_id
    technical_description: "Direct field from waypoints table"

  - name: waypoint_latitude
    description: Latitude of the delivery waypoint
    source: route_prod_gl.waypoints.latitude
    technical_description: "Direct field from waypoints table"

  - name: waypoint_longitude
    description: Longitude of the delivery waypoint
    source: route_prod_gl.waypoints.longitude
    technical_description: "Direct field from waypoints table"

  - name: legacy_zone_id
    description: Legacy identifier for the zone
    source: addressing_prod_gl.zones.legacy_zone_id
    technical_description: "Joined from zones table using waypoints.routing_zone_id"

  - name: waypoint_zone
    description: Name of the zone associated with the waypoint
    source: addressing_prod_gl.zones.name
    technical_description: "Joined from zones table using waypoints.routing_zone_id"

  - name: waypoint_zone_short_name
    description: Short name of the zone associated with the waypoint
    source: addressing_prod_gl.zones.short_name
    technical_description: "Joined from zones table using waypoints.routing_zone_id"

  - name: waypoint_zone_hub_id
    description: Identifier for the hub associated with the waypoint's zone
    source: addressing_prod_gl.zones.hub_id
    technical_description: "Direct field from zones table joined through waypoint"

  - name: dest_hub_id
    description: Identifier for the destination hub
    source: 
      - route_prod_gl.route_logs.hub_id
      - addressing_prod_gl.zones.hub_id
    technical_description: "Coalesced from route.hub_id and waypoint_zone.hub_id with system-specific ordering, cast to bigint"

  - name: dest_zone
    description: Name of the destination zone
    source: 
      - addressing_prod_gl.zones.name
    technical_description: "Coalesced from route_zone.name and waypoint_zone.name with system-specific ordering"

  - name: dpms_id
    description: Distribution Point Management System identifier
    source: core_prod_gl.transactions.distribution_point_id
    technical_description: "Direct field from source distribution_point_id"

  - name: bundle_tracking_id
    description: Tracking identifier for bundled orders
    source: order_prod_gl.multi_piece_shipments.tracking_number
    technical_description: "Joined through multi_piece_shipment_orders using order_id"

  - name: creation_datetime
    description: Timestamp when the transaction was created in local timezone
    source: core_prod_gl.transactions.created_at
    technical_description: "Converted from UTC to local timezone"

  - name: created_month
    description: Month and year when the transaction was created
    source: core_prod_gl.transactions.created_at
    technical_description: "Formatted as yyyy-MM from created_at timestamp" 