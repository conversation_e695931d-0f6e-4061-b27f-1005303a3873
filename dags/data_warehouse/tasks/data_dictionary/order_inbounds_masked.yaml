# Data dictionary

table_name: order_inbounds

overview:
  purpose: Captures the first warehouse scan event for each order, providing information about where and when orders first enter the warehouse system.
  granularity: Each row represents the first warehouse scan event for a unique order.
  business_rules:
    - Only includes orders that have at least one warehouse scan event
    - Takes only the first warehouse scan event per order based on event_datetime
    - Orders are joined with core orders table to get creation date information

input_tables:
  - data_warehouse.warehouse_scan_events
  - core_prod_gl.orders

fields:
  - name: order_id
    description: Unique identifier of the order being scanned
    source: data_warehouse.warehouse_scan_events.order_id
    technical_description: "Direct field from source"

  - name: scan_hub_id
    description: Identifier of the hub where the scan occurred
    source: data_warehouse.warehouse_scan_events.scan_hub_id
    technical_description: "Direct field from source"

  - name: route_id
    description: Identifier of the route associated with the scan, if applicable
    source: data_warehouse.warehouse_scan_events.route_id
    technical_description: "Direct field from source"

  - name: route_driver_id
    description: Identifier of the driver associated with the route during the scan
    source: data_warehouse.warehouse_scan_events.route_driver_id
    technical_description: "Direct field from source"

  - name: route_hub_id
    description: Identifier of the hub associated with the route
    source: data_warehouse.warehouse_scan_events.route_hub_id
    technical_description: "Direct field from source"

  - name: type
    description: Classification of the scan event
    source: data_warehouse.warehouse_scan_events.type
    technical_description: "Direct field from source"

  - name: source_table
    description: Indicates which source table the record came from
    source: data_warehouse.warehouse_scan_events.source_table
    technical_description: "Direct field from source"

  - name: event_datetime
    description: Local timestamp when the scan event occurred
    source: data_warehouse.warehouse_scan_events.event_datetime
    technical_description: "Direct field from source"

  - name: created_month
    description: Month and year when the order was created
    source: core_prod_gl.orders.created_at
    technical_description: "Derived from orders.created_at using date_format(created_at, 'yyyy-MM')" 