# Data dictionary

table_name: driver_types_enriched

overview:
  purpose: Provides a comprehensive view of driver types with enriched information including fleet categorization, hub assignments, and vehicle types. This table combines core driver type data with additional operational attributes to support fleet management and analysis.
  granularity: Each row represents a unique driver type configuration for a specific country
  business_rules:
    - Includes both active and deleted driver types
    - Driver types are enriched with additional attributes from GDrive data
    - Data is partitioned by system_id (country) and created_month

input_tables:
  - driver_prod_gl.driver_types
  - gdrive.driver_types_enriched

fields:
  - name: country
    description: Country identifier for the driver type record
    source: driver_prod_gl.driver_types.system_id
    technical_description: "Direct field from source"

  - name: id
    description: Unique identifier for the driver type
    source: driver_prod_gl.driver_types.id
    technical_description: "Direct field from source"

  - name: driver_type
    description: Name or label of the driver type
    source: driver_prod_gl.driver_types.name
    technical_description: "Direct field from source, renamed from name to driver_type"

  - name: hub
    description: Hub assignment for the driver type
    source: gdrive.driver_types_enriched.hub
    technical_description: "Direct field from source through left join with driver_types_enriched_gdrive"

  - name: fleet_type
    description: Classification of the fleet category
    source: gdrive.driver_types_enriched.fleet_type
    technical_description: "Direct field from source through left join with driver_types_enriched_gdrive"

  - name: scheme_type
    description: Type of scheme applied to the driver type
    source: gdrive.driver_types_enriched.scheme_type
    technical_description: "Direct field from source through left join with driver_types_enriched_gdrive"

  - name: veh_type
    description: Type of vehicle associated with the driver type
    source: gdrive.driver_types_enriched.veh_type
    technical_description: "Direct field from source through left join with driver_types_enriched_gdrive"

  - name: creation_date
    description: Date when the driver type was created
    source: driver_prod_gl.driver_types.created_at
    technical_description: "Converted from created_at timestamp to date in local timezone using from_utc_timestamp"

  - name: is_deleted
    description: Flag indicating if the driver type has been deleted
    source: driver_prod_gl.driver_types.deleted_at
    technical_description: "Derived field: 1 if deleted_at is not null, 0 otherwise"

  - name: system_id
    description: System identifier for the country
    source: driver_prod_gl.driver_types.system_id
    technical_description: "Direct field from source"

  - name: created_month
    description: Month and year when the driver type was created
    source: driver_prod_gl.driver_types.created_at
    technical_description: "Derived from created_at formatted as yyyy-MM" 