# Data dictionary

table_name: implanted_manifest_scan_events_enriched

overview:
  purpose: Aggregates implanted manifest scan events to provide a summary of when orders were added to implant manifests, tracking the earliest scan time for each order.
  granularity: Each row represents an order's implant manifest scan events aggregated by system_id
  business_rules:
    - Only includes events of type IMPLANTED_MANIFEST_SCAN (type 68)
    - Groups events by order_id and system_id to get the earliest implant manifest datetime
    - Partitioned by created_month

input_tables:
  - data_warehouse.implanted_manifest_scan_events

fields:
  - name: order_id
    description: Unique identifier for the order
    source: data_warehouse.implanted_manifest_scan_events.order_id
    technical_description: "Direct field from source"

  - name: system_id
    description: Identifier for the operating country/system
    source: data_warehouse.implanted_manifest_scan_events.system_id
    technical_description: "Direct field from source"

  - name: created_month
    description: Month when the implant manifest scan event was created
    source: data_warehouse.implanted_manifest_scan_events.created_month
    technical_description: "MIN(created_month) grouped by order_id and system_id"

  - name: min_implant_manifest_datetime
    description: Earliest timestamp when the order was added to an implant manifest
    source: data_warehouse.implanted_manifest_scan_events.implant_manifest_datetime
    technical_description: "MIN(implant_manifest_datetime) grouped by order_id and system_id" 