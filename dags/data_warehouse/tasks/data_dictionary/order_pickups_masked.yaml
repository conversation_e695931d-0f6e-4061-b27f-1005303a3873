# Data dictionary

table_name: order_pickups

overview:
  purpose: Tracks and aggregates pickup attempt information for orders, including first attempt details, successful pickup information, and pickup metrics.
  granularity: Each row represents pickup information for a single order
  business_rules:
    - Includes both successful and failed pickup attempts
    - Tracks first attempt details separately from successful pickup details
    - Distinguishes between third-party (Mitra - Fleet in ID) and NinjaVan pickups
    - Only includes orders that have at least one pickup scan event

input_tables:
  - data_warehouse.pickup_scan_events
  - data_warehouse.drivers_enriched
  - core_prod_gl.orders

fields:
  - name: order_id
    description: Unique identifier for the order
    source: core_prod_gl.orders.id
    technical_description: "Direct field from source"

  - name: first_attempt_failure_reason_id
    description: Reason ID for failure if the first pickup attempt failed
    source: data_warehouse.pickup_scan_events.failure_reason_id
    technical_description: "First failure reason ID based on earliest event_datetime"

  - name: first_attempt_reservation_id
    description: Reservation ID associated with the first pickup attempt
    source: data_warehouse.pickup_scan_events.reservation_id
    technical_description: "Reservation ID from the earliest pickup attempt based on event_datetime"

  - name: first_attempt_route_hub_id
    description: Hub ID associated with the first pickup attempt
    source: data_warehouse.pickup_scan_events.route_hub_id
    technical_description: "Route hub ID from the earliest pickup attempt based on event_datetime"

  - name: first_attempt_datetime
    description: Timestamp of the first pickup attempt
    source: data_warehouse.pickup_scan_events.event_datetime
    technical_description: "Minimum event_datetime for the order from pickup_scan_events"

  - name: success_route_hub_id
    description: Hub ID associated with the successful pickup
    source: data_warehouse.pickup_scan_events.route_hub_id
    technical_description: "Route hub ID from the successful pickup event where status = 'Success'"

  - name: success_route_driver_id
    description: Driver ID who successfully picked up the order
    source: data_warehouse.pickup_scan_events.route_driver_id
    technical_description: "Driver ID from the successful pickup event where status = 'Success'"

  - name: success_datetime
    description: Timestamp when the order was successfully picked up
    source: data_warehouse.pickup_scan_events.event_datetime
    technical_description: "Minimum event_datetime where status = 'Success'"

  - name: nv_success_datetime
    description: Timestamp when the order was successfully picked up by a NinjaVan driver (excluding third-party pickups)
    source: data_warehouse.pickup_scan_events.event_datetime
    technical_description: "Minimum event_datetime where status = 'Success' and third_party_pickup_flag = 0"

  - name: total_attempts
    description: Total number of pickup attempts for the order
    source: data_warehouse.pickup_scan_events
    technical_description: "Count of all pickup scan events for the order"

  - name: created_month
    description: Month and year when the order was created
    source: core_prod_gl.orders.created_at
    technical_description: "Formatted as YYYY-MM from orders.created_at" 