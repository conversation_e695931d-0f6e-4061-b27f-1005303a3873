# Data dictionary

table_name: id_network_sla_report_base

overview:
  purpose: Provides the base data for network SLA reporting by calculating expected timestamps for each milestone in an order's journey through the network, from pickup to delivery attempt
  granularity: Each row represents a single completed order with its expected timestamps at each network milestone
  business_rules:
    - Only includes orders with completed delivery status
    - Only includes orders with successful delivery
    - Calculates expected timestamps based on linehaul schedules and operational buffers
    - Considers both direct routes and routes with transit hubs
    - Accounts for different pickup waves (0-17 hours and 18-23 hours)

input_tables:
  - data_warehouse.last_mile_push_off_cutoffs
  - gsheets.id_af_sf_schedules
  - gsheets.id_hub_connections
  - gsheets.id_milkrun_connections
  - data_warehouse.shipment_orders_enriched
  - data_warehouse.transit_time_report
  - data_warehouse.hubs_enriched
  - data_warehouse.hub_relation_schedules_enriched

fields:
  - name: created_month
    description: Month when the order was created
    source: data_warehouse.transit_time_report.delivery_success_datetime
    technical_description: "Extract YYYY-MM from delivery_success_datetime"

  - name: order_id
    description: Unique identifier for the order
    source: data_warehouse.transit_time_report.order_id
    technical_description: "Direct field from source"

  - name: origin_hub_id
    description: ID of the origin hub where the order started
    source: data_warehouse.transit_time_report.origin_hub_id
    technical_description: "Direct field from source"

  - name: pickup_hub_id
    description: ID of the hub where the order was picked up
    source: data_warehouse.transit_time_report.pickup_hub_id
    technical_description: "Direct field from source"

  - name: inbound_hub_id
    description: ID of the hub where the order was first inbounded
    source: data_warehouse.transit_time_report.inbound_hub_id
    technical_description: "Direct field from source"

  - name: origin_msh_hub_id
    description: ID of the origin main sorting hub
    source: data_warehouse.hubs_enriched.id
    technical_description: "Lookup hub ID based on origin_msh name"

  - name: origin_msh
    description: Name of the origin main sorting hub
    source: data_warehouse.transit_time_report
    technical_description: "Derived from inbound_hub_name using format: first 3 letters + '-' + first 3 letters"

  - name: transit1_hub through transit6_hub
    description: Names of transit hubs in the order's journey
    source: gsheets.id_hub_connections
    technical_description: "Lookup from hub_connections based on origin and destination MSH"

  - name: dest_msh_hub_id
    description: ID of the destination main sorting hub
    source: data_warehouse.hubs_enriched.id
    technical_description: "Lookup hub ID based on dest_msh name"

  - name: dest_msh
    description: Name of the destination main sorting hub
    source: data_warehouse.transit_time_report
    technical_description: "Derived from dest_hub_name using format: first 3 letters + '-' + first 3 letters"

  - name: dest_hub_id
    description: ID of the final destination hub
    source: data_warehouse.transit_time_report.dest_hub_id
    technical_description: "Direct field from source"

  - name: dest_hub_name
    description: Name of the final destination hub
    source: data_warehouse.transit_time_report.dest_hub_name
    technical_description: "Direct field from source"

  - name: pickup_datetime
    description: Timestamp when the order was picked up
    source: data_warehouse.transit_time_report.pickup_datetime
    technical_description: "Direct field from source"

  - name: expected_poh_hour_adjustment
    description: Hour adjustment for pickup on hand based on pickup time
    source: data_warehouse.transit_time_report.pickup_datetime
    technical_description: "If pickup hour is between 0-17, set to 20; if between 18-23, set to 25"

  - name: inbound_datetime
    description: Timestamp when the order was first inbounded
    source: data_warehouse.transit_time_report.inbound_datetime
    technical_description: "Direct field from source"

  - name: start_clock_datetime
    description: Timestamp when SLA measurement starts
    source: data_warehouse.transit_time_report.start_clock_datetime
    technical_description: "Direct field from source"

  - name: start_clock_classification
    description: Classification of how the order entered the network
    source: "Multiple source tables"
    technical_description: "Classified as: pickup_msh_inbound, pickup_station_inbound, direct_msh_inbound, or direct_station_inbound based on pickup and inbound locations"

  - name: start_clock_granular_classification
    description: Detailed classification of how the order entered the network
    source: "Multiple source tables"
    technical_description: "Classified as: normal_msh_pu_ib, st_pu_direct_ats, st_pu_msh_ib, msh_pu_st_ib, normal_st_pu_ib, direct_msh_ib, direct_st_ib based on pickup and inbound patterns"

  - name: crossdock_transit_flow
    description: Classification of the order's transit flow through crossdock hubs
    source: "gsheets.id_hub_connections"
    technical_description: "Classified as: same_msh, direct, or X_transit (where X is 1-6) based on number of transit hubs"

  - name: milkrun_transit_flow
    description: Classification of the order's transit flow through milkrun hubs
    source: "gsheets.id_milkrun_connections"
    technical_description: "Classified as: direct or X_transit (where X is 1-7) based on number of milkrun transit hubs"

  - name: cdst_data_issue
    description: Indicator of any data issues in the crossdock-station transit flow
    source: "Derived field"
    technical_description: "Flags issues in FM STCD, CDCD, or specific hub connections where expected timestamps are missing"

  - name: expected_departure_datetime_to_origin_msh
    description: Expected departure time to origin main sorting hub
    source: "Multiple source tables"
    technical_description: "Calculated based on cutoff times, linehaul schedules, and operational buffers from pickup/inbound location to origin MSH"

  - name: expected_arrival_datetime_at_origin_msh
    description: Expected arrival time at origin main sorting hub
    source: "Multiple source tables"
    technical_description: "Calculated as expected_departure_datetime_to_origin_msh plus transit duration from schedules"

  - name: expected_departure_datetime_to_transitX
    description: Expected departure times to each transit hub
    source: "Multiple source tables"
    technical_description: "Calculated based on previous hub arrival time, processing time, and linehaul schedules"

  - name: expected_arrival_datetime_at_transitX
    description: Expected arrival times at each transit hub
    source: "Multiple source tables"
    technical_description: "Calculated as expected_departure_datetime_to_transitX plus transit duration from schedules"

  - name: expected_departure_datetime_to_dest_msh
    description: Expected departure time to destination main sorting hub
    source: "Multiple source tables"
    technical_description: "Calculated based on last transit hub arrival or origin MSH processing time and linehaul schedules"

  - name: expected_arrival_datetime_at_dest_msh
    description: Expected arrival time at destination main sorting hub
    source: "Multiple source tables"
    technical_description: "Calculated as expected_departure_datetime_to_dest_msh plus transit duration from schedules"

  - name: expected_departure_datetime_to_dest_hub
    description: Expected departure time to final destination hub
    source: "Multiple source tables"
    technical_description: "For direct routes: calculated from dest_msh; For milkrun routes: same as expected_departure_datetime_to_milkrun1"

  - name: expected_arrival_datetime_at_dest_hub
    description: Expected arrival time at final destination hub
    source: "Multiple source tables"
    technical_description: "Calculated as expected_departure_datetime_to_dest_hub plus transit duration from schedules"

  - name: expected_first_delivery_attempt_datetime
    description: Expected time of first delivery attempt
    source: "data_warehouse.last_mile_push_off_cutoffs"
    technical_description: "If arrival at dest_hub is before cutoff time, set to 23:59:59 same day; otherwise set to 23:59:59 next day" 