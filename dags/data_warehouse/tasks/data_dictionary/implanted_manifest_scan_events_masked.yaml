# Data dictionary

table_name: implanted_manifest_scan_events

overview:
  purpose: Tracks when orders are scanned into implanted manifests, providing a record of when orders are included in delivery manifests
  granularity: Each row represents a single implanted manifest scan event for an order
  business_rules:
    - Only includes events of type 68 (IMPLANTED_MANIFEST_SCAN)
    - Events are partitioned by system_id and created_month

input_tables:
  - events_prod_gl.order_events

fields:
  - name: order_event_id
    description: Unique identifier for the implanted manifest scan event
    source: events_prod_gl.order_events.id
    technical_description: "Direct field from source"

  - name: order_id
    description: Unique identifier for the order
    source: events_prod_gl.order_events.order_id
    technical_description: "Direct field from source"

  - name: system_id
    description: Identifier for the system where the event originated
    source: events_prod_gl.order_events.system_id
    technical_description: "Direct field from source"

  - name: implant_manifest_datetime
    description: Timestamp when the order was scanned into the implanted manifest in local timezone
    source: events_prod_gl.order_events.created_at
    technical_description: "Converted from UTC to local timezone based on system_id using from_utc_timestamp function"

  - name: created_month
    description: Month and year when the implanted manifest scan occurred
    source: events_prod_gl.order_events.created_at
    technical_description: "Derived from implant_manifest_datetime formatted as 'yyyy-MM'" 