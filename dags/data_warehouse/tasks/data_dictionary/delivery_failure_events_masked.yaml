# Data dictionary

table_name: delivery_failure_events

overview:
  purpose: Tracks delivery failure events for orders, capturing details about failed delivery attempts including the driver, route, and failure reasons
  granularity: Each row represents a single delivery failure event for an order
  business_rules:
    - Only includes events of type 38 (DELIVERY_FAILURE)
    - Events are partitioned by system_id and created_month
    - Event timestamps are converted to local timezone based on system_id

input_tables:
  - events_prod_gl.order_events

fields:
  - name: order_event_id
    description: Unique identifier for the delivery failure event
    source: events_prod_gl.order_events.id
    technical_description: "Direct field from source"

  - name: order_id
    description: Identifier of the order
    source: events_prod_gl.order_events.order_id
    technical_description: "Direct field from source"

  - name: driver_id
    description: Identifier of the driver who attempted the delivery
    source: events_prod_gl.order_events.data
    technical_description: "Extracted from JSON data field using get_json_object(data, '$.driver_id') and cast as bigint"

  - name: route_id
    description: Identifier of the delivery route
    source: events_prod_gl.order_events.data
    technical_description: "Extracted from JSON data field using get_json_object(data, '$.route_id') and cast as bigint"

  - name: waypoint_id
    description: Identifier of the waypoint in the delivery route
    source: events_prod_gl.order_events.data
    technical_description: "Extracted from JSON data field using get_json_object(data, '$.waypoint_id') and cast as bigint"

  - name: transaction_id
    description: Identifier of the transaction associated with the delivery failure
    source: events_prod_gl.order_events.data
    technical_description: "Extracted from JSON data field using get_json_object(data, '$.transaction_id') and cast as bigint"

  - name: failure_reason_id
    description: Identifier indicating the reason for delivery failure
    source: events_prod_gl.order_events.data
    technical_description: "Extracted from JSON data field using get_json_object(data, '$.failure_reason_id') and cast as integer"

  - name: type
    description: Event type identifier for delivery failure
    source: events_prod_gl.order_events.type
    technical_description: "Hard-coded as '38' representing DELIVERY_FAILURE event type"

  - name: event_datetime
    description: Timestamp of when the delivery failure occurred in local timezone
    source: events_prod_gl.order_events.created_at
    technical_description: "Converted from UTC to local timezone using from_utc_timestamp based on system_id"

  - name: system_id
    description: Identifier of the system where the event originated
    source: events_prod_gl.order_events.system_id
    technical_description: "Direct field from source"

  - name: created_month
    description: Month and year when the delivery failure event was created
    source: events_prod_gl.order_events.created_at
    technical_description: "Formatted from created_at using date_format(created_at, 'yyyy-MM')" 