# Data dictionary

table_name: order_destinations

overview:
  purpose: Tracks the destination information for orders including route, waypoint, and destination hubs and zones for both delivery and RTS (Return to Sender) scenarios
  granularity: Each row represents destination information for a single order
  business_rules:
    - Includes both delivery and RTS destination information when available
    - For RTS orders, the delivery destination hub is derived from the last address verification event before RTS if direct delivery destination is not available
    - Data is partitioned by order creation month

input_tables:
  - data_warehouse.delivery_transaction_events
  - data_warehouse.update_address_verification_events
  - data_warehouse.rts_trigger_events
  - data_warehouse.hubs_enriched
  - core_prod_gl.orders

fields:
  - name: order_id
    description: Unique identifier for the order
    source: core_prod_gl.orders.id
    technical_description: "Direct field from source"

  - name: tracking_id
    description: Tracking ID for the order
    source: core_prod_gl.orders.tracking_id
    technical_description: "Direct field from source"

  - name: delivery_route_hub_id
    description: Hub ID for the delivery route
    source: data_warehouse.delivery_transaction_events.route_hub_id
    technical_description: "Latest route hub ID from delivery transaction events"

  - name: delivery_waypoint_zone_hub_id
    description: Hub ID for the delivery waypoint zone
    source: data_warehouse.delivery_transaction_events.waypoint_zone_hub_id
    technical_description: "Latest waypoint zone hub ID from delivery transaction events"

  - name: delivery_dest_hub_id
    description: Hub ID for the delivery destination
    source: |
      data_warehouse.delivery_transaction_events.dest_hub_id,
      data_warehouse.update_address_verification_events.av_hub_id
    technical_description: "COALESCE(delivery_transaction_events.dest_hub_id, last_verified_or_unverified_av_hub_id_before_rts)"

  - name: delivery_route_zone
    description: Zone code for the delivery route
    source: data_warehouse.delivery_transaction_events.route_zone
    technical_description: "Latest route zone from delivery transaction events"

  - name: delivery_waypoint_zone
    description: Zone code for the delivery waypoint
    source: data_warehouse.delivery_transaction_events.waypoint_zone
    technical_description: "Latest waypoint zone from delivery transaction events"

  - name: delivery_dest_zone
    description: Zone code for the delivery destination
    source: data_warehouse.delivery_transaction_events.dest_zone
    technical_description: "Latest destination zone from delivery transaction events"

  - name: rts_route_hub_id
    description: Hub ID for the RTS route
    source: data_warehouse.delivery_transaction_events.route_hub_id
    technical_description: "Latest route hub ID from RTS transaction events"

  - name: rts_waypoint_zone_hub_id
    description: Hub ID for the RTS waypoint zone
    source: data_warehouse.delivery_transaction_events.waypoint_zone_hub_id
    technical_description: "Latest waypoint zone hub ID from RTS transaction events"

  - name: rts_dest_hub_id
    description: Hub ID for the RTS destination
    source: data_warehouse.delivery_transaction_events.dest_hub_id
    technical_description: "Latest destination hub ID from RTS transaction events"

  - name: rts_route_zone
    description: Zone code for the RTS route
    source: data_warehouse.delivery_transaction_events.route_zone
    technical_description: "Latest route zone from RTS transaction events"

  - name: rts_waypoint_zone
    description: Zone code for the RTS waypoint
    source: data_warehouse.delivery_transaction_events.waypoint_zone
    technical_description: "Latest waypoint zone from RTS transaction events"

  - name: rts_dest_zone
    description: Zone code for the RTS destination
    source: data_warehouse.delivery_transaction_events.dest_zone
    technical_description: "Latest destination zone from RTS transaction events"

  - name: created_month
    description: Month when the order was created
    source: core_prod_gl.orders.created_at
    technical_description: "DATE_FORMAT(orders.created_at, 'yyyy-MM')" 