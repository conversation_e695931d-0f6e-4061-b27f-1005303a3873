# Data dictionary

table_name: cancelled_events

overview:
  purpose: Tracks order cancellation events, providing a record of when orders were cancelled
  granularity: Each row represents a single cancellation event for an order
  business_rules:
    - Only includes events where the type is 2 (CANCELED)
    - Timestamps are converted to local timezone based on system_id
    - Events are partitioned by system_id and created_month

input_tables:
  - hub_prod_gl.order_events

fields:
  - name: id
    description: Unique identifier for the cancellation event
    source: hub_prod_gl.order_events.id
    technical_description: "Direct field from source"

  - name: order_id
    description: Unique identifier of the order that was cancelled
    source: hub_prod_gl.order_events.order_id
    technical_description: "Direct field from source"

  - name: type
    description: Event type identifier indicating cancellation
    source: hub_prod_gl.order_events.type
    technical_description: "Hardcoded value of '2' representing cancellation events"

  - name: scan_type_name
    description: Human-readable name for the event type
    source: hub_prod_gl.order_events.type
    technical_description: "Hardcoded value of 'cancelled' for all records"

  - name: created_at
    description: Timestamp when the cancellation event occurred, in local timezone
    source: hub_prod_gl.order_events.created_at
    technical_description: "Converted from UTC to local timezone based on system_id using from_utc_timestamp function"

  - name: system_id
    description: Identifier of the system where the event originated
    source: hub_prod_gl.order_events.system_id
    technical_description: "Direct field from source"

  - name: created_month
    description: Month and year when the cancellation event occurred
    source: hub_prod_gl.order_events.created_at
    technical_description: "Derived using date_format(created_at, 'yyyy-MM')" 