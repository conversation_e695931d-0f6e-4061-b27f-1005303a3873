# Data dictionary

table_name: order_dp_milestones

overview:
  purpose: Tracks key delivery provider (DP) milestones for orders, specifically when orders are assigned to DPs and drivers
  granularity: Each row represents DP milestone events for a single order
  business_rules:
    - Only includes orders that have DP-related events
    - Captures the first occurrence of shipper-to-DP and driver-to-DP events
    - Events are identified by specific type_ids (20 for shipper-to-DP, 22 for driver-to-DP)

input_tables:
  - data_warehouse.dp_order_events
  - core_prod_gl.orders

fields:
  - name: order_id
    description: Unique identifier for the order
    source: core_prod_gl.orders.id
    technical_description: "Direct field from source"

  - name: shipper_to_dp_dpms_id
    description: DP management system ID when order was assigned to DP
    source: data_warehouse.dp_order_events.dpms_id
    technical_description: "First dpms_id when type_id = 20, determined using min_by(dpms_id, event_datetime)"

  - name: shipper_to_dp_datetime
    description: Timestamp when order was assigned to DP
    source: data_warehouse.dp_order_events.event_datetime
    technical_description: "First event_datetime when type_id = 20"

  - name: driver_to_dp_dpms_id
    description: DP management system ID when order was assigned to driver
    source: data_warehouse.dp_order_events.dpms_id
    technical_description: "First dpms_id when type_id = 22, determined using min_by(dpms_id, event_datetime)"

  - name: driver_to_dp_datetime
    description: Timestamp when order was assigned to driver
    source: data_warehouse.dp_order_events.event_datetime
    technical_description: "First event_datetime when type_id = 22"

  - name: created_month
    description: Month and year when the order was created
    source: core_prod_gl.orders.created_at
    technical_description: "Formatted as YYYY-MM from orders.created_at using date_format function" 