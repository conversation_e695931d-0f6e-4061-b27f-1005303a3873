# Data dictionary

table_name: xb_outbound_orders

overview:
  purpose: Identifies and tracks cross-border (XB) orders within the Ninja Van network by detecting parcels that appear in multiple system IDs and have matching tracking and third-party tracking IDs
  granularity: Each row represents a unique order-tracking ID combination in the cross-border flow, with flags indicating whether it's an outbound record
  business_rules:
    - Orders must appear in at least 2 different system IDs
    - At least one record must have tracking_id matching third_party_tracking_id
    - Outbound flag is set to 1 when tracking_id equals third_party_tracking_id

input_tables:
  - data_warehouse.orders_enriched

fields:
  - name: order_id
    description: Unique identifier for the order
    source: data_warehouse.orders_enriched.order_id
    technical_description: "Direct field from source"

  - name: tracking_id
    description: Tracking number assigned to the order
    source: data_warehouse.orders_enriched.tracking_id
    technical_description: "Direct field from source"

  - name: third_party_tracking_id
    description: Tracking ID assigned by third-party delivery partners
    source: data_warehouse.orders_enriched.third_party_tracking_id
    technical_description: "Direct field from source"

  - name: outbound_flag
    description: Indicates if this record represents the outbound leg of the cross-border journey
    source: data_warehouse.orders_enriched.tracking_id, data_warehouse.orders_enriched.third_party_tracking_id
    technical_description: "CASE WHEN tracking_id = third_party_tracking_id THEN 1 ELSE 0 END"

  - name: system_id
    description: Identifier for the system where the record originated
    source: data_warehouse.orders_enriched.system_id
    technical_description: "Direct field from source"

  - name: created_month
    description: Month when the order was created
    source: data_warehouse.orders_enriched.creation_datetime
    technical_description: "DATE_FORMAT(creation_datetime, 'yyyy-MM')" 