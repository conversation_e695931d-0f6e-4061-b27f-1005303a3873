# Data dictionary

table_name: order_third_party_transfers

overview:
  purpose: Tracks when orders are transferred to third-party delivery partners, capturing the first transfer event for each order
  granularity: Each row represents a single order's first transfer to a third-party delivery partner
  business_rules:
    - Only includes orders that have been transferred to third-party delivery partners
    - Records only the first transfer event for each order
    - Links transfer events with order creation information

input_tables:
  - data_warehouse.third_party_transfer_events
  - core_prod_gl.orders

fields:
  - name: order_id
    description: Unique identifier for the order
    source: core_prod_gl.orders.id
    technical_description: "Direct field from source"

  - name: event_datetime
    description: Timestamp when the order was first transferred to a third-party delivery partner
    source: data_warehouse.third_party_transfer_events.event_datetime
    technical_description: "MIN(event_datetime) grouped by order_id from third_party_transfer_events"

  - name: created_month
    description: Month and year when the order was created
    source: core_prod_gl.orders.created_at
    technical_description: "DATE_FORMAT(orders.created_at, 'yyyy-MM')" 