# Data dictionary

table_name: force_success_events

overview:
  purpose: Tracks all force success events for orders, which represent manual overrides where an order's status was forcefully marked as successful
  granularity: Each row represents a single force success event for a specific order
  business_rules:
    - Only includes events where type = 3 (FORCED_SUCCESS)
    - Events are partitioned by system_id and created_month
    - Event timestamps are converted to local timezone based on system_id

input_tables:
  - events_prod_gl.order_events

fields:
  - name: order_event_id
    description: Unique identifier for the force success event
    source: events_prod_gl.order_events.id
    technical_description: "Direct field from source"

  - name: order_id
    description: Unique identifier of the order
    source: events_prod_gl.order_events.order_id
    technical_description: "Direct field from source"

  - name: system_id
    description: Identifier of the system where the force success event occurred
    source: events_prod_gl.order_events.system_id
    technical_description: "Direct field from source"

  - name: event_datetime
    description: Local timestamp when the force success event occurred
    source: events_prod_gl.order_events.time
    technical_description: "Converted from UTC to local timezone based on system_id using from_utc_timestamp(time, [system_timezone])"

  - name: created_month
    description: Month and year when the force success event was created, used for partitioning
    source: events_prod_gl.order_events.time
    technical_description: "Extracted from event time using date_format(time, 'yyyy-MM')" 