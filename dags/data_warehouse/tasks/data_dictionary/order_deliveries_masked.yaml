# Data dictionary

table_name: order_deliveries

overview:
  purpose: Tracks delivery attempts and outcomes for orders, including delivery and return-to-sender (RTS) attempts, success status, and address changes
  granularity: Each row represents a unique order with its delivery attempts and outcomes
  business_rules:
    - Only includes valid delivery transaction events (valid_flag = 1)
    - Tracks up to first 3 delivery attempts and RTS attempts
    - Captures the last successful delivery if any
    - Records address changes that occurred after the first delivery attempt

input_tables:
  - data_warehouse.delivery_transaction_events
  - data_warehouse.update_address_events
  - core_prod_gl.orders

fields:
  - name: order_id
    description: Unique identifier for the order
    source: core_prod_gl.orders.id
    technical_description: "Direct field from source"

  - name: delivery_attempts
    description: Total number of delivery attempts made for the order
    source: data_warehouse.delivery_transaction_events
    technical_description: "COUNT of events where type = 'delivery' and valid_flag = 1"

  - name: rts_attempts
    description: Total number of return-to-sender attempts made for the order
    source: data_warehouse.delivery_transaction_events
    technical_description: "COUNT of events where type = 'rts' and valid_flag = 1"

  - name: first_valid_delivery_attempt_datetime
    description: Timestamp of the first delivery attempt
    source: data_warehouse.delivery_transaction_events
    technical_description: "MIN(event_datetime) where type = 'delivery' and valid_flag = 1"

  - name: first_valid_delivery_attempt_timeslot_start
    description: Start time of the delivery window for first attempt
    source: data_warehouse.delivery_transaction_events
    technical_description: "timeslot_start from first delivery attempt record"

  - name: first_valid_delivery_attempt_timeslot_end
    description: End time of the delivery window for first attempt
    source: data_warehouse.delivery_transaction_events
    technical_description: "timeslot_end from first delivery attempt record"

  - name: first_valid_delivery_attempt_failure_reason_id
    description: Reason code if the first delivery attempt failed
    source: data_warehouse.delivery_transaction_events
    technical_description: "failure_reason_id from first delivery attempt record"

  - name: first_valid_delivery_attempt_status
    description: Status of the first delivery attempt
    source: data_warehouse.delivery_transaction_events
    technical_description: "status from first delivery attempt record"

  - name: first_valid_delivery_attempt_hub_id
    description: ID of the hub that attempted the first delivery
    source: data_warehouse.delivery_transaction_events
    technical_description: "route_hub_id from first delivery attempt record"

  - name: second_valid_delivery_attempt_datetime
    description: Timestamp of the second delivery attempt
    source: data_warehouse.delivery_transaction_events
    technical_description: "event_datetime from second delivery attempt record"

  - name: second_valid_delivery_attempt_timeslot_start
    description: Start time of the delivery window for second attempt
    source: data_warehouse.delivery_transaction_events
    technical_description: "timeslot_start from second delivery attempt record"

  - name: second_valid_delivery_attempt_timeslot_end
    description: End time of the delivery window for second attempt
    source: data_warehouse.delivery_transaction_events
    technical_description: "timeslot_end from second delivery attempt record"

  - name: second_valid_delivery_attempt_failure_reason_id
    description: Reason code if the second delivery attempt failed
    source: data_warehouse.delivery_transaction_events
    technical_description: "failure_reason_id from second delivery attempt record"

  - name: second_valid_delivery_attempt_status
    description: Status of the second delivery attempt
    source: data_warehouse.delivery_transaction_events
    technical_description: "status from second delivery attempt record"

  - name: second_valid_delivery_attempt_hub_id
    description: ID of the hub that attempted the second delivery
    source: data_warehouse.delivery_transaction_events
    technical_description: "route_hub_id from second delivery attempt record"

  - name: third_valid_delivery_attempt_datetime
    description: Timestamp of the third delivery attempt
    source: data_warehouse.delivery_transaction_events
    technical_description: "event_datetime from third delivery attempt record"

  - name: third_valid_delivery_attempt_timeslot_start
    description: Start time of the delivery window for third attempt
    source: data_warehouse.delivery_transaction_events
    technical_description: "timeslot_start from third delivery attempt record"

  - name: third_valid_delivery_attempt_timeslot_end
    description: End time of the delivery window for third attempt
    source: data_warehouse.delivery_transaction_events
    technical_description: "timeslot_end from third delivery attempt record"

  - name: third_valid_delivery_attempt_failure_reason_id
    description: Reason code if the third delivery attempt failed
    source: data_warehouse.delivery_transaction_events
    technical_description: "failure_reason_id from third delivery attempt record"

  - name: third_valid_delivery_attempt_status
    description: Status of the third delivery attempt
    source: data_warehouse.delivery_transaction_events
    technical_description: "status from third delivery attempt record"

  - name: third_valid_delivery_attempt_hub_id
    description: ID of the hub that attempted the third delivery
    source: data_warehouse.delivery_transaction_events
    technical_description: "route_hub_id from third delivery attempt record"

  - name: last_valid_delivery_attempt_datetime
    description: Timestamp of the most recent delivery attempt
    source: data_warehouse.delivery_transaction_events
    technical_description: "event_datetime from most recent delivery attempt record"

  - name: last_valid_delivery_attempt_timeslot_start
    description: Start time of the delivery window for last attempt
    source: data_warehouse.delivery_transaction_events
    technical_description: "timeslot_start from most recent delivery attempt record"

  - name: last_valid_delivery_attempt_timeslot_end
    description: End time of the delivery window for last attempt
    source: data_warehouse.delivery_transaction_events
    technical_description: "timeslot_end from most recent delivery attempt record"

  - name: last_valid_delivery_attempt_failure_reason_id
    description: Reason code if the last delivery attempt failed
    source: data_warehouse.delivery_transaction_events
    technical_description: "failure_reason_id from most recent delivery attempt record"

  - name: last_valid_delivery_attempt_status
    description: Status of the last delivery attempt
    source: data_warehouse.delivery_transaction_events
    technical_description: "status from most recent delivery attempt record"

  - name: last_valid_delivery_attempt_hub_id
    description: ID of the hub that attempted the last delivery
    source: data_warehouse.delivery_transaction_events
    technical_description: "route_hub_id from most recent delivery attempt record"

  - name: first_valid_rts_attempt_datetime
    description: Timestamp of the first return-to-sender attempt
    source: data_warehouse.delivery_transaction_events
    technical_description: "event_datetime from first RTS attempt record where type = 'rts'"

  - name: first_valid_rts_attempt_failure_reason_id
    description: Reason code if the first RTS attempt failed
    source: data_warehouse.delivery_transaction_events
    technical_description: "failure_reason_id from first RTS attempt record"

  - name: first_valid_rts_attempt_status
    description: Status of the first RTS attempt
    source: data_warehouse.delivery_transaction_events
    technical_description: "status from first RTS attempt record"

  - name: first_valid_rts_attempt_hub_id
    description: ID of the hub that attempted the first RTS
    source: data_warehouse.delivery_transaction_events
    technical_description: "route_hub_id from first RTS attempt record"

  - name: second_valid_rts_attempt_datetime
    description: Timestamp of the second return-to-sender attempt
    source: data_warehouse.delivery_transaction_events
    technical_description: "event_datetime from second RTS attempt record"

  - name: second_valid_rts_attempt_failure_reason_id
    description: Reason code if the second RTS attempt failed
    source: data_warehouse.delivery_transaction_events
    technical_description: "failure_reason_id from second RTS attempt record"

  - name: second_valid_rts_attempt_status
    description: Status of the second RTS attempt
    source: data_warehouse.delivery_transaction_events
    technical_description: "status from second RTS attempt record"

  - name: second_valid_rts_attempt_hub_id
    description: ID of the hub that attempted the second RTS
    source: data_warehouse.delivery_transaction_events
    technical_description: "route_hub_id from second RTS attempt record"

  - name: third_valid_rts_attempt_datetime
    description: Timestamp of the third return-to-sender attempt
    source: data_warehouse.delivery_transaction_events
    technical_description: "event_datetime from third RTS attempt record"

  - name: third_valid_rts_attempt_failure_reason_id
    description: Reason code if the third RTS attempt failed
    source: data_warehouse.delivery_transaction_events
    technical_description: "failure_reason_id from third RTS attempt record"

  - name: third_valid_rts_attempt_status
    description: Status of the third RTS attempt
    source: data_warehouse.delivery_transaction_events
    technical_description: "status from third RTS attempt record"

  - name: third_valid_rts_attempt_hub_id
    description: ID of the hub that attempted the third RTS
    source: data_warehouse.delivery_transaction_events
    technical_description: "route_hub_id from third RTS attempt record"

  - name: last_valid_rts_attempt_datetime
    description: Timestamp of the most recent return-to-sender attempt
    source: data_warehouse.delivery_transaction_events
    technical_description: "event_datetime from most recent RTS attempt record"

  - name: last_valid_rts_attempt_failure_reason_id
    description: Reason code if the last RTS attempt failed
    source: data_warehouse.delivery_transaction_events
    technical_description: "failure_reason_id from most recent RTS attempt record"

  - name: last_valid_rts_attempt_status
    description: Status of the last RTS attempt
    source: data_warehouse.delivery_transaction_events
    technical_description: "status from most recent RTS attempt record"

  - name: last_valid_rts_attempt_hub_id
    description: ID of the hub that attempted the last RTS
    source: data_warehouse.delivery_transaction_events
    technical_description: "route_hub_id from most recent RTS attempt record"

  - name: delivery_success_datetime
    description: Timestamp when the delivery was successfully completed
    source: data_warehouse.delivery_transaction_events
    technical_description: "event_datetime from the most recent record where status = 'Success'"

  - name: delivery_success_hub_id
    description: ID of the hub that successfully delivered the order
    source: data_warehouse.delivery_transaction_events
    technical_description: "route_hub_id from the successful delivery record"

  - name: delivery_success_driver_id
    description: ID of the driver who successfully delivered the order
    source: data_warehouse.delivery_transaction_events
    technical_description: "route_driver_id from the successful delivery record"

  - name: created_month
    description: Month when the order was created
    source: core_prod_gl.orders.created_at
    technical_description: "DATE_FORMAT(created_at, 'yyyy-MM')"

  - name: event_flag
    description: Indicates if there was an address update after first delivery attempt
    source: data_warehouse.update_address_events
    technical_description: "1 if there exists an update_address_event after first_valid_delivery_attempt_datetime, 0 otherwise"

  - name: previous_to_postcode_before_update_address_events
    description: Previous postcode before any address updates that occurred after first delivery attempt
    source: data_warehouse.update_address_events
    technical_description: "old_to_postcode from the first update_address_event that occurred after first_valid_delivery_attempt_datetime" 