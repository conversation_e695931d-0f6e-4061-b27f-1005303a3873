# Data dictionary

table_name: gamification_my_daily_report_with_free_parcels

overview:
  purpose: Provides daily performance and incentive metrics for hybrid, sameday staff drivers in Malaysia
    , including free parcels. This table is used for driver performance tracking, payroll calculations,
    and incentive management including balloon bonuses.
  granularity: Each row represents a driver's performance metrics for a single day.
  business_rules:
    - Merged waypoint deliveries are paid at 50% of the normal PPR rate
    - Special hybrid drivers (IDs 200046, 200487, 200501, 1013700, 1653789) have different PPR rates
    - Drivers with less than 14 days of employment have null hub information
    - For Hybrid Riders, parcels over 8kg (category 3) count double for balloon bonus calculations
    - Balloon bonuses are paid when cumulative monthly parcel thresholds are reached
    - This table will be unioned with another set of table that contains contractors with different business rules
    - Unused columns here will be hardcoded as null or zero

input_tables:
  - data_warehouse.gamification_my_base_data
  - data_warehouse.gamification_my_reservations_enriched
  - data_warehouse.gamification_my_planned_parcels_events
  - gsheets.my_lm_payroll_data_delivery_rates
  - gsheets.my_lm_payroll_data_reservation_rates
  - gsheets.my_lm_payroll_data_free_parcels
  - gsheets.my_lm_payroll_data_balloon_targets
  - gsheets.my_lm_payroll_data_logic
  - driver_prod_gl.drivers
  - sort_prod_gl.hubs

fields:
  - name: system_id
    description: Identifier for the country system
    source: data_warehouse.gamification_my_base_data
    technical_description: "Direct field from source, hardcoded as 'my' for Malaysia"

  - name: route_date
    description: Date of the delivery route
    source: data_warehouse.gamification_my_base_data.delivery_attempt_datetime
    technical_description: "Date extracted from delivery_attempt_datetime"

  - name: route_month
    description: Year and month of the delivery route
    source: data_warehouse.gamification_my_base_data.delivery_attempt_datetime
    technical_description: "Formatted as 'yyyy-MM' from route_date"

  - name: driver_id
    description: Unique identifier for the driver
    source: data_warehouse.gamification_my_base_data.driver_id
    technical_description: "Direct field from source"

  - name: driver_type
    description: Category of driver (HYBRID, SAMEDAY STAFF, etc.)
    source: data_warehouse.gamification_my_base_data.driver_type
    technical_description: "Direct field from source"

  - name: driver_display_name
    description: Name of the driver displayed in the system
    source: data_warehouse.gamification_my_base_data.driver_display_name
    technical_description: "Direct field from source"

  - name: hub_name
    description: Name of the hub the driver is associated with
    source: sort_prod_gl.hubs.name
    technical_description: "Only populated if driver has been employed for more than 14 days, otherwise null"

  - name: hub_id
    description: Identifier for the hub the driver is associated with
    source: driver_prod_gl.drivers.hub_id
    technical_description: "Only populated if driver has been employed for more than 14 days, otherwise null"

  - name: currency
    description: Currency code for payment calculations
    source: data_warehouse.gamification_my_base_data
    technical_description: "Hardcoded as 'MYR' for Malaysian Ringgit"

  - name: cycle_end_ppr_scheme
    description: PPR scheme applied at the end of the cycle
    source: data_warehouse.gamification_my_base_data
    technical_description: "Empty string for hybrid drivers as they don't use cycle-based PPR schemes"

  - name: created_month
    description: Month when the record was created
    source: data_warehouse.gamification_my_base_data.delivery_attempt_datetime
    technical_description: "Formatted as 'yyyy-MM' from delivery_attempt_datetime"

  - name: target_parcel_count
    description: Target number of parcels for the driver
    source: gsheets.my_lm_payroll_data_free_parcels.free_parcel_threshold
    technical_description: "Maximum free_parcel_threshold for the driver on that day"

  - name: planned_parcel_count
    description: Number of parcels planned for delivery
    source: data_warehouse.gamification_my_base_data
    technical_description: "Count of distinct order_ids for the driver on that day"

  - name: pending_parcel_count
    description: Number of parcels not yet delivered or failed
    source: data_warehouse.gamification_my_base_data
    technical_description: "Calculated as planned_parcel_count - delivered_parcel_count - failed_parcel_count"

  - name: delivered_parcel_count
    description: Number of parcels successfully delivered
    source: data_warehouse.gamification_my_base_data
    technical_description: "Count of distinct order_ids with transaction_status = 'Success'"

  - name: failed_parcel_count
    description: Number of parcels that failed delivery
    source: data_warehouse.gamification_my_base_data
    technical_description: "Count of distinct order_ids with transaction_status = 'Fail'"

  - name: daily_payable_lm_parcels
    description: Number of parcels eligible for payment on that day
    source: data_warehouse.gamification_my_base_data
    technical_description: "Count of parcels delivered with payment_rank > free_parcel_threshold"

  - name: delivery_daily_bonus
    description: Daily bonus amount for deliveries
    source: data_warehouse.gamification_my_base_data
    technical_description: "Sum of delivery_bonus_per_weight_tier across all weight categories"

  - name: delivery_daily_bonus_payroll
    description: Daily bonus amount for deliveries adjusted for payroll
    source: data_warehouse.gamification_my_base_data
    technical_description: "Same as delivery_daily_bonus for hybrid drivers"

  - name: waypoint_incentives
    description: Incentives earned for waypoints
    source: none
    technical_description: "Null for hybrid drivers"

  - name: daily_bonus
    description: Total daily bonus amount
    source: multiple
    technical_description: "Sum of delivery_bonus_per_weight_tier, merchant_pickup_bonus
    , customer_pickup_bonus_per_weight_tier, and rdo_pickup_bonus_per_weight_tier"

  - name: daily_bonus_payroll
    description: Total daily bonus amount adjusted for payroll
    source: multiple
    technical_description: "Same as daily_bonus for hybrid drivers"

  - name: category_1_name to category_13_name
    description: Name of each weight category
    source: gsheets.my_lm_payroll_data_delivery_rates
    technical_description: "Weight tier name for each category (1-13)"

  - name: category_1_parcels_delivered to category_13_parcels_delivered
    description: Number of parcels delivered in each weight category
    source: data_warehouse.gamification_my_base_data
    technical_description: "Count of parcels delivered for each category (1-13)"

  - name: category_1_daily_bonus to category_13_daily_bonus
    description: Daily bonus amount for each weight category
    source: data_warehouse.gamification_my_base_data
    technical_description: "Sum of delivery_bonus_per_weight_tier for each category (1-13)"

  - name: category_1_merged_parcels_delivered to category_13_merged_parcels_delivered
    description: Number of merged parcels delivered in each weight category
    source: data_warehouse.gamification_my_base_data
    technical_description: "Count of merged parcels delivered for each category (1-13)"

  - name: category_1_merged_deliveries_daily_bonus to category_13_merged_deliveries_daily_bonus
    description: Daily bonus amount for merged deliveries in each weight category
    source: data_warehouse.gamification_my_base_data
    technical_description: "Sum of merged_delivery_bonus_per_weight_tier for each category (1-13)"

  - name: category_1_non_merged_parcels_delivered to category_13_non_merged_parcels_delivered
    description: Number of non-merged parcels delivered in each weight category
    source: data_warehouse.gamification_my_base_data
    technical_description: "Count of non-merged parcels delivered for each category (1-13)"

  - name: category_1_non_merged_deliveries_daily_bonus to category_13_non_merged_deliveries_daily_bonus
    description: Daily bonus amount for non-merged deliveries in each weight category
    source: data_warehouse.gamification_my_base_data
    technical_description: "Sum of non_merged_delivery_bonus_per_weight_tier for each category (1-13)"

  - name: pickedup_customer_count
    description: Number of return parcels picked up from customers
    source: data_warehouse.gamification_my_base_data
    technical_description: "Count of return parcels picked up with transaction_type = 'PP'"

  - name: pickedup_customer_amount
    description: Bonus amount for customer pickups
    source: data_warehouse.gamification_my_base_data
    technical_description: "Sum of customer_pickup_bonus_per_weight_tier"

  - name: pickedup_customer_amount_payroll
    description: Bonus amount for customer pickups adjusted for payroll
    source: data_warehouse.gamification_my_base_data
    technical_description: "Same as pickedup_customer_amount for hybrid drivers"

  - name: pickedup_merchant_count
    description: Number of successful merchant reservations
    source: data_warehouse.gamification_my_reservations_enriched
    technical_description: "Count of successful reservations"

  - name: pickedup_merchant_amount
    description: Bonus amount for merchant pickups
    source: data_warehouse.gamification_my_reservations_enriched
    technical_description: "Sum of merchant_pickup_bonus based on reservation rates"

  - name: daily_pickup_bonus
    description: Total daily bonus for all pickups
    source: multiple
    technical_description: "Sum of merchant_pickup_bonus and customer_pickup_bonus_per_weight_tier"

  - name: success_rate
    description: Percentage of successful deliveries
    source: data_warehouse.gamification_my_base_data
    technical_description: "Calculated as delivered_parcel_count / planned_parcel_count, rounded to 4 decimal places"

  - name: daily_parcels_delivered_for_balloon_bonus
    description: Number of parcels eligible for balloon bonus on that day
    source: data_warehouse.gamification_my_base_data
    technical_description: "For Hybrid Riders, parcels with category 3 (>8kg) count double, otherwise count once"

  - name: picked_up_rdo_count
    description: Number of RDO (Return Delivery Order) pickups
    source: data_warehouse.gamification_my_base_data
    technical_description: "Count of pickups with tracking_id like '%-DO'"

  - name: picked_up_rdo_amount
    description: Bonus amount for RDO pickups
    source: data_warehouse.gamification_my_base_data
    technical_description: "Sum of rdo_pickup_bonus_per_weight_tier"

  - name: cumulative_parcels_delivered_for_balloon_bonus
    description: Cumulative count of parcels for balloon bonus within the month
    source: data_warehouse.gamification_my_base_data
    technical_description: "Running sum of daily_parcels_delivered_for_balloon_bonus within the month"

  - name: balloon_bonus_amount
    description: Balloon bonus amount earned on that day
    source: gsheets.my_lm_payroll_data_balloon_targets
    technical_description: "Bonus amount when cumulative_parcels_delivered_for_balloon_bonus falls within a target range"

  - name: tier_1_balloon_targets_start to tier_4_balloon_targets_start
    description: Starting targets for each balloon bonus tier
    source: gsheets.my_lm_payroll_data_balloon_targets
    technical_description: "Minimum parcel count required to qualify for each bonus tier"

  - name: tier_1_balloon_targets_end to tier_4_balloon_targets_end
    description: Ending targets for each balloon bonus tier
    source: gsheets.my_lm_payroll_data_balloon_targets
    technical_description: "Maximum parcel count for each bonus tier"

  - name: tier_1_balloon_bonus_amount to tier_4_balloon_bonus_amount
    description: Bonus amount for each balloon bonus tier
    source: gsheets.my_lm_payroll_data_balloon_targets
    technical_description: "Bonus amount paid when reaching each tier threshold"

  - name: daily_balloon_bonus_amount
    description: Daily balloon bonus amount
    source: gsheets.my_lm_payroll_data_balloon_targets
    technical_description: "Balloon bonus amount only on the day when a new tier is reached, otherwise 0" 