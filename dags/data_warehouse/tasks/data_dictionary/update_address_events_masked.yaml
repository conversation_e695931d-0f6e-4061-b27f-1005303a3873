# Data dictionary

table_name: update_address_events

overview:
  purpose: Tracks changes made to order delivery addresses, capturing both old and new values for each address component
  granularity: Each row represents a single address update event for an order
  business_rules:
    - Only includes events where the order event type is 11 (UPDATE_ADDRESS)
    - Timestamps are converted to local timezone based on system_id
    - Data is partitioned by system_id and created_month

input_tables:
  - hub_prod_gl.order_events

fields:
  - name: order_event_id
    description: Unique identifier for the address update event
    source: hub_prod_gl.order_events.id
    technical_description: "Direct field from source"

  - name: order_id
    description: Unique identifier of the order that had its address updated
    source: hub_prod_gl.order_events.order_id
    technical_description: "Direct field from source"

  - name: event_datetime
    description: Timestamp when the address update occurred
    source: hub_prod_gl.order_events.time
    technical_description: "Converted from UTC to local timezone using system_id"

  - name: event_creation_datetime
    description: Timestamp when the address update event was created in the system
    source: hub_prod_gl.order_events.created_at
    technical_description: "Converted from UTC to local timezone using system_id"

  - name: old_to_address1
    description: Previous value of address line 1
    source: hub_prod_gl.order_events.data
    technical_description: "Extracted from JSON data field: $.to_address_1.old_value"

  - name: new_to_address1
    description: New value of address line 1
    source: hub_prod_gl.order_events.data
    technical_description: "Extracted from JSON data field: $.to_address_1.new_value"

  - name: old_to_address2
    description: Previous value of address line 2
    source: hub_prod_gl.order_events.data
    technical_description: "Extracted from JSON data field: $.to_address_2.old_value"

  - name: new_to_address2
    description: New value of address line 2
    source: hub_prod_gl.order_events.data
    technical_description: "Extracted from JSON data field: $.to_address_2.new_value"

  - name: old_to_postcode
    description: Previous postal code
    source: hub_prod_gl.order_events.data
    technical_description: "Extracted from JSON data field: $.to_postcode.old_value"

  - name: new_to_postcode
    description: New postal code
    source: hub_prod_gl.order_events.data
    technical_description: "Extracted from JSON data field: $.to_postcode.new_value"

  - name: old_to_city
    description: Previous city name
    source: hub_prod_gl.order_events.data
    technical_description: "Extracted from JSON data field: $.to_city.old_value"

  - name: new_to_city
    description: New city name
    source: hub_prod_gl.order_events.data
    technical_description: "Extracted from JSON data field: $.to_city.new_value"

  - name: old_to_district
    description: Previous district name
    source: hub_prod_gl.order_events.data
    technical_description: "Extracted from JSON data field: $.to_district.old_value"

  - name: new_to_district
    description: New district name
    source: hub_prod_gl.order_events.data
    technical_description: "Extracted from JSON data field: $.to_district.new_value"

  - name: old_to_state
    description: Previous state name
    source: hub_prod_gl.order_events.data
    technical_description: "Extracted from JSON data field: $.to_state.old_value"

  - name: new_to_state
    description: New state name
    source: hub_prod_gl.order_events.data
    technical_description: "Extracted from JSON data field: $.to_state.new_value"

  - name: created_month
    description: Month when the address update occurred
    source: hub_prod_gl.order_events.time
    technical_description: "Formatted as YYYY-MM from the event time"

  - name: system_id
    description: Identifier of the system where the event originated
    source: hub_prod_gl.order_events.system_id
    technical_description: "Direct field from source" 