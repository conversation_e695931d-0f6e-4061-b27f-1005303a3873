# Data dictionary

table_name: update_cash_events_enriched

overview:
  purpose: Tracks changes in Cash on Delivery (COD) values for orders, providing insights into COD value updates and their frequency
  granularity: Each row represents the summary of COD value changes for a unique order within a system
  business_rules:
    - Only includes records where old COD value differs from new COD value
    - Aggregates multiple COD updates for the same order
    - Groups data by order_id and system_id

input_tables:
  - data_warehouse.update_cash_events

fields:
  - name: order_id
    description: Unique identifier for the order
    source: data_warehouse.update_cash_events.order_id
    technical_description: "Direct field from source"

  - name: system_id
    description: Identifier for the operating system/country (ID, MY, PH, SG, TH, VN)
    source: data_warehouse.update_cash_events.system_id
    technical_description: "Direct field from source"

  - name: min_old_cod_value
    description: Minimum value of the original COD amount before any updates
    source: data_warehouse.update_cash_events.old_cod_value
    technical_description: "MIN(old_cod_value) grouped by order_id and system_id"

  - name: max_new_cod_value
    description: Maximum value of the updated COD amount after changes
    source: data_warehouse.update_cash_events.new_cod_value
    technical_description: "MAX(new_cod_value) grouped by order_id and system_id"

  - name: cod_update_frequency
    description: Number of times the COD value was updated for this order
    source: data_warehouse.update_cash_events
    technical_description: "COUNT(*) of records grouped by order_id and system_id where old_cod_value != new_cod_value"

  - name: created_month
    description: Month when the COD update event was created
    source: data_warehouse.update_cash_events.created_month
    technical_description: "MIN(created_month) grouped by order_id and system_id" 