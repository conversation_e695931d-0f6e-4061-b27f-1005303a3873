# Data dictionary

table_name: third_party_transfer_events

overview:
  purpose: Tracks events when orders are transferred to third-party delivery partners
  granularity: Each row represents a single transfer event for an order
  business_rules:
    - Only includes events of type 41 (TRANSFERRED_TO_THIRD_PARTY)
    - Events are partitioned by system_id and created_month
    
input_tables:
  - events_prod_gl.order_events

fields:
  - name: order_event_id
    description: Unique identifier for the transfer event
    source: events_prod_gl.order_events.id
    technical_description: "Direct field from source, cast as bigint"

  - name: order_id
    description: Unique identifier of the order being transferred
    source: events_prod_gl.order_events.order_id
    technical_description: "Direct field from source, cast as bigint"

  - name: system_id
    description: Identifier of the system where the event originated
    source: events_prod_gl.order_events.system_id
    technical_description: "Direct field from source"

  - name: event_datetime
    description: Timestamp when the order was transferred to third party
    source: events_prod_gl.order_events.time
    technical_description: "Converted from UTC to local timezone based on system_id"

  - name: created_month
    description: Month when the transfer event occurred
    source: events_prod_gl.order_events.time
    technical_description: "Derived from time field using format yyyy-MM" 