# Data dictionary

table_name: update_cash_events

overview:
  purpose: Captures individual Cash on Delivery (COD) value update events for orders, tracking both old and new values
  granularity: Each row represents a single COD value update event for an order
  business_rules:
    - Only includes events of type 15 (UPDATE_CASH)
    - Records both old and new COD values for each update
    - Timestamps are converted to local timezone based on system_id

input_tables:
  - events_prod_gl.order_events

fields:
  - name: id
    description: Unique identifier for the COD update event
    source: events_prod_gl.order_events.id
    technical_description: "Direct field from source"

  - name: order_id
    description: Unique identifier for the order
    source: events_prod_gl.order_events.order_id
    technical_description: "Direct field from source"

  - name: type
    description: Event type identifier for cash updates
    source: events_prod_gl.order_events.type
    technical_description: "Constant value '15' representing UPDATE_CASH event type"

  - name: created_at
    description: Timestamp when the COD update occurred
    source: events_prod_gl.order_events.created_at
    technical_description: "Converted from UTC to local timezone based on system_id using from_utc_timestamp(created_at, [local_timezone])"

  - name: old_cod_value
    description: Original COD value before the update
    source: events_prod_gl.order_events.data
    technical_description: "Extracted from JSON data field using get_json_object(data,'$.cash_on_delivery.old_value') and cast as double"

  - name: new_cod_value
    description: Updated COD value after the change
    source: events_prod_gl.order_events.data
    technical_description: "Extracted from JSON data field using get_json_object(data,'$.cash_on_delivery.new_value') and cast as double"

  - name: system_id
    description: Identifier for the operating system/country (ID, MY, PH, SG, TH, VN)
    source: events_prod_gl.order_events.system_id
    technical_description: "Direct field from source"

  - name: created_month
    description: Month when the COD update event was created
    source: events_prod_gl.order_events.created_at
    technical_description: "Derived using date_format(created_at, 'yyyy-MM')" 