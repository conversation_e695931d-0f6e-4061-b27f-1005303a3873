# Data dictionary

table_name: hubs_enriched

overview:
  purpose: Provides enriched information about hubs including their geographical location, type, relationships, and operational characteristics. This table combines core hub data with additional attributes from various sources to create a comprehensive view of each hub.
  granularity: Each row represents a unique hub in a specific country
  business_rules:
    - Parent-child relationships are established through dummy hub mappings
    - Special SLA region logic is applied for Vietnam's central region provinces
    - Includes both active and deleted hubs with a flag to distinguish them
    - Contains manually updated attributes from GDrive for operational characteristics

input_tables:
  - sort_prod_gl.dummy_hub_map
  - sort_prod_gl.hubs
  - sort_prod_gl.regions
  - gdrive.hubs_enriched

fields:
  - name: id
    description: Unique identifier for the hub
    source: sort_prod_gl.hubs.hub_id
    technical_description: "Direct field from source, cast as bigint"

  - name: country
    description: Country code where the hub is located
    source: sort_prod_gl.hubs.system_id
    technical_description: "Direct field from source"

  - name: name
    description: Full name of the hub
    source: sort_prod_gl.hubs.name
    technical_description: "Direct field from source"

  - name: short_name
    description: Abbreviated or short name of the hub
    source: sort_prod_gl.hubs.short_name
    technical_description: "Direct field from source"

  - name: address_country
    description: Country of the hub's physical address
    source: sort_prod_gl.hubs.country
    technical_description: "Direct field from source"

  - name: address_city
    description: City where the hub is located
    source: sort_prod_gl.hubs.city
    technical_description: "Direct field from source"

  - name: latitude
    description: Geographical latitude coordinate of the hub
    source: sort_prod_gl.hubs.latitude
    technical_description: "Direct field from source"

  - name: longitude
    description: Geographical longitude coordinate of the hub
    source: sort_prod_gl.hubs.longitude
    technical_description: "Direct field from source"

  - name: facility_type
    description: Type of facility the hub represents
    source: sort_prod_gl.hubs.facility_type
    technical_description: "Direct field from source"

  - name: region
    description: Regional classification of the hub
    source: sort_prod_gl.regions.name
    technical_description: "Direct field from regions table joined on region_id"

  - name: area
    description: Specific area designation for the hub
    source: sort_prod_gl.hubs.area
    technical_description: "Direct field from source"

  - name: sort_hub_flag
    description: Indicates if the hub is a sorting facility
    source: sort_prod_gl.hubs.sort_hub
    technical_description: "Derived from source, coalesced with 0 if null and cast as bigint"

  - name: virtual_hub_flag
    description: Indicates if the hub is a virtual hub
    source: sort_prod_gl.dummy_hub_map
    technical_description: "Derived as 1 if hub exists in dummy_hub_map, 0 otherwise"

  - name: parent_hub_id
    description: ID of the parent hub if this is a virtual hub
    source: "sort_prod_gl.hubs.hub_id (via dummy_hub_map join)"
    technical_description: "Obtained by joining through dummy_hub_map to get the real hub ID, cast as bigint"

  - name: parent_hub_name
    description: Name of the parent hub if this is a virtual hub
    source: "sort_prod_gl.hubs.name (via dummy_hub_map join)"
    technical_description: "Obtained by joining through dummy_hub_map to get the real hub name"

  - name: is_delivery_hub
    description: Indicates if the hub is used for deliveries
    source: gdrive.hubs_enriched.is_delivery_hub
    technical_description: "Manual update from GDrive source, cast as bigint"

  - name: is_sea_haul_hub
    description: Indicates if the hub handles sea transportation
    source: gdrive.hubs_enriched.is_sea_haul_hub
    technical_description: "Manual update from GDrive source, cast as bigint"

  - name: consol_hub
    description: Indicates if the hub is a consolidation hub
    source: gdrive.hubs_enriched.consol_hub
    technical_description: "Manual update from GDrive source, cast as bigint"

  - name: sla_region
    description: Region classification for SLA purposes
    source: "sort_prod_gl.regions.name, sort_prod_gl.hubs.system_id, sort_prod_gl.hubs.short_name"
    technical_description: "Derived field - returns 'Central' for VN hubs in specific central provinces, otherwise uses region name"

  - name: creation_datetime
    description: Timestamp when the hub was created
    source: sort_prod_gl.hubs.created_at
    technical_description: "Converted from UTC to local timezone based on system_id"

  - name: deletion_datetime
    description: Timestamp when the hub was deleted, if applicable
    source: sort_prod_gl.hubs.deleted_at
    technical_description: "Converted from UTC to local timezone based on system_id"

  - name: is_deleted
    description: Indicates if the hub has been deleted
    source: sort_prod_gl.hubs.deleted_at
    technical_description: "Derived as 1 if deleted_at is not null, 0 otherwise"

  - name: system_id
    description: System identifier representing the country
    source: sort_prod_gl.hubs.system_id
    technical_description: "Direct field from source"

  - name: created_month
    description: Month and year when the hub was created
    source: sort_prod_gl.hubs.created_at
    technical_description: "Derived from created_at formatted as yyyy-MM" 