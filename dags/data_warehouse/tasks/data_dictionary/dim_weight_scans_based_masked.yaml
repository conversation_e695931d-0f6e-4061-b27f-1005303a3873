# Data dictionary

table_name: dim_weight_scans_base

overview:
  purpose: Stores weight scan measurements and dimensional data for parcels at hub inbound locations, including both raw measurements and processed values.
  granularity: Each row represents a unique weight scan event for an order at a hub, identified by a scan_uuid.
  business_rules:
    - Only includes weight scans from December 2020 onwards (when DWS system was implemented)
    - Excludes scans from system_id 'mm'
    - For duplicate scan_uuids, only the latest scan record is kept
    - Volume weight calculations vary by system_id and shipper:
      - ID: divided by 6000
      - MY: divided by 6000 for shippers 45012, 42206; divided by 5000 for others
      - PH: divided by 3500
      - SG/TH/VN: divided by 6000

input_tables:
  - events_prod_gl.order_events
  - aaa_prod_gl.user_info
  - data_warehouse.lnk_orders_shippers

fields:
  - name: order_event_id
    description: Unique identifier for the weight scan event
    source: events_prod_gl.order_events.id
    technical_description: "Direct field from source, cast as bigint"

  - name: scan_uuid
    description: Unique identifier for the weight scan
    source: events_prod_gl.order_events.data.scan_uuid
    technical_description: "Extracted from JSON data field using get_json_object"

  - name: order_id
    description: Unique identifier for the order
    source: events_prod_gl.order_events.order_id
    technical_description: "Direct field from source"

  - name: shipper_id
    description: Identifier of the shipper
    source: data_warehouse.lnk_orders_shippers.shipper_id
    technical_description: "Joined from lnk_orders_shippers on order_id and system_id"

  - name: user_id
    description: Identifier of the user who performed the weight scan
    source: events_prod_gl.order_events.user_id
    technical_description: "Direct field from source"

  - name: user_name
    description: Full name of the user who performed the scan
    source: aaa_prod_gl.user_info.first_name, aaa_prod_gl.user_info.last_name, aaa_prod_gl.user_info.display_name
    technical_description: "COALESCE(TRIM(CONCAT_WS(' ', first_name, last_name)), display_name)"

  - name: user_grant_type
    description: Authorization type of the user who performed the scan
    source: events_prod_gl.order_events.user_grant_type
    technical_description: "Direct field from source"

  - name: device
    description: Identifier of the device used for scanning
    source: events_prod_gl.order_events.data.device_id
    technical_description: "Extracted from JSON data field using get_json_object"

  - name: device_type
    description: Type of device used for scanning
    source: events_prod_gl.order_events.data.device_type
    technical_description: "Extracted from JSON data field using get_json_object"

  - name: scan_hub_id
    description: Identifier of the hub where the scan was performed
    source: events_prod_gl.order_events.data.hub_id
    technical_description: "Extracted from JSON data field using get_json_object, cast as integer"

  - name: raw_weight
    description: Raw weight measurement from the scanning device
    source: events_prod_gl.order_events.data.raw_weight
    technical_description: "Extracted from JSON data field using get_json_object, cast as double"

  - name: raw_length
    description: Raw length measurement from the scanning device
    source: events_prod_gl.order_events.data.raw_length
    technical_description: "Extracted from JSON data field using get_json_object, cast as double"

  - name: raw_width
    description: Raw width measurement from the scanning device
    source: events_prod_gl.order_events.data.raw_width
    technical_description: "Extracted from JSON data field using get_json_object, cast as double"

  - name: raw_height
    description: Raw height measurement from the scanning device
    source: events_prod_gl.order_events.data.raw_height
    technical_description: "Extracted from JSON data field using get_json_object, cast as double"

  - name: old_weight
    description: Previous weight value before the scan
    source: events_prod_gl.order_events.data.weight.old_value
    technical_description: "Extracted from JSON data field using get_json_object, cast as double"

  - name: old_length
    description: Previous length value before the scan
    source: events_prod_gl.order_events.data.length.old_value
    technical_description: "Extracted from JSON data field using get_json_object, cast as double"

  - name: old_width
    description: Previous width value before the scan
    source: events_prod_gl.order_events.data.width.old_value
    technical_description: "Extracted from JSON data field using get_json_object, cast as double"

  - name: old_height
    description: Previous height value before the scan
    source: events_prod_gl.order_events.data.height.old_value
    technical_description: "Extracted from JSON data field using get_json_object, cast as double"

  - name: old_parcel_size
    description: Previous parcel size category before the scan
    source: events_prod_gl.order_events.data.parcel_size_id.old_value
    technical_description: "Mapped from size_id to size category (xs, s, m, l, xl, xxl)"

  - name: old_vol_weight
    description: Previous volumetric weight calculated from old dimensions
    source: events_prod_gl.order_events.data
    technical_description: "Calculated as (old_width * old_length * old_height) divided by system-specific divisor, rounded to 2 decimal places"

  - name: new_weight
    description: New weight value after the scan
    source: events_prod_gl.order_events.data.weight.new_value
    technical_description: "Extracted from JSON data field using get_json_object, cast as double"

  - name: new_length
    description: New length value after the scan
    source: events_prod_gl.order_events.data.length.new_value
    technical_description: "Extracted from JSON data field using get_json_object, cast as double"

  - name: new_width
    description: New width value after the scan
    source: events_prod_gl.order_events.data.width.new_value
    technical_description: "Extracted from JSON data field using get_json_object, cast as double"

  - name: new_height
    description: New height value after the scan
    source: events_prod_gl.order_events.data.height.new_value
    technical_description: "Extracted from JSON data field using get_json_object, cast as double"

  - name: new_parcel_size
    description: New parcel size category after the scan
    source: events_prod_gl.order_events.data.parcel_size_id.new_value
    technical_description: "Mapped from size_id to size category (xs, s, m, l, xl, xxl)"

  - name: new_vol_weight
    description: New volumetric weight calculated from new dimensions
    source: events_prod_gl.order_events.data
    technical_description: "Calculated as (new_width * new_length * new_height) divided by system-specific divisor, rounded to 2 decimal places"

  - name: scan_datetime
    description: Local timestamp when the scan was performed
    source: events_prod_gl.order_events.time
    technical_description: "Converted from UTC to local timezone based on system_id"

  - name: system_id
    description: Identifier of the operating system/country
    source: events_prod_gl.order_events.system_id
    technical_description: "Direct field from source"

  - name: created_month
    description: Month and year when the scan was created
    source: events_prod_gl.order_events.time
    technical_description: "Formatted as 'yyyy-MM' from the time field" 