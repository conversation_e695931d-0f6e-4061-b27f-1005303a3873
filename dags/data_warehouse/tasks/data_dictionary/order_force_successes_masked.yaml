# Data dictionary

table_name: order_force_successes

overview:
  purpose: Tracks the latest force success events for orders, providing information about when orders were marked as force succeeded.
  granularity: Each row represents the most recent force success event for a unique order.
  business_rules:
    - Only the most recent force success event is kept for each order (using ROW_NUMBER and rank = 1)
    - Orders must exist in the core orders table
    - Data is partitioned by order creation month

input_tables:
  - data_warehouse.force_success_events
  - core_prod_gl.orders

fields:
  - name: order_id
    description: Unique identifier of the order
    source: data_warehouse.force_success_events.order_id
    technical_description: "Direct field from source"

  - name: event_datetime
    description: Timestamp when the force success event occurred
    source: data_warehouse.force_success_events.event_datetime
    technical_description: "Latest event_datetime for each order using ROW_NUMBER() OVER (PARTITION BY order_id ORDER BY event_datetime DESC)"

  - name: created_month
    description: Month and year when the order was created
    source: core_prod_gl.orders.created_at
    technical_description: "Derived from orders.created_at using date_format(created_at, 'yyyy-MM')" 