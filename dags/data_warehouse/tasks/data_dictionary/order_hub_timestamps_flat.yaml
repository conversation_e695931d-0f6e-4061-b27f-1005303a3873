# Data dictionary

table_name: order_hub_timestamps_flat

overview:
  purpose: Flattens and combines hub timestamp data for orders throughout their journey in the network, tracking when orders pass through each hub and various processing milestones
  granularity: Each row represents an order at a specific hub with its associated timestamps and the parcel flow
  business_rules:
    - Orders are tracked separately for forward and return-to-shipper flows
    - Only includes hubs where the order was actually processed
    - Collects timestamps from various systems (POH, shipments, trips) into a single view
    - Sequences hubs by department sequence for tracking order path

input_tables:
  - data_warehouse.middle_mile_trip_relationships
  - data_warehouse.movement_trips_enriched
  - data_warehouse.order_department_movements
  - data_warehouse.order_rts_triggers
  - data_warehouse.poh_order_metrics
  - data_warehouse.poh_metrics
  - data_warehouse.shipments_enriched
  - data_warehouse.orders_enriched

fields:
  - name: order_id
    description: Unique identifier for the order
    source: data_warehouse.order_department_movements.order_id
    technical_description: "Direct field from source"

  - name: parcel_flow
    description: Indicates whether the movement is a forward delivery or return-to-sender leg
    source: data_warehouse.order_department_movements, data_warehouse.order_rts_triggers
    technical_description: "If entry_datetime >= rts.event_datetime then 'rts_leg', else 'forward_leg'"

  - name: min_seq
    description: The minimum department sequence number for this order at this hub
    source: data_warehouse.order_department_movements.department_sequence
    technical_description: "Minimum department_sequence value for the order at this hub for a flow"

  - name: hub_id
    description: Identifier for the hub where the order was processed
    source: data_warehouse.order_department_movements.hub_id
    technical_description: "Adjusted hub_id where hub_id 1179 (JKT-FULFILLMENT) is mapped to 439 (MAC-MAC)"

  - name: first_poh_datetime
    description: Timestamp of the first proof of handover at the hub
    source: data_warehouse.poh_metrics.handover_time
    technical_description: "Minimum handover_time from poh_metrics where the order's hub_handover_id matches"

  - name: trip_arrival_datetime
    description: Actual arrival time of the trip at the destination hub
    source: data_warehouse.movement_trips_enriched.actual_arrival_datetime
    technical_description: "Minimum actual_arrival_datetime from trips where the shipment_id and dest_hub_id match"

  - name: ih_datetime
    description: Shipment Hub Inbound timestamp
    source: data_warehouse.order_department_movements.entry_datetime
    technical_description: "Minimum entry_datetime where department is 'middle_mile_staging_dest' or 'middle_mile_staging_transit'"

  - name: ih_shipment_id
    description: Shipment ID associated with the Shipment Hub Inbound timestamp
    source: data_warehouse.order_department_movements.location_id
    technical_description: "Minimum location_id by entry_datetime where department is 'middle_mile_staging_dest' or 'middle_mile_staging_transit'"

  - name: ib_datetime
    description: Inbound timestamp at the hub
    source: data_warehouse.order_department_movements.entry_datetime
    technical_description: "Minimum entry_datetime where department in ('last_mile', 'sort') or (department = 'first_mile' and location_type = 'HUB')"

  - name: last_ats_datetime
    description: Last Add to Shipment timestamp at the hub
    source: data_warehouse.order_department_movements.entry_datetime
    technical_description: "Maximum entry_datetime where department is 'middle_mile_staging_origin'"

  - name: last_ats_shipment_id
    description: Shipment ID associated with the last Add to Shipment status
    source: data_warehouse.order_department_movements.location_id
    technical_description: "Maximum location_id by entry_datetime where department is 'middle_mile_staging_origin'"

  - name: cs_datetime
    description: Shipment Closed timestamp
    source: data_warehouse.shipments_enriched.orig_shipment_close_datetime
    technical_description: "orig_shipment_close_datetime from shipments_enriched where shipment_id matches last_ats_shipment_id"

  - name: iv_datetime
    description: Shipment Van Inbounded timestamp
    source: data_warehouse.order_department_movements.entry_datetime
    technical_description: "Minimum entry_datetime where department is 'middle_mile'"

  - name: created_month
    description: Month when the order was created
    source: data_warehouse.orders_enriched.created_month
    technical_description: "Direct field from orders_enriched" 