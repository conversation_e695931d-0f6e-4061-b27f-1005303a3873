# Data dictionary

table_name: reserve_tracking_ids_enriched

overview:
  purpose: This table provides order creation request payload details
  granularity: Each row represents a unique tracking ID and system ID combination with its latest parcel job details.
  business_rules:
    - Only includes non-deleted tracking IDs
    - Takes the most recent record for each tracking ID and system ID combination
    - Null tracking IDs are excluded
    - Timestamps are converted to local timezone based on system ID

input_tables:
  - o_create_prod_gl.reserve_tracking_ids

fields:
  - name: tracking_id
    description: Unique identifier for the tracking record
    source: o_create_prod_gl.reserve_tracking_ids.tracking_id
    technical_description: "Direct field from source"

  - name: dimensions_flag
    description: Indicates whether dimension information is present for the parcel
    source: o_create_prod_gl.reserve_tracking_ids.request_payload
    technical_description: "Derived as 1 if parcel_job.dimensions exists in request_payload, 0 otherwise"

  - name: size
    description: Size of the parcel
    source: o_create_prod_gl.reserve_tracking_ids.request_payload
    technical_description: "Extracted from request_payload.parcel_job.dimensions.size"

  - name: weight
    description: Weight of the parcel
    source: o_create_prod_gl.reserve_tracking_ids.request_payload
    technical_description: "Extracted from request_payload.parcel_job.dimensions.weight"

  - name: length
    description: Length of the parcel
    source: o_create_prod_gl.reserve_tracking_ids.request_payload
    technical_description: "Extracted from request_payload.parcel_job.dimensions.length"

  - name: width
    description: Width of the parcel
    source: o_create_prod_gl.reserve_tracking_ids.request_payload
    technical_description: "Extracted from request_payload.parcel_job.dimensions.width"

  - name: height
    description: Height of the parcel
    source: o_create_prod_gl.reserve_tracking_ids.request_payload
    technical_description: "Extracted from request_payload.parcel_job.dimensions.height"

  - name: is_pickup_required
    description: Flag indicating if pickup is required for the parcel
    source: o_create_prod_gl.reserve_tracking_ids.request_payload
    technical_description: "Derived as 1 if parcel_job.is_pickup_required is 'true', 0 otherwise"

  - name: scheduled_pickup_date
    description: Scheduled date for parcel pickup
    source: o_create_prod_gl.reserve_tracking_ids.request_payload
    technical_description: "Extracted and converted to date from request_payload.parcel_job.pickup_date"

  - name: delivery_start_date
    description: Start date for parcel delivery
    source: o_create_prod_gl.reserve_tracking_ids.request_payload
    technical_description: "Extracted and converted to date from request_payload.parcel_job.delivery_start_date"

  - name: delivery_start_time
    description: Start time of delivery window
    source: o_create_prod_gl.reserve_tracking_ids.request_payload
    technical_description: "Extracted from request_payload.parcel_job.delivery_timeslot.start_time"

  - name: delivery_end_time
    description: End time of delivery window
    source: o_create_prod_gl.reserve_tracking_ids.request_payload
    technical_description: "Extracted from request_payload.parcel_job.delivery_timeslot.end_time"

  - name: allow_self_collection
    description: Flag indicating if self-collection is allowed
    source: o_create_prod_gl.reserve_tracking_ids.request_payload
    technical_description: "Derived as 1 if parcel_job.allow_self_collection is 'true', 0 otherwise"

  - name: system_id
    description: Identifier for the system where the record originated
    source: o_create_prod_gl.reserve_tracking_ids.system_id
    technical_description: "Converted to lowercase from source system_id"

  - name: created_month
    description: Month and year when the record was created
    source: o_create_prod_gl.reserve_tracking_ids.created_at
    technical_description: "Derived from created_at timestamp, formatted as YYYY-MM in local timezone based on system_id" 