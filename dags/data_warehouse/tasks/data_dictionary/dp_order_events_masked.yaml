# Data dictionary

table_name: dp_order_events

overview:
  purpose: Tracks order events related to delivery partner (DP) interactions, including handovers between DPs, customers, shippers, and drivers
  granularity: Each row represents a single DP-related event for an order
  business_rules:
    - Only includes events of type 19 (FROM_DP_TO_CUSTOMER), 20 (FROM_SHIPPER_TO_DP), 21 (FROM_DP_TO_DRIVER), and 22 (FROM_DRIVER_TO_DP)
    - Events are partitioned by system_id and created_month for efficient querying

input_tables:
  - events_prod_gl.order_events

fields:
  - name: order_event_id
    description: Unique identifier for the order event
    source: events_prod_gl.order_events.id
    technical_description: "Direct field from source, cast as bigint"

  - name: order_id
    description: Unique identifier for the order
    source: events_prod_gl.order_events.order_id
    technical_description: "Direct field from source, cast as bigint"

  - name: system_id
    description: Identifier for the system where the event originated
    source: events_prod_gl.order_events.system_id
    technical_description: "Direct field from source"

  - name: type_id
    description: Identifier for the type of DP event (19=DP to Customer, 20=Shipper to DP, 21=DP to Driver, 22=Driver to DP)
    source: events_prod_gl.order_events.type
    technical_description: "Direct field from source, filtered to only include types 19, 20, 21, and 22"

  - name: dpms_id
    description: Identifier for the delivery partner management system
    source: events_prod_gl.order_events.data
    technical_description: "Extracted from JSON data field using get_json_object(data, '$.dp_id'), cast as bigint"

  - name: event_datetime
    description: Timestamp when the event occurred in local timezone
    source: events_prod_gl.order_events.time
    technical_description: "Converted from UTC to local timezone based on system_id using from_utc_timestamp function"

  - name: created_month
    description: Month and year when the event was created (used for partitioning)
    source: events_prod_gl.order_events.time
    technical_description: "Derived from time field using date_format(time, 'yyyy-MM')" 