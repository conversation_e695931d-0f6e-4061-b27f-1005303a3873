# Data dictionary

table_name: orders_enriched

overview:
  purpose: A comprehensive enriched orders table that combines order details with related information from multiple sources including marketplace data, pricing, delivery types, and third-party vendor information. It provides a complete view of each order with enriched status, pricing details, geographical information, and delivery specifications.
  granularity: Each row represents a unique order with its associated details, pricing, and delivery information.
  business_rules:
    - Deleted orders are excluded (deleted_at not NULL)
    - Status is enriched into categories - completed, pending, returned_to_sender, cancelled, on_hold, transit, exception
    - All monetary values are provided in both local currency and SGD
    - For MY orders, shipper groups are categorized into XB (Crossborder), LEX, WH (Warehouse), MP (Marketplace), DRTW, DRTM, RTW, RTM

input_tables:
  - core_prod_gl.order_details
  - core_prod_gl.orders
  - core_prod_gl.cods
  - core_prod_gl.delivery_types
  - shipper_prod_gl.marketplace_sellers
  - shipper_prod_gl.shippers
  - 3pl_oms_prod_gl.third_party_vendors
  - 3pl_oms_prod_gl.third_party_orders
  - pricing_prod_gl.pricing_orders

fields:
  - name: order_id
    description: Unique identifier for the order
    source: core_prod_gl.orders.id
    technical_description: "Direct field from source"

  - name: tracking_id
    description: Tracking number assigned to the order
    source: core_prod_gl.orders.tracking_id
    technical_description: "Direct field from source"

  - name: third_party_tracking_id
    description: Tracking ID assigned by third-party delivery partners
    source: 3pl_oms_prod_gl.third_party_orders.third_party_tracking_id
    technical_description: "Direct field from source"

  - name: shipper_reference_number
    description: Reference number provided by the shipper
    source: core_prod_gl.orders.shipper_order_ref_no
    technical_description: "Direct field from source"

  - name: status
    description: Current status of the order
    source: core_prod_gl.orders.status
    technical_description: "Direct field from source"

  - name: granular_status
    description: Detailed status of the order
    source: core_prod_gl.orders.granular_status
    technical_description: "Direct field from source"

  - name: status_enriched
    description: Simplified categorization of order status
    source: core_prod_gl.orders.status, core_prod_gl.orders.granular_status
    technical_description: "Categorized based on granular_status and status fields with the following rules:
      - completed: When granular_status is 'Completed' or 'Transferred to 3PL' AND status is 'Completed'
      - pending: When granular_status is 'Pending Pickup', 'Pending Pickup at Distribution Point', 'Staging', 'Pickup fail', 'Cross Border Transit', 'En-route to Sorting Hub', or 'Van en-route to pickup'
      - returned_to_sender: When granular_status is 'Returned to Sender'
      - cancelled: When granular_status is 'Cancelled'
      - on_hold: When granular_status is 'On Hold'
      - transit: When granular_status is 'Arrived at Sorting Hub', 'On Vehicle for Delivery', 'Pending Reschedule', 'Arrived at Origin Hub', 'Arrived at Distribution Point', or 'Transferred to 3PL' (without Completed status)
      - exception: For any other granular_status values not covered above"

  - name: shipper_id
    description: Unique identifier for the shipper
    source: core_prod_gl.orders.global_shipper_id
    technical_description: "Cast as bigint from orders.global_shipper_id"

  - name: seller_id
    description: Unique identifier for the seller
    source: core_prod_gl.orders.shipper_ref_metadata
    technical_description: "Extracted from JSON path $.shipper.sellerId in shipper_ref_metadata"

  - name: seller_name
    description: Name of the seller
    source: core_prod_gl.orders.shipper_ref_metadata
    technical_description: "Extracted from JSON path $.shipper.sellerName in shipper_ref_metadata"

  - name: lazmall_flag
    description: Indicates if the order is from LazMall
    source: core_prod_gl.orders.shipper_ref_metadata
    technical_description: "Set to 1 if platformTags contains 'lazmall', 0 otherwise"

  - name: lex_flag
    description: Indicates if the order is handled by LEX (Lazada Express)
    source: core_prod_gl.orders.shipper_ref_metadata, shipper_prod_gl.shippers
    technical_description: "Set to 1 for Lazada orders where firstMileTplName is 'LEX ID', 0 for other Lazada orders, null for non-Lazada orders"

  - name: source_id
    description: Identifier for the order source
    source: core_prod_gl.orders.source_id
    technical_description: "Cast as integer from orders.source_id"

  - name: plugin_name
    description: Name of the plugin used for the order
    source: core_prod_gl.order_details.metadata
    technical_description: "Extracted from JSON path $.plugin_name in order_details.metadata"

  - name: cod_id
    description: Cash on Delivery identifier
    source: core_prod_gl.orders.cod_id
    technical_description: "Direct field from source"

  - name: cost
    description: Order cost in local currency
    source: core_prod_gl.orders.cost
    technical_description: "Cast as double from orders.cost"

  - name: cost_sgd
    description: Order cost converted to SGD
    source: core_prod_gl.orders.cost
    technical_description: "Calculated by dividing cost by country-specific exchange rate, rounded to 2 decimal places"

  - name: insurance_value
    description: Insurance value in local currency
    source: core_prod_gl.orders.insurance
    technical_description: "Cast as double from orders.insurance"

  - name: insurance_value_sgd
    description: Insurance value converted to SGD
    source: core_prod_gl.orders.insurance
    technical_description: "Calculated by dividing insurance_value by country-specific exchange rate, rounded to 2 decimal places"

  - name: gst_fee
    description: GST fee in local currency
    source: core_prod_gl.orders.pricing_info
    technical_description: "Extracted from JSON path $.gst in pricing_info"

  - name: gst_fee_sgd
    description: GST fee converted to SGD
    source: core_prod_gl.orders.pricing_info
    technical_description: "Calculated by dividing gst_fee by country-specific exchange rate, rounded to 2 decimal places"

  - name: delivery_fee
    description: Delivery fee in local currency
    source: core_prod_gl.orders.pricing_info
    technical_description: "Extracted from JSON path $.delivery_fee in pricing_info"

  - name: delivery_fee_sgd
    description: Delivery fee converted to SGD
    source: core_prod_gl.orders.pricing_info
    technical_description: "Calculated by dividing delivery_fee by country-specific exchange rate, rounded to 2 decimal places"

  - name: cod_value
    description: Cash on Delivery amount in local currency
    source: core_prod_gl.cods.goods_amount
    technical_description: "Direct field from source"

  - name: cod_value_sgd
    description: Cash on Delivery amount converted to SGD
    source: core_prod_gl.cods.goods_amount
    technical_description: "Calculated by dividing cod_value by country-specific exchange rate, rounded to 2 decimal places"

  - name: ninjapack_flag
    description: Indicates if the order is a Ninjapack order
    source: core_prod_gl.orders.tracking_id
    technical_description: "Set to 1 if tracking_id matches country-specific Ninjapack prefixes, 0 otherwise"

  - name: rts_flag
    description: Return to Sender flag
    source: core_prod_gl.orders.rts
    technical_description: "Cast as bigint from orders.rts"

  - name: parcel_size
    description: Size category of the parcel
    source: core_prod_gl.orders.parcel_size_id
    technical_description: "Mapped from parcel_size_id: 0=s, 1=m, 2=l, 3=xl, 4=xxl, 5=xs"

  - name: nv_width
    description: Width of the parcel
    source: core_prod_gl.orders.dimensions
    technical_description: "Extracted from JSON path $.width in dimensions, cast as double"

  - name: nv_height
    description: Height of the parcel
    source: core_prod_gl.orders.dimensions
    technical_description: "Extracted from JSON path $.height in dimensions, cast as double"

  - name: nv_length
    description: Length of the parcel
    source: core_prod_gl.orders.dimensions
    technical_description: "Extracted from JSON path $.length in dimensions, cast as double"

  - name: nv_weight
    description: Weight of the parcel as recorded by Ninja Van
    source: core_prod_gl.orders.dimensions
    technical_description: "Extracted from JSON path $.weight in dimensions, cast as double"

  - name: weight
    description: Weight of the parcel
    source: core_prod_gl.orders.weight
    technical_description: "Direct field from source"

  - name: original_weight
    description: Original weight of the parcel
    source: core_prod_gl.orders.data
    technical_description: "Extracted from JSON path $.originalWeight in data, cast as double"

  - name: items
    description: Details of items in the order
    source: core_prod_gl.orders.package_content, core_prod_gl.orders.shipper_ref_metadata
    technical_description: "Uses package_content if created_at >= '2022-06-27 05:36:00', otherwise uses $.items from shipper_ref_metadata"

  - name: delivery_instructions
    description: Special instructions for delivery
    source: core_prod_gl.orders.instruction
    technical_description: "Direct field from source"

  - name: from_billing_zone
    description: Billing zone of the pickup location
    source: core_prod_gl.orders.pricing_info
    technical_description: "Coalesced from $.pricing_request.params.from_billing_zone or $.fromBillingZone.billingZone in pricing_info"

  - name: to_billing_zone
    description: Billing zone of the delivery location
    source: core_prod_gl.orders.pricing_info
    technical_description: "Coalesced from $.pricing_request.params.to_billing_zone or $.toBillingZone.billingZone in pricing_info"

  - name: from_l1_id
    description: Billing zone L1 ID of pickup location
    source: core_prod_gl.orders.pricing_info
    technical_description: "Extracted from JSON path $.fromBillingZone.l1_id in pricing_info"

  - name: from_l1_name
    description: Billing zone L1 name of pickup location
    source: core_prod_gl.orders.pricing_info
    technical_description: "Extracted from JSON path $.fromBillingZone.l1_name in pricing_info"

  - name: to_l1_id
    description: Billing zone L1 ID of delivery location
    source: core_prod_gl.orders.pricing_info
    technical_description: "Extracted from JSON path $.toBillingZone.l1_id in pricing_info"

  - name: to_l1_name
    description: Billing zone L1 name of delivery location
    source: core_prod_gl.orders.pricing_info
    technical_description: "Extracted from JSON path $.toBillingZone.l1_name in pricing_info"

  - name: from_l2_id
    description: Billing zone L2 ID of pickup location
    source: core_prod_gl.orders.pricing_info
    technical_description: "Extracted from JSON path $.fromBillingZone.l2_id in pricing_info"

  - name: from_l2_name
    description: Billing zone L2 name of pickup location
    source: core_prod_gl.orders.pricing_info
    technical_description: "Extracted from JSON path $.fromBillingZone.l2_name in pricing_info"

  - name: to_l2_id
    description: Billing zone L2 ID of delivery location
    source: core_prod_gl.orders.pricing_info
    technical_description: "Extracted from JSON path $.toBillingZone.l2_id in pricing_info"

  - name: to_l2_name
    description: Billing zone L2 name of delivery location
    source: core_prod_gl.orders.pricing_info
    technical_description: "Extracted from JSON path $.toBillingZone.l2_name in pricing_info"

  - name: from_l3_id
    description: Billing zone L3 ID of pickup location
    source: core_prod_gl.orders.pricing_info
    technical_description: "Extracted from JSON path $.fromBillingZone.l3_id in pricing_info"

  - name: from_l3_name
    description: Billing zone L3 name of pickup location
    source: core_prod_gl.orders.pricing_info
    technical_description: "Extracted from JSON path $.fromBillingZone.l3_name in pricing_info"

  - name: to_l3_id
    description: Billing zone L3 ID of delivery location
    source: core_prod_gl.orders.pricing_info
    technical_description: "Extracted from JSON path $.toBillingZone.l3_id in pricing_info"

  - name: to_l3_name
    description: Billing zone L3 name of delivery location
    source: core_prod_gl.orders.pricing_info
    technical_description: "Extracted from JSON path $.toBillingZone.l3_name in pricing_info"

  - name: service_type
    description: Type of delivery service
    source: core_prod_gl.orders.service_type
    technical_description: "Direct field from source"

  - name: delivery_type
    description: Type of delivery
    source: core_prod_gl.delivery_types.name
    technical_description: "Joined from delivery_types table using delivery_type_id"

  - name: order_type
    description: Type of order
    source: core_prod_gl.orders.type
    technical_description: "Direct field from source"

  - name: delivery_verification_mode
    description: Mode of delivery verification
    source: core_prod_gl.orders.shipper_ref_metadata
    technical_description: "Extracted from JSON path $.delivery_verification_mode in shipper_ref_metadata"

  - name: from_postcode
    description: Postal code of pickup location
    source: core_prod_gl.orders.from_postcode
    technical_description: "Direct field from source"

  - name: to_postcode
    description: Postal code of delivery location
    source: core_prod_gl.orders.to_postcode
    technical_description: "Direct field from source"

  - name: from_name
    description: Name of sender
    source: core_prod_gl.orders.from_name
    technical_description: "Direct field from source"

  - name: from_address1
    description: Primary address line of pickup location
    source: core_prod_gl.orders.from_address1
    technical_description: "Direct field from source"

  - name: from_address2
    description: Secondary address line of pickup location
    source: core_prod_gl.orders.from_address2
    technical_description: "Direct field from source"

  - name: from_city
    description: City of pickup location
    source: core_prod_gl.orders.from_city
    technical_description: "Direct field from source"

  - name: from_country
    description: Country of pickup location
    source: core_prod_gl.orders.from_country
    technical_description: "Direct field from source"

  - name: to_name
    description: Name of recipient
    source: core_prod_gl.orders.to_name
    technical_description: "Direct field from source"

  - name: to_address1
    description: Primary address line of delivery location
    source: core_prod_gl.orders.to_address1
    technical_description: "Direct field from source"

  - name: to_address2
    description: Secondary address line of delivery location
    source: core_prod_gl.orders.to_address2
    technical_description: "Direct field from source"

  - name: to_city
    description: City of delivery location
    source: core_prod_gl.orders.to_city
    technical_description: "Direct field from source"

  - name: to_country
    description: Country of delivery location
    source: core_prod_gl.orders.to_country
    technical_description: "Direct field from source"

  - name: stamp_id
    description: Stamp identifier
    source: core_prod_gl.orders.stamp_id
    technical_description: "Direct field from source"

  - name: original_to_address1
    description: Original primary address line of delivery location
    source: core_prod_gl.orders.data
    technical_description: "Extracted from JSON path $.previousDeliveryDetails[0].address1 in data"

  - name: original_to_address2
    description: Original secondary address line of delivery location
    source: core_prod_gl.orders.data
    technical_description: "Extracted from JSON path $.previousDeliveryDetails[0].address2 in data"

  - name: original_to_city
    description: Original city of delivery location
    source: core_prod_gl.orders.data
    technical_description: "Extracted from JSON path $.previousDeliveryDetails[0].city in data"

  - name: original_to_country
    description: Original country of delivery location
    source: core_prod_gl.orders.data
    technical_description: "Extracted from JSON path $.previousDeliveryDetails[0].country in data"

  - name: original_to_latitude
    description: Original latitude of delivery location
    source: core_prod_gl.orders.data
    technical_description: "Extracted from JSON path $.previousDeliveryDetails[0].latitude in data"

  - name: original_to_longitude
    description: Original longitude of delivery location
    source: core_prod_gl.orders.data
    technical_description: "Extracted from JSON path $.previousDeliveryDetails[0].longitude in data"

  - name: platform_creation_datetime
    description: Timestamp when the order was created on the marketplace platform
    source: core_prod_gl.orders.shipper_ref_metadata
    technical_description: "Extracted from JSON path $.platformInfo.platformOrderCreationTime in shipper_ref_metadata, converted to local timezone"

  - name: comments
    description: Additional comments on the order
    source: core_prod_gl.orders.comments
    technical_description: "Direct field from source"

  - name: shipper_group_my
    description: Categorization of Malaysian orders by shipper group
    source: core_prod_gl.orders.shipper_ref_metadata, marketplace_sellers.marketplace_id, orders.shipper_id
    technical_description: "Categorized for MY orders into XB (Crossborder), LEX, WH (Warehouse), MP (Marketplace), DRTW, DRTM, RTW, RTM based on tracking_id patterns and metadata"

  - name: billing_weight
    description: Weight used for billing calculations
    source: pricing_prod_gl.pricing_orders.billing_weight
    technical_description: "Cast as double from pricing_orders.billing_weight"

  - name: package_id
    description: Unique identifier for the package
    source: core_prod_gl.orders.shipper_ref_metadata
    technical_description: "Extracted from JSON path $.package_id in shipper_ref_metadata"

  - name: third_party_shipper_name
    description: Name of the third-party delivery partner
    source: 3pl_oms_prod_gl.third_party_vendors.name
    technical_description: "Joined from third_party_vendors using legacy_id and system_id"

  - name: creation_datetime
    description: Timestamp when the order was created
    source: core_prod_gl.orders.created_at
    technical_description: "Converted from orders.created_at to local timezone"

  - name: created_month
    description: Month and year when the order was created
    source: core_prod_gl.orders.created_at
    technical_description: "Formatted as YYYY-MM from orders.created_at" 