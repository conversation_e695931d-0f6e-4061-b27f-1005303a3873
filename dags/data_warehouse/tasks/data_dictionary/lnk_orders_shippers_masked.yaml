# Data dictionary

table_name: lnk_orders_shippers

overview:
  purpose: Links orders to their respective shippers, providing a mapping between order IDs and shipper IDs across different systems
  granularity: Each row represents a unique order-shipper relationship
  business_rules:
    - Only includes orders from Core
    - Orders are partitioned by creation month

input_tables:
  - core_prod_gl.orders

fields:
  - name: order_id
    description: Unique identifier for an order
    source: core_prod_gl.orders.id
    technical_description: "Direct field from source"

  - name: shipper_id
    description: Unique identifier for a shipper
    source: core_prod_gl.orders.global_shipper_id
    technical_description: "Cast from source global_shipper_id as bigint"

  - name: system_id
    description: Identifier for the system where the order originated
    source: "Derived from system configuration"
    technical_description: "String constant representing the system ID (ID, MM, MY, PH, SG, TH, VN)"

  - name: created_month
    description: Month and year when the order was created
    source: core_prod_gl.orders.created_at
    technical_description: "Formatted as YYYY-MM from orders.created_at using date_format function" 