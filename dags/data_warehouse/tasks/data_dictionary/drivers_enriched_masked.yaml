# Data dictionary

table_name: drivers_enriched

overview:
  purpose: A comprehensive table containing enriched driver information including their personal details, employment information, hub assignments, and vendor relationships. This table combines core driver data with related information from multiple sources to provide a complete view of driver profiles.
  granularity: Each row represents a unique driver record with their associated details
  business_rules:
    - Includes both active and deleted drivers
    - For Singapore drivers, driver subtype is extracted from the first name field after the first hyphen
    - Only includes the first active contact details for each driver
    - Only includes the highest ranked preferred zone for each driver
    - Only includes non-deleted MM driver details when joining with landhaul vendors

input_tables:
  - data_warehouse.driver_types_enriched
  - data_warehouse.hubs_enriched
  - driver_prod_gl.drivers
  - driver_prod_gl.driver_zone_preferences
  - driver_prod_gl.driver_contacts
  - addressing_prod_gl.zones
  - hub_prod_gl.mm_driver_details
  - hub_prod_gl.landhaul_vendors

fields:
  - name: country
    description: The country/system identifier for the driver
    source: driver_prod_gl.drivers.system_id
    technical_description: "Direct field from source"

  - name: id
    description: Unique identifier for the driver
    source: driver_prod_gl.drivers.id
    technical_description: "Direct field from source"

  - name: first_name
    description: Driver's first name
    source: driver_prod_gl.drivers.first_name
    technical_description: "Direct field from source"

  - name: last_name
    description: Driver's last name
    source: driver_prod_gl.drivers.last_name
    technical_description: "Direct field from source"

  - name: display_name
    description: Display name of the driver
    source: driver_prod_gl.drivers.display_name
    technical_description: "Direct field from source"

  - name: contact_details
    description: Primary contact information for the driver
    source: driver_prod_gl.driver_contacts
    technical_description: "First active contact details selected by minimum ID where deleted_at is null"

  - name: hub_id
    description: Identifier of the hub the driver is assigned to
    source: driver_prod_gl.drivers.hub_id
    technical_description: "Direct field from source"

  - name: hub_name
    description: Name of the hub the driver is assigned to
    source: data_warehouse.hubs_enriched.name
    technical_description: "Joined from hubs_enriched table using hub_id and system_id"

  - name: hub_region
    description: Region of the hub the driver is assigned to
    source: data_warehouse.hubs_enriched.region
    technical_description: "Joined from hubs_enriched table using hub_id and system_id"

  - name: zone
    description: Name of the driver's preferred zone
    source: addressing_prod_gl.zones.name
    technical_description: "Joined from zones table using the highest ranked zone_id from driver_zone_preferences"

  - name: employment_type
    description: Type of employment for the driver
    source: driver_prod_gl.drivers.employment_type
    technical_description: "Direct field from source"

  - name: driver_type
    description: Type classification of the driver
    source: data_warehouse.driver_types_enriched.driver_type
    technical_description: "Joined from driver_types_enriched using driver_type_id"

  - name: driver_subtype
    description: Subtype classification for Singapore drivers
    source: driver_prod_gl.drivers.first_name
    technical_description: "For Singapore drivers only (system_id = 'sg'), extracted from the text between first and second hyphen in first_name"

  - name: fleet_type
    description: Type of fleet the driver belongs to
    source: data_warehouse.driver_types_enriched.fleet_type
    technical_description: "Joined from driver_types_enriched using driver_type_id"

  - name: scheme_type
    description: Type of scheme the driver operates under
    source: data_warehouse.driver_types_enriched.scheme_type
    technical_description: "Joined from driver_types_enriched using driver_type_id"

  - name: veh_type
    description: Type of vehicle the driver operates
    source: data_warehouse.driver_types_enriched.veh_type
    technical_description: "Joined from driver_types_enriched using driver_type_id"

  - name: employment_start_date
    description: Date when the driver started employment
    source: driver_prod_gl.drivers.employment_start_date
    technical_description: "Converted from UTC timestamp to local timezone date based on system_id"

  - name: employment_end_date
    description: Date when the driver ended employment
    source: driver_prod_gl.drivers.employment_end_date
    technical_description: "Converted from UTC timestamp to local timezone date based on system_id"

  - name: comments
    description: Additional comments or notes about the driver
    source: driver_prod_gl.drivers.comments
    technical_description: "Direct field from source"

  - name: vendor_name
    description: Name of the landhaul vendor associated with the driver
    source: hub_prod_gl.landhaul_vendors.name
    technical_description: "Joined from landhaul_vendors through non-deleted mm_driver_details using vendor_id"

  - name: creation_date
    description: Date when the driver record was created
    source: driver_prod_gl.drivers.created_at
    technical_description: "Converted from UTC timestamp to local timezone date"

  - name: is_deleted
    description: Flag indicating if the driver record has been deleted
    source: driver_prod_gl.drivers.deleted_at
    technical_description: "Derived as 1 if deleted_at is not null, 0 otherwise"

  - name: system_id
    description: System identifier for the driver record
    source: driver_prod_gl.drivers.system_id
    technical_description: "Direct field from source"

  - name: created_month
    description: Month and year when the driver record was created
    source: driver_prod_gl.drivers.created_at
    technical_description: "Formatted as YYYY-MM from created_at timestamp" 