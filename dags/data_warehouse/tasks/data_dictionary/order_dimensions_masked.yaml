# Data dictionary

table_name: order_dimensions

overview:
  purpose: Stores the estimated weight and volume calculations for orders based on various measurement scans and business rules
  granularity: Each row represents dimensional measurements and calculations for a single order
  business_rules:
    - Weight calculations use different divisors based on system_id and shipper_id (6000 for most cases, 5000 for MY standard shippers, 3500 for PH)
    - If actual weight is not available, volume-based weight is used
    - If no measurements are available, default values of 0.1kg for weight and 0.0006 cbm for volume are used
    - Only includes orders with valid scan measurements

input_tables:
  - data_warehouse.dim_weight_scans_base
  - core_prod_gl.orders

fields:
  - name: order_id
    description: Unique identifier for the order
    source: core_prod_gl.orders.id
    technical_description: "Direct field from source"

  - name: estimated_weight
    description: Final calculated weight of the order in kilograms
    source: data_warehouse.dim_weight_scans_base.raw_weight
    technical_description: "COALESCE(latest_weight, vol_weight, 0.1) where vol_weight is calculated based on system rules:
      - MY (shipper_id in (45012, 42206)): weight * 6000 / 1000000
      - MY (other shippers): weight * 5000 / 1000000
      - PH: weight * 3500 / 1000000
      - Others: weight * 6000 / 1000000"

  - name: estimated_volume
    description: Final calculated volume of the order in cubic meters (cbm)
    source: data_warehouse.dim_weight_scans_base.[raw_length, raw_width, raw_height]
    technical_description: "COALESCE(recorded_volume_cbm, weight_vol, 0.0006) where:
      - recorded_volume_cbm = (length * width * height) / 1000000
      - weight_vol follows same system rules as estimated_weight"

  - name: created_month
    description: Month and year when the order was created
    source: core_prod_gl.orders.created_at
    technical_description: "DATE_FORMAT(created_at, 'yyyy-MM')" 