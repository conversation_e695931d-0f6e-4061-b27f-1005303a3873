# Data dictionary

table_name: order_rts_triggers

overview:
  purpose: Tracks the most recent RTS (Return to Sender) trigger event and reason for each order that has been marked as RTS. This helps in understanding why orders were returned and when the RTS decision was made.
  granularity: Each row represents the latest RTS trigger event for a single order
  business_rules:
    - Only includes orders where the RTS flag is set to 1 in the orders table
    - Only captures the most recent RTS trigger event per order (based on event_datetime)
    - RTS reasons can come from either RTS events (type 6) or ticket resolved events (type 44)
    - Orders are partitioned by creation month for efficient data management

input_tables:
  - data_warehouse.rts_trigger_events
  - core_prod_gl.orders

fields:
  - name: order_id
    description: Unique identifier for the order
    source: data_warehouse.rts_trigger_events.order_id
    technical_description: "Direct field from source, joined with orders table"

  - name: event_datetime
    description: Timestamp when the RTS trigger event occurred
    source: data_warehouse.rts_trigger_events.event_datetime
    technical_description: "Direct field from source, filtered to get the latest event per order using ROW_NUMBER()"

  - name: rts_reason
    description: Reason why the order was marked for RTS
    source: data_warehouse.rts_trigger_events.rts_reason, data_warehouse.rts_trigger_events.order_event_type_id
    technical_description: "CASE statement logic: If order_event_type_id = 6 then use rts_reason, if order_event_type_id = 44 then 'MISSING_PETS_TICKET', else NULL"

  - name: created_month
    description: Month and year when the order was created
    source: core_prod_gl.orders.created_at
    technical_description: "Derived from orders.created_at using date_format(created_at, 'yyyy-MM')" 