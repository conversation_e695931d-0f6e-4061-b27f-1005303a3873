from pathlib import Path
import json

from airflow import DAG
from airflow.models import Variable
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator

from common.airflow import notifications as notif
from metadata.constants import Timeout

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")

env = Variable.get("env")

default_args = {"owner": "airflow", "start_date": "2024-01-01", "retries": 1}

# Increased Spark configurations for larger scale
spark_conf = {
    "spark.driver.memory": "4g",
    "spark.executor.memory": "8g",
    "spark.executor.cores": "2",
    "spark.dynamicAllocation.minExecutors": "5",
    "spark.dynamicAllocation.maxExecutors": "10",
}

missing_hwc_tables = Variable.get("missing_hwc_tables", deserialize_json=True, default_var={})
misc_failed_tables = Variable.get("misc_failed_tables", deserialize_json=True, default_var={})

primary_keys = Variable.get("with_other_pk", deserialize_json=True, default_var={})
primary_keys_str = json.dumps(primary_keys)

with DAG(
    dag_id="copy_table_to_mig",
    default_args=default_args,
    schedule_interval=None,
    catchup=False,
    concurrency=100,
    sla_miss_callback=notif.chat.send_dag_run_sla_miss_alert,
    tags=["migration"],
    description="DAG to copy entire table to migration",
) as dag:
    created_months = ["2024-11", "2024-12", "2025-01", "2025-02", "2025-03", "2025-04"]

    # Process missing HWC tables - using full copy since these are missing tables
    for schema, tables in missing_hwc_tables.items():
        for table in tables:
            for month in created_months:
                task = SparkSubmitOperator(
                    task_id=f"copy-full-table-{schema}-{table}-{month}",
                    name=f"copy-full-table-{schema}-{table}-{month}",
                    execution_timeout=Timeout.TWENTY_FOUR_HOURS,
                    application=f"{tasks_path}/deltalake_copy_table_to_mig.py",
                    application_args=[
                        schema,
                        table,
                        month,
                        primary_keys_str,
                        "1",
                    ],  # Copy all rows since these are missing tables
                    conn_id="spark_default",
                    conf=spark_conf,
                )

    # Process misc failed tables with their specific months - using diff computation to identify missing/stale rows
    for schema, table_months in misc_failed_tables.items():
        for table, months in table_months.items():
            for month in months:
                task = SparkSubmitOperator(
                    task_id=f"copy-diff-table-{schema}-{table}-{month}",
                    name=f"copy-diff-table-{schema}-{table}-{month}",
                    execution_timeout=Timeout.TWENTY_FOUR_HOURS,
                    application=f"{tasks_path}/deltalake_copy_table_to_mig.py",
                    application_args=[
                        schema,
                        table,
                        month,
                        primary_keys_str,
                        "0",
                    ],  # Use diff computation to identify what's missing/stale
                    conn_id="spark_default",
                    conf=spark_conf,
                )
