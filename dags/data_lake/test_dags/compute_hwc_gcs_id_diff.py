from pathlib import Path
import json

from airflow import DAG
from airflow.models import Variable
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator

from common.airflow import notifications as notif
from metadata.constants import Timeout

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")

env = Variable.get("env")

default_args = {"owner": "airflow", "start_date": "2025-05-19", "retries": 0}

spark_conf = {
    "spark.driver.memory": "4g",
    "spark.executor.memory": "16g",
    "spark.executor.cores": "2",
    "spark.dynamicAllocation.minExecutors": "2",
    "spark.dynamicAllocation.maxExecutors": "10",
}

deltalake_tables = Variable.get("mysql_tables", deserialize_json=True, default_var={})

primary_keys = Variable.get("with_other_pk", deserialize_json=True, default_var={})
primary_keys_str = json.dumps(primary_keys)

with DAG(
    dag_id="compute_obs_gcs_id_diff",
    default_args=default_args,
    schedule_interval=None,
    catchup=False,
    concurrency=500,
    sla_miss_callback=notif.chat.send_dag_run_sla_miss_alert,
    tags=["hwc-migration"],
) as dag:
    created_months = ["2024-11", "2024-12", "2025-01", "2025-02", "2025-03", "2025-04"]

    # Uncomment after it works for single table
    for schema, tables in deltalake_tables.items():
        for table in tables:
            for month in created_months:
                task = SparkSubmitOperator(
                    task_id=f"obs-gcs-diff-{schema}-{table}-{month}",
                    name=f"obs-gcs-diff-{schema}-{table}-{month}",
                    execution_timeout=Timeout.TWELVE_HOURS,
                    application=f"{tasks_path}/deltalake_obs_hwc_diff.py",
                    application_args=[schema, table, month, primary_keys_str],
                    conn_id="spark_default",
                    conf=spark_conf,
                )
